﻿
User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 1/8/2019 10:55:31 AM",	
Error Message: "The data reader is incompatible with the specified 'Helpdesk_MetsoSICModel.usp_GetProjectDetailsForCheckSheetnew_Result'. A member of the type, 'CheckSheetActivityStatusName', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 1/10/2019 11:07:40 AM",	
Error Message: "The data reader is incompatible with the specified 'Helpdesk_MetsoSICModel.usp_GetProjectDetailsForCheckSheetnew_Result'. A member of the type, 'CheckSheetActivityStatusName', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Settings",	
Controller Name" SettingsController",	
Action Name: " GetPlantTypeDetails",	
Created Date: " 1/10/2019 11:12:35 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Settings",	
Controller Name" SettingsController",	
Action Name: " GetPlantTypeDetails",	
Created Date: " 1/10/2019 11:28:15 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Settings",	
Controller Name" SettingsController",	
Action Name: " GetZeroDateMileStoneDetails",	
Created Date: " 1/10/2019 11:47:50 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Settings",	
Controller Name" SettingsController",	
Action Name: " Insert_InstallationProtocolSpecsDetails",	
Created Date: " 1/10/2019 11:47:53 AM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Settings",	
Controller Name" SettingsController",	
Action Name: " Insert_InstallationProtocolSpecsDetails",	
Created Date: " 1/10/2019 4:12:55 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/10/2019 4:16:31 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/10/2019 6:21:31 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/10/2019 6:21:55 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/10/2019 6:21:58 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/10/2019 6:22:00 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/10/2019 6:22:03 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/10/2019 6:22:05 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 1:39:14 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 1:39:14 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:11:45 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:11:47 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:14:52 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:14:54 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:14 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:15 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:25 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:25 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:29 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:29 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:34 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:34 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:39 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:40 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:47 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:17:47 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/11/2019 3:18:20 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Settings",	
Controller Name" SettingsController",	
Action Name: " Insert_InstallationProtocolSpecsDetails",	
Created Date: " 1/11/2019 3:18:21 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Settings",	
Controller Name" SettingsController",	
Action Name: " Insert_InstallationProtocolSpecsDetails",	
Created Date: " 1/14/2019 12:01:46 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:06:35 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:31:18 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:31:20 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:32:00 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:32:01 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:33:58 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:34:00 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:34:52 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:34:54 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:38:23 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 12:38:24 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:09:37 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:09:37 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:09:56 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:09:57 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:10:26 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:10:26 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:10:42 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:10:42 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:10:55 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetStates",	
Created Date: " 1/14/2019 5:10:55 PM",	
Error Message: "An error occurred while executing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 1/14/2019 5:10:59 PM",	
Error Message: "The data reader is incompatible with the specified 'Helpdesk_MetsoSICModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetRegions",	
Created Date: " 1/17/2019 10:18:43 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetPlanttypes",	
Created Date: " 1/17/2019 10:18:45 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetPlantStageNames",	
Created Date: " 1/17/2019 10:18:45 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectCurrentStatusNames",	
Created Date: " 1/17/2019 10:18:46 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectStatusStatusNames",	
Created Date: " 1/17/2019 10:18:46 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetCurrentStatusStatusNames",	
Created Date: " 1/17/2019 10:18:46 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetZeroMileStoneDropDownValues",	
Created Date: " 1/17/2019 10:18:46 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetSupplyStatusNames",	
Created Date: " 1/17/2019 10:18:46 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 1/17/2019 10:18:46 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 1/17/2019 10:18:47 AM",	
Error Message: "The data reader is incompatible with the specified 'Helpdesk_MetsoSICModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetRegions",	
Created Date: " 1/17/2019 10:19:37 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetPlanttypes",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetPlantStageNames",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectCurrentStatusNames",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectStatusStatusNames",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetCurrentStatusStatusNames",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetZeroMileStoneDropDownValues",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetSupplyStatusNames",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "An error occurred while preparing the command definition. See the inner exception for details.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 1/17/2019 10:19:39 AM",	
Error Message: "Object reference not set to an instance of an object.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 1/30/2019 6:09:11 PM",	
Error Message: "Object reference not set to an instance of an object.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 2/21/2019 11:01:05 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 2/21/2019 11:22:50 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 2/21/2019 11:23:07 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 2/21/2019 11:23:35 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 2/21/2019 11:26:00 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 2/21/2019 11:36:03 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'NTMonthval', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 2/21/2019 11:36:18 AM",	
Error Message: "Object reference not set to an instance of an object.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetOrderTermsNames",	
Created Date: " 3/13/2019 11:40:26 AM",	
Error Message: "Object reference not set to an instance of an object.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 3/14/2019 3:34:50 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:10:09 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:10:27 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:12:24 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:40:41 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:43:18 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:44:30 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:47:19 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 5:47:51 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:12:00 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:12:34 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:24:06 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:26:21 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:27:16 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:31:11 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:31:38 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:37:06 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/25/2019 6:38:49 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/26/2019 11:14:54 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/26/2019 11:22:46 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/26/2019 11:44:32 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/26/2019 11:56:24 AM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/26/2019 12:34:53 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------


User : "Method",	
Controller Name" ProjectController",	
Action Name: " GetProjectDetailsLandingGrid",	
Created Date: " 4/26/2019 12:37:20 PM",	
Error Message: "The data reader is incompatible with the specified 'SupportDesk_MetsoCSModel.USp_GetProjectDetailsFOrWeb_Result'. A member of the type, 'Warranty_wbs', does not have a corresponding column in the data reader with the same name.",	

---------------------------------------------------------------------------------------------

