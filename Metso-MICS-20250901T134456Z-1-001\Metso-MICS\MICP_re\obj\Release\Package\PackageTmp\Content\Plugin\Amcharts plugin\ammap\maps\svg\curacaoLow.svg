<?xml version="1.0" encoding="utf-8"?>
<!-- (c) ammap.com | SVG map of Curacao - Low -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:amcharts="http://amcharts.com/ammap" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
	<defs>
		<style type="text/css">
			.land
			{
				fill: #CCCCCC;
				fill-opacity: 1;
				stroke:white;
				stroke-opacity: 1;
				stroke-width:0.5;
			}
		</style>

		<amcharts:ammap projection="mercator" leftLongitude="-69.1624547" topLatitude="12.3927182" rightLongitude="-68.73046875" bottomLatitude="12.0312619264"></amcharts:ammap>

		<!-- All areas are listed in the line below. You can use this list in your script. -->
		<!--{id:"CW"}-->

	</defs>
	<g>
		<path id="CW" title="Curaçao" class="land" d="M799.38,641.72L722.54,555.97L620.4,415.63L304.3,318.62L0.62,0L12.97,181.18L353.47,504.67L799.38,641.72z"/>
	</g>
</svg>
