﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>CheckSheets</title>

       <style>    
        @@media only screen and (min-width: 1280px) 
              {
            #IdDivModelCheckSheet {
                height:500px;
            }
        } 
              
              @@media only screen and (max-width: 1275px) {
            #IdDivModelCheckSheet {
                height:500px;
            }
        } 
    </style>

</head>
<body>
    <div style="width: inherit; overflow: auto" id="DivLandingGrid">
    <table id="projectTblGrid"></table>
    <div id="projectDivPager"></div>
</div>
 @*//=======================================================================================================//*@
   

    @*//=========================================================================================================//*@

   
</body>
</html>
   <script>
       $("#IdTxtCivilProvided").datepicker();

       function NewButton() {
           $("#IdToAppend").html("");
           $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
           $("#IdToAppend").append("<Button id='IdBtnNewCheckSheet' class='AddNew'>New</Button>")
       }
       NewButton();
 
   
    $.ProjectLandingGrid = function () {
        debugger
        var LoadStaticDataMainGrid = [
            { "ActivityHeaderIC": "1", "ProjectCode": "C.005867 & C.005869", "Client": "Mr. K K Singh", "State": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "NW-Rapid", "ProjectStatus": "Completed", "ChecksheetStatus": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
            { "ActivityHeaderIC": "2", "ProjectCode": "C.005868 & C.005900", "Client": "Mr. Praveen", "State": "Karnataka", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "Modular", "ProjectStatus": "InProgress", "ChecksheetStatus": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
            { "ActivityHeaderIC": "3", "ProjectCode": "C.005869 & C.005901", "Client": "Mr. Suresh", "State": "Tamilnadu", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "Modular", "ProjectStatus": "Completed", "ChecksheetStatus": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
            { "ActivityHeaderIC": "4", "ProjectCode": "C.005870 & C.005902", "Client": "Mr. Mahesh", "State": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "NW-Rapid", "ProjectStatus": "InProgress", "ChecksheetStatus": "Completed", "Created Date": "11-09-2018", "CompletedDate": "18-09-2018" }];
        //  $("#DivLandingGrid").GridUnload();
        $("#DivLandingGrid").html("");

        $("#DivLandingGrid").append("<table id='projectTblGrid'></table>")
        $("#DivLandingGrid").append("<div id='projectDivPager'></div>")
        $("#projectTblGrid").GridUnload();
        $("#projectTblGrid").jqGrid({

            
            colModel: [

                    {
                        name: "View", align: "center", width: 80, resizable: true, sortable: false, search: false, searchable: false,hidden: true , formatter: function (a, b) {
                            return "<i class='glyphicon glyphicon-file editclass'  title='Click Here For View' onClick='EditCheckSheetDetails(" + b.rowId + ")' ></i>"
                        }
                    },
         { name: "ProjectDetailsId", label: "ProjectDetailsId", width: "100", align: "left", resizable: true, sortable: true, search: true, hidden: true },
         { name: "Project_Code", label: "Project Code", width: "125", align: "left", resizable: true, sortable: true, search: true, search: true, searchable: true },
         { name: "Site_Client_Name", label: "Client", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true },
         { name: "State_Name", label: "State", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true },
         { name: "ItemTypeName", label: "Item Type", width: "140", align: "left", resizable: true, sortable: true, search: true, searchable: true },
         { name: "SIC_Models_Name", label: "Equipment", width: "140", align: "left", resizable: true, sortable: true, search: true, searchable: true },
         { name: "ProtocolStageStatusName", label: "Status", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false },
         { name: "Company_Employee_Name", label: "Employee Name", width: "140", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false,},
            ],
            height: 'auto',
            width: 990,
            caption: "Protocol Rejection",
            url: AbsolutePath("/CheckSheet/ProjectInjfoForProtocolRejection?pid=0"),
            datatype: "json",
            mtype: "GET",
            sortname: "ProjectDetailsid",
            sortorder: "asc",
            //  sortable:true,
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            loadonce: true,
            rowNum: 20,
            rownumbers: true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: "#projectDivPager",
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                console.log(data)
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: true,
            },
        })
        $("#projectTblGrid").navGrid("#projectDivPager", { add: false, edit: false, del: false, refresh: false, search: false })
        $("#projectTblGrid").filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
        var $grid = $('#projectTblGrid');
       // $grid.jqGrid('setCaption', 'Check Sheet');
        $("#projectTblGrid").navGrid("#projectDivPager", {}, {}, {}, {}, {
            multipleSearch: true,
            multipleGroup: true,
            showQuery: true
        });
        $("#projectTblGrid").navButtonAdd("#projectDivPager",
           {
               title: "Refresh",
               buttonicon: "ui-icon-refresh",
               caption: "",
               position: "last",
               onClickButton: function () {
                   debugger
                   $("#projectTblGrid").jqGrid("GridUnload");
                   $.ProjectLandingGrid(projectid);
                   // $("#projectTblGrid").trigger('reloadGrid')
               }
           });
        $("#projectTblGrid").navButtonAdd("#projectDivPager",
          {
              title: "Export",
              buttonicon: "ui-icon-bookmark",
              caption: "",
              position: "last",
              onClickButton: function () {
                  //$("#projectTblGrid").jqGrid("GridUnload");
                  //$.ProjectLandingGrid();
                  window.location.href = AbsolutePath("/CheckSheet/ExportToexcelProtocolrejection");
              }
          });

    }

    $.ProjectLandingGrid();

        //===============================================================================================//
       



    </script>

