﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>CheckSheets</title>

       <style>    
        @@media only screen and (min-width: 1280px) 
              {
            #IdDivModelCheckSheet {
                height:500px;
            }
        } 
              
              @@media only screen and (max-width: 1275px) {
            #IdDivModelCheckSheet {
                height:500px;
            }
        } 
    </style>

</head>
<body>
    <div style="width: inherit; overflow: auto" id="DivLandingGrid">
    <table id="projectTblGrid"></table>
    <div id="projectDivPager"></div>
</div>
 @*//=======================================================================================================//*@
     <div class="modal fade ClsDivModelView" id="IdDivModelCheckSheet" role="dialog" style="position: absolute !important; height: 800px;overflow-y: scroll;">
                    <div class="modal-dialog modal-lg" style="width: 97%">
                        <!-- Modal content-->
                        <div class="modal-content">

                            <div class="modal-header">
                                <button type="button" class="close closeCheckSheet" id="btnCLoseX">&times;</button>
                    <button type="button" class="Colsemodal" style="display: none" data-dismiss="modal"></button>
                                <h4 class="modal-title">Progress Sheet Details</h4>


                            </div>
                            <div id="IDDivCustModalBody" class="modal-body">
                                <div id="IdDivCheckSheetForm">
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdProjectCode" class="ClsLabelCustTxtBox ">Project Code</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                                <input id="IdProjectCode" type="text" class="form-control input-sm ClsTxtQuotaionHeader titlevalue" readonly="readonly" />
                                        </div>
                                         <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdProjectNamenew" class="ClsLabelCustTxtBox ">Project Name</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                                <input id="IdProjectNamenew" type="text" class="form-control input-sm ClsTxtQuotaionHeader titlevalue" readonly="readonly" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdProjectName" readonly="readonly" class="ClsLabelCustTxtBox ">Plant type </label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdProjectName" readonly="readonly" type="text" class="form-control input-sm ClsCheckSheetDetails titlevalue" readonly="readonly" />
                                        </div>
                                       
                                    </div>
                                    <br />
                                    <div class="row">
                                         <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdClient" class="ClsLabelCustTxtBox ">Client</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdClient" type="text" readonly="readonly" value="" class="form-control input-sm ClsCheckSheetDetails titlevalue" readonly="readonly" />

                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtState" class="ClsLabelCustTxtBox ">State</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtState"readonly="readonly" type="text" class="form-control input-sm ClsCheckSheetDetails titlevalue" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtlocation" class="ClsLabelCustTxtBox ">Location</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtlocation" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader titlevalue" />
                                        </div>
                                        
                                    </div>

                                </div>

                                <br />
                               @* <div class="row">
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"  style="background-color: #8bcaca; color:#1f716c; width: 97%; margin-left: 13px;font-weight:bolder">
                                        Cheak Sheet Details
                                    </div>
                                </div>*@
                                
                                   <div class="row">
                                       <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectStatus" class="ClsLabelCustTxtBox ">Project Status</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtProjectStatus" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader titlevalue" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtCheckSheetStatus" class="ClsLabelCustTxtBox ">Progress Sheet Status</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtCheckSheetStatus" readonly="readonly" type="text" class="form-control input-sm ClsCheckSheetDetails titlevalue" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtInstallationEngineer" class="ClsLabelCustTxtBox ">Installation Engineer</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtInstallationEngineer" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader titlevalue" />
                                        </div>
                                        
                                    </div>
                                <br />
                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtSubmittedDate" class="ClsLabelCustTxtBox ">Submitted Date</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtSubmittedDate" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader titlevalue" />
                                        </div>
                                </div>
                                <br />
                                <div class="row">
                                    @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>*@
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                        <div style="width: inherit; overflow: auto;" id="IdDivGridCheckSheet">
                                            <table id='IdTblCheckSheetGrid'></table>
                                            <div id='CheckSheetDetailsPager'></div>
                                        </div>
                                    </div>
                                </div>
                                @* ---Main Cheak Sheet--- *@
                                @*<div id="IDCheakSheetAccordian">

               
                    <div class="panel-primary" id="div_ProjectAccoridn">
                        <div class="panel panel-heading clickable" data-toggle="collapse" style='height: 30px; background-color: #006666;' data-parent="#accordion" data-target="#IDProjectDetails">
                            <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: 500; color: white; margin-top: -4px;">Cheak Sheet Details</h4>
                            <span class="pull-right"><i class="glyphicon glyphicon-chevron-down"></i></span>
                        </div>

                        <div style="width: inherit; overflow: auto;" id="IdDivGridCheckSheet">
                            <table id='IdTblCheckSheetGrid'></table>
                            <div id='CheckSheetDetailsPager'></div>
                        </div>
                    </div>
                </div>*@
                            </div>
                            <div class="modal-footer">
                                <button type="button" id="btnExport" class="btn ButtonStyle ">Export</button>
                               @* <button type="button" id="IdBtnCheckSheetSave" class="btn ButtonStyle ClsEnquiryFrombtn">Save</button>
                                <button type="button" id="IdBtnCheckSheetReset" class="btn ButtonStyle ClsEnquiryFrombtn">Reset</button>*@
                                <button type="button" id="IdBtnCheckSheetCancel" class="btn ButtonStyle ClsEnquiryFrombtn closeCheckSheet">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

    @*//=========================================================================================================//*@

    <div class="modal fade ClsDivModelView" id="IdDivModelCheckSheetDetail" role="dialog" style="position: absolute !important;">
                    <div class="modal-dialog modal-lg" style="width: 97%">
                        <!-- Modal content-->
                        <div class="modal-content">

                            <div class="modal-header">
                                <button type="button" class="close" id="IdbtnCloseArrow">&times;</button>
                                <h4 class="modal-title">Cheak Sheet Details</h4>


                            </div>
                            <div id="IDDivSheetModalBody" class="modal-body">
                                <div id="IdDivCheckSheetDetailForm">
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtEnquiryNum" class="ClsLabelCustTxtBox ">Description</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <div class="input-group">
                                                <input id="IdTxtDescription" type="text" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" autofocus="autofocus" />

                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtEnquiryDate" class="ClsLabelCustTxtBox ">Civil Provided</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtCivilProvided" type="text" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="TxtCreatedby" class="ClsLabelCustTxtBox ">Supply Status</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectSupplyStatus" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>

                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtCreatedDate" class="ClsLabelCustTxtBox ">Ground Work</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectGroundWork" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>

                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtQuotationNo" class="ClsLabelCustTxtBox ">Erection</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectErection" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtOfferNo" class="ClsLabelCustTxtBox ">Electrical Work</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectElectricalWork" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtCreatedDate" class="ClsLabelCustTxtBox ">Constraint</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectConstraint" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>

                                        </div>

                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtQuotationNo" class="ClsLabelCustTxtBox ">Remarks</label>
                                        </div>
                                        <div class="col-sm-10">
                                            <textarea class="form-control " id="IdTxtEnqRemarks"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <br />


                            </div>
                            <div class="modal-footer">
                               @* <button type="button" id="txtSaveEnqDetails" class="btn ButtonStyle ClsEnquiryFrombtn">Save</button>
                                <button type="button" id="btnGenerateQtnDetails" class="btn ButtonStyle ClsEnquiryFrombtn" data-dismiss="modal">Reset</button>*@
                                
                                <button type="button" id="btnCLoseEnqForm" class="btn ButtonStyle ClsEnquiryFrombtn">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
</body>
</html>
   <script>     
       $("#IdTxtCivilProvided").datepicker();

       if ('@ViewBag.CheckSheetsProjectCode' == '0')
       {
          
        $("#IdToAppend").html("");        
    //function NewButton() {
    //        $("#IdToAppend").html("");
    //        $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
    //           //$("#IdToAppend").append("<Button id='IdBtnNewCheckSheet' class='AddNew'>New</Button>")
    //    }
    //    NewButton();
      
        $(document).on('click', '#IdBtnNewCheckSheet', function () {
            $("#IdDivModelCheckSheet").modal({ backdrop: false });
        });
           newbuttonValue = 1;          
       }

       if ('@ViewBag.CheckSheetsProjectCode' != '0')
       {
           newbuttonValue = 0;
       }

       function LeftMenuInactive() {
           for (var i = 0; i < $("#IdLeftMenu li").length; i++) {
               $($("#IdLeftMenu li ")[i]).removeClass("menuActive");
           }
       }
       var projectid = 0
       $(".titlevalue ").mouseenter(function () {
           var word = $(this).val();

           $(".titlevalue ").attr('title', word);
       });
        //============================================= Check Sheet ==========================================//

        function CheckSheetLeftMenu() {
            $("#IdToAppend").html("");
            $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
            $("#IdToAppend").append("<ul id='IdLeftMenu'></ul>")
            $("#IdLeftMenu").css("margin-left", "-30px");
            $("#IdLeftMenu").append("<li id='IdCSCheckSheet'>Progress Sheet</li>")
            $("#IdLeftMenu").append("<li id='IdCSProject'>Project</li>")
            $("#IdLeftMenu").append("<li id='IdCSHydraAvailability'>Hydra Availability</li>")
            $("#IdLeftMenu").append("<li id='IdCSIdleReport'>Idle Report</li>")
            $("#IdLeftMenu").append("<li id='IdCSCommissioning'>Commissioning</li>")
            $("#IdLeftMenu").append("<li id='IdCSInstallationProtocalls'>Installation Protocols</li>")
           // $("#IdLeftMenu").append("<li id='IdCSShortSupplies'>Short Supplies</li>")

        }

        $(document).on('click', '#IdCSCheckSheet', function () {
            $("#IdDivModelViewCust").modal("hide");
            LeftMenuInactive()
            $(this).addClass("menuActive")
            $(".MainContainerBody").html("");
            $(".MainContainerBody").load(AbsolutePath("/Home/CheckSheets?ProjectId=" + 0));
            newbuttonValue = 1;
        })

        $(document).on('click', '#IdCSProject', function () {
            $("#IdDivModelViewCust").modal("hide");
            LeftMenuInactive()
            $(this).addClass("menuActive")
            $(".MainContainerBody").html("");
            $(".MainContainerBody").load(AbsolutePath("/Home/Projects?ProjectId=" + projectid));
        })

        $(document).on('click', '#IdCSHydraAvailability', function () {
            $("#IdDivModelViewCust").modal("hide");
            LeftMenuInactive()
            $(this).addClass("menuActive")
            $(".MainContainerBody").html("");
            $(".MainContainerBody").load(AbsolutePath("/Home/HydraAvailability?ProjectId=" + projectid));
        })

        $(document).on('click', '#IdCSIdleReport', function () {
            $("#IdDivModelViewCust").modal("hide");
            LeftMenuInactive()
            $(this).addClass("menuActive")
            $(".MainContainerBody").html("");
            $(".MainContainerBody").load(AbsolutePath("/Home/IdleReport?ProjectId=" + projectid));
        })

        $(document).on('click', '#IdCSCommissioning', function () {
            $("#IdDivModelViewCust").modal("hide");
            LeftMenuInactive()
            $(this).addClass("menuActive")
            $(".MainContainerBody").html("");
            $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + projectid));
        })

        $(document).on('click', '#IdCSInstallationProtocalls', function () {
            $("#IdDivModelViewCust").modal("hide");
            LeftMenuInactive()
            $(this).addClass("menuActive")
            $(".MainContainerBody").html("");
            $(".MainContainerBody").load(AbsolutePath("/Home/InstallationProtocalls?ProjectId=" + projectid));
        })
   
        //$(document).on('click', '#IdIdleReportShortSupplies', function () {
        //    $("#IdDivModelViewCust").modal("hide");
        //    LeftMenuInactive()
        //    $(this).addClass("menuActive")
        //    $(".MainContainerBody").html("");
        //    $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + 1));
        //})



      //==========================================================================================================//
        function EditCheckSheetDetails(Id) {
            console.log(Id)
        if ('@ViewBag.CheckSheetsProjectCode' == '0') {
            CheckSheetLeftMenu()
        }
        CheckSheetLeftMenu();
        debugger
        //var rowdata = $("#projectTblGrid").getRowData(Id);
            //var ProjectId = rowdata.ProjectDetailsid;

        //alert(ProjectId)
        };


       $(document).on('click', '#btnExport', function () {

           window.location.href = AbsolutePath("/CheckSheet/ExportToexcelCheckSheetDetails?ProjectId=" + projectid)
       })

       $(document).on('click', ".editclass", function () {
           debugger
          // $(this).parent().next().next().css("border","solid red 2px")
         
           var PDID = $(this).parent().next().text()
           projectid = PDID

           $("#IdDivModelCheckSheet").modal({ backdrop: false });

           var ProjectCode = $(this).parent().next().next().text()
           var ProjectName = $(this).parent().next().next().next().next().text()
           var Client = $(this).parent().next().next().next().text()
           var State = $(this).parent().next().next().next().next().text()
           var Location = $(this).parent().next().next().next().next().next().text()
           var ProjectType = $(this).parent().next().next().next().next().next().next().text()

           var Status = $(this).parent().next().next().next().next().next().next().next().text()
           var Created = $(this).parent().next().next().next().next().next().next().next().next().text()
           var Completed = $(this).parent().next().next().next().next().next().next().next().next().next().text()
           var Activity = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().text()
           var Employee = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().text()
           var ProjectName = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().next().text()
           
           $("#IdProjectCode").val(ProjectCode)
           $("#IdProjectName").val(ProjectType)
           $("#IdClient").val(Client)
           $("#IdTxtState").val(State)
           $("#IdTxtlocation").val(Location)
           $("#IdTxtProjectStatus").val(Status)
           $("#IdTxtCheckSheetStatus").val(Activity)
           $("#IdTxtInstallationEngineer").val(Employee)
           $("#IdTxtSubmittedDate").val(Completed)
           $("#IdProjectNamenew").val(ProjectName)
           $("#IdTxtNotAvailable").val(5)
           $.CheckSheetLandingGrid(PDID);
       })


    function ViewCheckSheetDetails(Id) {
        if ('@ViewBag.CheckSheetsProjectCode' == '0')
        {
            CheckSheetLeftMenu()            
        }
            $("#IdDivModelCheckSheet").modal({ backdrop: false });
          
    }

  
       //=====================================================================================//
        $.ProjectLandingGrid = function (projectid) {
            debugger
            var LoadStaticDataMainGrid = [
                { "ActivityHeaderIC": "1", "ProjectCode": "C.005867 & C.005869", "Client": "Mr. K K Singh", "State": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "NW-Rapid", "ProjectStatus": "Completed", "ChecksheetStatus": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
                { "ActivityHeaderIC": "2", "ProjectCode": "C.005868 & C.005900", "Client": "Mr. Praveen", "State": "Karnataka", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "Modular", "ProjectStatus": "InProgress", "ChecksheetStatus": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
                { "ActivityHeaderIC": "3", "ProjectCode": "C.005869 & C.005901", "Client": "Mr. Suresh", "State": "Tamilnadu", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "Modular", "ProjectStatus": "Completed", "ChecksheetStatus": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
                { "ActivityHeaderIC": "4", "ProjectCode": "C.005870 & C.005902", "Client": "Mr. Mahesh", "State": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "NW-Rapid", "ProjectStatus": "InProgress", "ChecksheetStatus": "Completed", "Created Date": "11-09-2018", "CompletedDate": "18-09-2018" }];
          //  $("#DivLandingGrid").GridUnload();
            $("#DivLandingGrid").html("");

            $("#DivLandingGrid").append("<table id='projectTblGrid'></table>")
            $("#DivLandingGrid").append("<div id='projectDivPager'></div>")
            $("#projectTblGrid").GridUnload();
            $("#projectTblGrid").jqGrid({

                //datatype: "local",
                //data: LoadStaticDataMainGrid,

                //colModel: [
                //        //{
                //        //    name: "Edit", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                //        //        return "<i class='glyphicon glyphicon-pencil'  title='Click Here For Edit'  onclick='EditCheckSheetDetails(" + b.rowId + ")' id='IdProject_EditImg" + b.rowId + "'></span>";
                //        //    }
                //        //},
                //        {
                //            name: "View", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                //                return "<i class='glyphicon glyphicon-file' title='Click Here For View' data-toggle='modal' data-target='#IdEnquiryModal' onClick='ViewCheckSheetDetails(" + b.rowId + ")' ></i>"
                //            }
                //        },

                //         { name: "ActivityHeaderIC", index: "ActivityHeaderIC", label: "ActivityHeaderIC", editable: true, align: "left", hidden: true },

                //              { name: "ProjectCode", index: "ProjectCode", label: "ProjectCode", editable: true, align: "left", hidden: false },
                //              {
                //                  name: "Client", index: "Client", label: "Client", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true,
                //              },
                //              { name: "State", index: "State", label: "State", editable: true, align: "left", width: 110, resizable: true, sortable: true, search: true, searchable: true },
                //              { name: "Location", index: "Location", label: "Location", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true, },
                //              { name: "ProjectType", index: "ProjectType", label: "ProjectType", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true },
                //              { name: "ProjectStatus", index: "Status", label: "Project Status", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true, hidden: false },
                //               { name: "ChecksheetStatus", index: "Status", label: "CheckSheet Status", editable: true, align: "left", width: 120, resizable: true, sortable: true, search: true, searchable: true, hidden: false },
                //              { name: "CreatedDate", index: "CreatedDate", label: "CreatedDate", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true },
                //              { name: "CompletedDate", index: "CompletedDate", label: "CompletedDate", editable: true, align: "left", width: 120, resizable: true, sortable: true, search: true, searchable: true },

                //],
                colModel: [
                        //{
             //    name: 'Edit', width: 60, align: 'center', formatter: function view(a, b) {
             //        return "<span class='glyphicon glyphicon-pencil ClsViewEnquiryDetails' id='editImg_" + b.rowId + "'></span>";
             //    }, search: false,
                        //},

                        {
                 name: "View", align: "center", width: 80, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                     return "<i class='glyphicon glyphicon-file editclass'  title='Click Here For View' onClick='EditCheckSheetDetails(" + b.rowId + ")' ></i>"
                            }
                        },
             { name: "ProjectDetailsid", label: "ProjectDetailsId", width: "100", align: "left", resizable: true, sortable: true, search: true,hidden:true},
             { name: "Project_Code",  label: "Project Code", width: "125", align: "left", resizable: true, sortable: true, search: true, search: true, searchable: true },
             { name: "Site_Client_Name",  label: "Client", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true },
             { name: "State_Name", label: "State", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
             { name: "Location",  label: "Location", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
             { name: "Plant_typeName",  label: "Project Type", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
             { name: "ProjectStatusName",  label: "Status", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false },
             { name: "Created_Date",  label: "Created Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
             { name: "CheckSheet_SubmittedDate",  label: "Completed Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
              { name: "Project_Code",  label: "Project Code", width: "110", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
              { name: "CheckSheetStatusName", label: "Activity", width: "110", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
              { name: "Company_Employee_Name", label: "Employee", width: "110", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
              { name: "ProjectName", label: "Project_Name", width: "110", align: "left", resizable: true, sortable: true, search: true, searchable: true , hidden: true},
                ],
                height: 'auto',
                width: 1002,
                caption: "CheckSheet",
                url: AbsolutePath("/CheckSheet/ProjectInjfoForCheekSheet?pid=" + projectid),
                datatype: "json",
                mtype: "GET",
                sortname: "ProjectDetailsid",
                sortorder: "asc",
             //  sortable:true,
                viewrecords: true,
                selrow: true,
                shrinkToFit: false,
               loadonce: true,
                rowNum: 20,
                rownumbers: true,
                rowList: [20, 40, 60, 80, 100, 120],
                pager: "#projectDivPager",
                loadComplete: function (data) {
                    $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                    console.log(data)
                },
                resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
                jsonReader: {
                    root: "root",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: true,
                },
            })
            $("#projectTblGrid").navGrid("#projectDivPager", { add: false, edit: false, del: false, refresh: false, search: false })
            $("#projectTblGrid").filterToolbar({
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
            var $grid = $('#projectTblGrid');
            $grid.jqGrid('setCaption', 'Progress Sheet');
            $("#projectTblGrid").navGrid("#projectDivPager", {}, {}, {}, {}, {
                multipleSearch: true,
                multipleGroup: true,
                showQuery: true
            });
            $("#projectTblGrid").navButtonAdd("#projectDivPager",
               {
                   title: "Refresh",
                   buttonicon: "ui-icon-refresh",
                   caption: "",
                   position: "last",
                   onClickButton: function () {
                       debugger
                      $("#projectTblGrid").jqGrid("GridUnload");
                      $.ProjectLandingGrid(projectid);
                      // $("#projectTblGrid").trigger('reloadGrid')
                   }
               });
            $("#projectTblGrid").navButtonAdd("#projectDivPager",
              {
                  title: "Export",
                  buttonicon: "ui-icon-bookmark",
                  caption: "",
                  position: "last",
                  onClickButton: function () {
                      //$("#projectTblGrid").jqGrid("GridUnload");
                      //$.ProjectLandingGrid();
                      window.location.href = AbsolutePath("/CheckSheet/ExportToexcel");
                  }
              });

        }

        $.ProjectLandingGrid('@ViewBag.CheckSheetsProjectCode');
       
       //===============================================================================================//
       $.CheckSheetLandingGrid = function (pid)
       {
            var CheakSheetStaticData = [
             { "Description": "Primary Crusher-NW106", "CivilProvided": "30/08/2018", "SupplyStatus": "In-Complete", "GroundWorkStatus": "Complete", "GroundWorkSD": "21/10/18", "GroundWorkED": "23/10/18", "ErectionStatus": "Complete", "ErectionSD": "21/10/18", "ErectionED": "23/10/18", "ElectricalWorkStatus": "Complete", "ElectricalWorkSD": "21/10/18", "ElectricalWorkED": "23/10/18", "NoLoadTrialStatus": "Short Supply", "NoLoadTrialSD": "21/10/18", "NoLoadTrialED": "23/10/18", "Constraint": "Short Supply", "Remarks": "Pulley Fitment is balanced due to incomplete supply" },
               { "Description": "Secondary Crusher-NW220GPD", "CivilProvided": "30/08/2018", "SupplyStatus": "In-Complete", "GroundWorkStatus": "Complete", "GroundWorkSD": "21/10/18", "GroundWorkED": "23/10/18", "ErectionStatus": "Complete", "ErectionSD": "21/10/18", "ErectionED": "23/10/18", "ElectricalWorkStatus": "Complete", "ElectricalWorkSD": "21/10/18", "ElectricalWorkED": "23/10/18", "NoLoadTrialStatus": "Short Supply", "NoLoadTrialSD": "21/10/18", "NoLoadTrialED": "23/10/18", "Constraint": "Short Supply", "Remarks": "Pulley Fitment is balanced due to incomplete supply" },
                 { "Description": "Dumper Hopper NW106", "CivilProvided": "30/08/2018", "SupplyStatus": "In-Complete", "GroundWorkStatus": "Complete", "GroundWorkSD": "21/10/18", "GroundWorkED": "23/10/18", "ErectionStatus": "Complete", "ErectionSD": "21/10/18", "ErectionED": "23/10/18", "ElectricalWorkStatus": "Complete", "ElectricalWorkSD": "21/10/18", "ElectricalWorkED": "23/10/18", "NoLoadTrialStatus": "Short Supply", "NoLoadTrialSD": "21/10/18", "NoLoadTrialED": "23/10/18", "Constraint": "Short Supply", "Remarks": "Pulley Fitment is balanced due to incomplete supply" },
                   { "Description": "GSB Chute with fasteners", "CivilProvided": "30/08/2018", "SupplyStatus": "In-Complete", "GroundWorkStatus": "Complete", "GroundWorkSD": "21/10/18", "GroundWorkED": "23/10/18", "ErectionStatus": "Complete", "ErectionSD": "21/10/18", "ErectionED": "23/10/18", "ElectricalWorkStatus": "Complete", "ElectricalWorkSD": "21/10/18", "ElectricalWorkED": "23/10/18", "NoLoadTrialStatus": "Short Supply", "NoLoadTrialSD": "21/10/18", "NoLoadTrialED": "23/10/18", "Constraint": "Short Supply", "Remarks": "Pulley Fitment is balanced due to incomplete supply" },
            ]

            $("#IdDivGridCheckSheet").html("");

            $("#IdDivGridCheckSheet").append("<table id='IdTblCheckSheetGrid'></table>")
            $("#IdDivGridCheckSheet").append("<div id='CheckSheetDetailsPager'></div>")


            $("#IdTblCheckSheetGrid").jqGrid({
                //datatype: "local",
                //mtype: "GET",
                //data: CheakSheetStaticData,
                url: AbsolutePath("/CheckSheet/ChecksheetDetails?ProjectID=" + pid),
                datatype: "json",
                mtype: "GET",
                sortname: "CheckSheetDetailsId",
                sortorder: "desc",
                colModel: [
                        //{
                        //    name: "Edit", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                        //        return "<i class='glyphicon glyphicon-pencil'  title='Click Here For Edit'  onclick='$.EditEnquiry(" + b.rowId + ")'>"
                        //    }
                        //},
                        //{
                        //    name: "View", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                        //        return "<i class='glyphicon glyphicon-file' title='Click Here For View' data-toggle='modal' data-target='#IdEnquiryModal' onclick='$.EnquiryView(" + b.rowId + ")'>"
                        //    }
                        //},
                          { name: "CheckSheetDetailsId",  label: "Status", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                              { name: "Description",  label: "Description", editable: true, align: "left", hidden: false },
                              {
                                  name: "Civil_Provided",  label: "Civil Provided", editable: true, align: "left", width: 85, resizable: true, sortable: true, search: true, searchable: true,
                              },
                              { name: "CheckSheetSupplyStatus",  label: "Supply Status", editable: true, align: "left", width: 110, resizable: true, sortable: true, search: true, searchable: true },
                                { name: "Groundworkstatus", label: "Ground Assembly Status", editable: true, align: "left", width: 110, resizable: true, sortable: true, search: true, searchable: true },
                        
                              { name: "Ground_work_StartDate",  label: "Start Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true,},
                              { name: "Ground_Work_EndDate",  label: "End Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true,},

                             { name: "LiftingPlacingstatus", label: "Lift Placing Status", editable: true, align: "left", width: 110, resizable: true, sortable: true, search: true, searchable: true },
                              { name: "LiftingPlacing_StartDate",  label: "Start Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true,},
                              { name: "LiftingPlacing_StartDate",  label: "End Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true,},

                              
                              { name: "electricalworkstatus",  label: "ElectricalWork Status", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true,  },
                              { name: "Electrical_Work_StartDate",  label: "Start Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true, },
                              { name: "Electrical_Work_EndDate",  label: "End Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true,},
                              { name: "NoLoadstatus", label: "NoLoad Status", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true,},

                              { name: "NoLoadTrail_StartDate",  label: "Start Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true,},
                              { name: "NoLoadTrail_EndDate", label: "End Date", editable: true, align: "left", width: 72, resizable: true, sortable: true, search: true, searchable: true, hidden: false,},
                             { name: "CheckSheetConstraintsName",  label: "Constraint", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true, },
                              { name: "Remarks",  label: "Remarks", editable: true, align: "left", width: 120, resizable: true, sortable: true, search: true, searchable: true },

                ],
                height: 240,
                width: 1680,
               // caption: "CheckSheet",
                viewrecords: true,
                selrow: true,
                shrinkToFit: false,
                loadonce: true,
                rownumbers: true,
                rowNum: 20,
                rowList: [20, 40, 60, 80, 100, 120],
                pager: "#CheckSheetDetailsPager",
                loadComplete: function () {
                    $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                },
                resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
                jsonReader: {
                    root: "root",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            })
            $("#IdTblCheckSheetGrid").navGrid("#CheckSheetDetailsPager", { add: false, edit: false, del: false, refresh: false, search: false })
            $("#IdTblCheckSheetGrid").filterToolbar({
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
            $("#IdTblCheckSheetGrid").navGrid("#CheckSheetDetailsPager", {}, {}, {}, {}, {
                multipleSearch: true,
                multipleGroup: true,
                showQuery: true
            });
            //var $grid = $('#IdTblCheckSheetGrid');
            //$grid.jqGrid('setCaption', 'Check Sheet');
            //$("#IdTblCheckSheetGrid").navButtonAdd("#CheckSheetDetailsPager",
            //   {
            //       title: "add",
            //       buttonicon: "ui-icon-plus",
            //       caption: "",
            //       position: "first",
            //       onClickButton: function () {

            //           $("#IdDivModelCheckSheetDetail").modal("show");

            //       }
            //   });

            $("#IdTblCheckSheetGrid").navButtonAdd("#CheckSheetDetailsPager",
               {
                   title: "Refresh",
                   buttonicon: "ui-icon-refresh",
                   caption: "",
                   position: "last",
                   onClickButton: function () {
                       $("#IdTblCheckSheetGrid").jqGrid("GridUnload");
                       $.CheckSheetLandingGrid(pid);
                   }
               });
           
            $("#IdTblCheckSheetGrid").jqGrid('setGroupHeaders', {
                useColSpanStyle: false,
                groupHeaders: [
              { startColumnName: 'Groundworkstatus', numberOfColumns: 3, titleText: '<center>Ground Assembly</center>' },
              { startColumnName: 'LiftingPlacingstatus', numberOfColumns: 3, titleText: '<center>Lifting & Placing</center>' },
                    { startColumnName: 'electricalworkstatus', numberOfColumns: 3, titleText: '<center>Electrical Work</center>' },
                     { startColumnName: 'NoLoadstatus', numberOfColumns: 3, titleText: '<center>No Load Trial</center>' },

                ]
            });          
        }

    </script>
