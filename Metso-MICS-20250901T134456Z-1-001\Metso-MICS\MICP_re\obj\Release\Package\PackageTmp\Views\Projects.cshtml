﻿@{
    Layout = null;
}

<!DOCTYPE html>


<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Index</title>
    <link href="~/Assets/Jqgrid/ui.jqgrid.css" rel="stylesheet" />
    <style>
        label {
            font-family: sans-serif;
            font-size: 12px;
            font-weight: bold;
        }

        .panel-heading[data-toggle="collapse"]:after {
            font-family: 'Glyphicons Halflings';
            content: "\e113";
            position: absolute;
            color: white;
            font-size: 12px;
            /* line-height: 0px; */
            right: 30px;
            /* top: calc(52% - 11px); */
            margin-top: -16px;
        }

        .panel-heading[data-toggle="collapse"]:after {
            font-family: 'Glyphicons Halflings';
            content: "\e113";
            position: absolute;
            color: #229877;
            font-size: 12px;
            /* line-height: 0px; */
            right: 30px;
            /* top: calc(52% - 11px); */
            margin-top: -16px;
        }

        .panel-heading[data-toggle="collapse"].collapsed:after {
            -webkit-transform: rotate(0deg);
            -moz-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
        }

        .panel-heading[data-toggle="collapse"].collapsed:after {
            -webkit-transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            -o-transform: rotate(180deg);
            transform: rotate(180deg);
        }

        .modal-open .modal {
            overflow-x: hidden;
            overflow-y: hidden;
        }

        .ui-jqgrid-title {
            visibility: visible;
        }

        ui-jqgrid .ui-jqgrid-caption {
            font-size: 14px;
            color: red;
            background: rgba(221, 249, 249, 0.69);
            padding: 2px 4px 2px 6px;
        }

        .optionStyle:hover {
            background-color: green !important;
        }

        .ui-jqgrid .ui-jqgrid-view input, .ui-jqgrid .ui-jqgrid-view select, .ui-jqgrid .ui-jqgrid-view textarea, .ui-jqgrid .ui-jqgrid-view button {
            font-size: 11px;
            /* border: solid green 1px; */
        }

        .myclass1 {
            display: none;
            padding: 0px 2px;
            font-size: 10px;
            font-weight: normal;
            line-height: 1;
            color: white;
            text-align: center;
            background-color: #327d78;
            border: 1px solid #327d78;
            border-radius: 4px;
        }

        .ui-icon-circle-triangle-n {
            display: none;
        }


        .AddNew {
            width: 90%;
            height: 22px;
            background-color: #e4562b;
            color: white;
            font-family: sans-serif;
            font-weight: bold;
            margin-left: 10px;
            margin-bottom: 20px;
            outline: 0;
            border: 0px;
        }
    </style>
</head>
<body>
    <div style="width: inherit; overflow: auto;">
        <table id="IdTblProjectsGrid"></table>
        <div id="IdDivProjectsPager"></div>

    </div>
    <div id="div_loggedDetails" style="display: none">
        <span id="spn_loggedinID">@ViewBag.Employee_ID</span>
        <span id="spn_loggedinName">@ViewBag.Company_Employee_Name</span>
        <span id="spn_UserDesignation">@ViewBag.RefMasterDetail_Short_Name</span>
        <span id="spn_BrandID">@ViewBag.Branch_ID</span>
        <span id="spn_CountryID">@ViewBag.CountryIC</span>
    </div>
    @*//============================================================================================//*@
    <div class="modal fade" data-backdrop="false" data-keyboard="false" id="IdDivModelViewProjects" role="dialog" style="position: absolute !important; overflow-y: scroll;">
        <div class="modal-dialog modal-lg" style="width: 97%">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close closeProject" id="btnCLoseX">&times;</button>
                    <button type="button" class="Colsemodal" style="display: none" data-dismiss="modal"></button>
                    <h4 class="modal-title">Project Details</h4>
                </div>
                <div id="IDDivProjectModalBody" class="modal-body" style="overflow-y: scroll; height: 615px;">
                    <div id="IdDivProjectForm">
                        <br />
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjName" class="ClsLabelProjectTxtBox PTdisabledfields">Project Name<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjName" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT ProjectSaveValidationPT PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjCreatedBy" class="ClsLabelProjectTxtBox ">Created By<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjCreatedBy" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT ProjectSaveValidationPT fieldsDisabled bydefaultdisabled" readonly />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjCreatedDate" class="ClsLabelProjectTxtBox ">Created Date<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjCreatedDate" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT ProjectSaveValidationPT fieldsDisabled bydefaultdisabled" readonly />
                            </div>


                        </div>
                        <br />
                        <div class="row">

                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="Sel_Region" class="ClsLabelProjectTxtBox ">Region<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">

                                <select class="form-control ClsTxtProjectHeaderddl ProjectSubmitValidationPT ProjectSaveValidationPT PTdisabledfields" id="Sel_Region">
                                    <option value="0">--Select--</option>

                                </select>


                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjState" class="ClsLabelProjectTxtBox ">State<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">

                                <select class="form-control ClsTxtProjectHeaderddl ProjectSubmitValidationPT ProjectSaveValidationPT PTdisabledfields" id="ddl_States">
                                    <option value="0">--Select--</option>

                                </select>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="AssignedTo" class="ClsLabelProjectTxtBox ">Assigned To RIE<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <div class="input-group">
                                    <input id="IdTxtProjAssignedTo" type="text" class="form-control ClsTxtProjectHeader ProjectSubmitValidationPT fieldsDisabled bydefaultdisabled">
                                    <span class="input-group-addon" id="IdSpnProjectSelect"><i class="glyphicon glyphicon-search"></i></span>
                                </div>
                            </div>

                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="AssignedTo" class="ClsLabelProjectTxtBox ">Dispatcher<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <div class="input-group">
                                    <input id="IdTxtDispatcher" type="text" class="form-control ClsTxtProjectHeader ProjectSubmitValidationPT fieldsDisabled bydefaultdisabled">
                                    <span class="input-group-addon" id="IdSpnDispatcher"><i class="glyphicon glyphicon-search"></i></span>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjPlantType" class="ClsLabelProjectTxtBox ">Plant Type<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <select id="IdDdlPlantType" class="form-control ClsTxtProjectHeaderddl ProjectSubmitValidationPT PTdisabledfields">
                                    <option value="0">--Select--</option>

                                </select>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdDdlPlantStage" class="ClsLabelProjectTxtBox ">Stage<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <select id="IdDdlPlantStage" class="form-control ClsTxtProjectHeaderddl ProjectSubmitValidationPT PTdisabledfields">
                                    <option value="0">--Select--</option>

                                </select>
                            </div>

                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjCodeForAccordion" class="ClsLabelProjectTxtBox ">Project Code<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjCodeForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjLocation" class="ClsLabelProjectTxtBox ">Location<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-2 col-xs-12">
                                <textarea id="IdTxtProjLocation" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT ProjectSaveValidationPT PTdisabledfields" />
                            </div>


                        </div>
                        <br />
                        <div class="row">

                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjClient" class="ClsLabelProjectTxtBox ">Client(Owner)<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjClient" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSaveValidationPT PTdisabledfields" />
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjClientContactNo" class="ClsLabelProjectTxtBox ">Client Contact No<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjClientContactNo" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSaveValidationPT PTdisabledfields" />
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjClientEmialID" class="ClsLabelProjectTxtBox ">Client Email-id<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjClientEmialID" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSaveValidationPT PTdisabledfields" />
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjSiteClientName" class="ClsLabelProjectTxtBox ">Site Client Name<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjSiteClientName" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSaveValidationPT PTdisabledfields" />
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjSiteClientMobile" class="ClsLabelProjectTxtBox ">Site Client Mobile<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjSiteClientMobile" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSaveValidationPT  PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="txt_Projectmgr" class="ClsLabelProjectTxtBox ">Project Manager<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <div class="input-group">
                                    <input id="txt_Projectmgr" type="text" class="form-control ClsTxtProjectHeader ProjectSaveValidationPT fieldsDisabled bydefaultdisabled">
                                    <span class="input-group-addon" id="IdSpnProjectmgr"><i class="glyphicon glyphicon-search"></i></span>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row divredfont">


                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtSONumber" class="ClsLabelProjectTxtBox ">SO Number</label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtSONumber" type="text" class="form-control input-sm ClsTxtProjectHeader  PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="txt_SOorHFMDate" class="ClsLabelProjectTxtBox ">SO/HFM Date<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="txt_SOorHFMDate" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtSoldToCode" class="ClsLabelProjectTxtBox ">Sold To Code<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtSoldToCode" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                            </div>

                        </div>
                        <br />

                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtShiptoCode" class="ClsLabelProjectTxtBox ">Ship to Code<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtShiptoCode" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtServiceNetworkNo" class="ClsLabelProjectTxtBox ">Man Hour Network<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtServiceNetworkNo" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtPODate" class="ClsLabelProjectTxtBox ">PO Date<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtPODate" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT PTdisabledfields" />
                            </div>
                        </div>
                        <br />
                        <div class="" id="div_break1">
                            <div class="row divredfont">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="ddl_ProjectCurrentStatus" class="ClsLabelProjectTxtBox ">Current Status<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <select id="ddl_ProjectCurrentStatus" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields"></select>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjZeroDateMilestone" class="ClsLabelProjectTxtBox ">Zero Date Milestone<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <select id="IdTxtProjZeroDateMilestone" class="form-control ClsTxtProjectHeaderddl ProjectSubmitValidationPTred PTdisabledfields">
                                        <option value="0">--Select--</option>

                                    </select>
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txtPOReceivedDate" class="ClsLabelProjectTxtBox ">PO Received Date<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="txtPOReceivedDate" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>

                            </div>
                            <br />
                            <div class="row divredfont">
                                @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="txt_OrderValue" class="ClsLabelProjectTxtBox ">Order Value<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="txt_OrderValue" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                    </div>*@
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txt_OrderTerms" class="ClsLabelProjectTxtBox ">Order Terms<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <select id="txt_OrderTerms" class="form-control ProjectSubmitValidationPTred PTdisabledfields">

                                        <option value="0">--Select--</option>
                                    </select>
                                </div>
                                @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="txtBasicValue" class="ClsLabelProjectTxtBox ">Basic Value<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="txtBasicValue" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                    </div>*@
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtLOIDate" class="ClsLabelProjectTxtBox ">LOI Date<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtLOIDate" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtICNDate" class="ClsLabelProjectTxtBox ">ICN Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtICNDate" type="text" class="form-control input-sm ClsTxtProjectHeader  PTdisabledfields" />
                                </div>
                            </div>
                            <br />
                            <div class="row divredfont">
                                @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="txt_CreditNotValue" class="ClsLabelProjectTxtBox ">Credit Note Value(Basic)<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="txt_CreditNotValue" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                    </div>*@
                                @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="txt_CreditCoteNo" class="ClsLabelProjectTxtBox ">Credit Note No<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="txt_CreditCoteNo" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                    </div>*@
                            </div>
                            <br />
                            <div class="row divredfont">

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label id="IdTxtProjDODateForAccordion_lbl" for="IdTxtProjDODateForAccordion" class="ClsLabelProjectTxtBox ">DO Date<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjDODateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>                               
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjLayoutApproval" class="ClsLabelProjectTxtBox ">Layout Approval<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjLayoutApproval" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjClNoteRecievedForAccordion" class="ClsLabelProjectTxtBox ">Cl Note Recieved<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjClNoteRecievedForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>
                            </div>
                            <br />
                            <div class="row divredfont">

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjSiteClearanceForAccordion" class="ClsLabelProjectTxtBox ">Dispatch Clearance<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjSiteClearanceForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>                               
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjContractualDeliveryDaysForAccordion" class="ClsLabelProjectTxtBox ">Contractual Completion  Days<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjContractualDeliveryDaysForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjTargetDate" class="ClsLabelProjectTxtBox ">Target Date<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjTargetDate" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred fieldsDisabled bydefaultdisabled" disabled="true" />
                                </div>
                            </div>
                            <br />
                        </div>
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjContractualDeliveryForAccordion" class="ClsLabelProjectTxtBox ">Contractual Completion<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjContractualDeliveryForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT fieldsDisabled bydefaultdisabled" disabled="true" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjContractualRemainingDaysForAccordion" class="ClsLabelProjectTxtBox ">Contractual Remaining Days<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjContractualRemainingDaysForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT fieldsDisabled bydefaultdisabled" disabled="true" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="txt_Projected_CDD" class="ClsLabelProjectTxtBox ">Projected Contractual Completion Days<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="txt_Projected_CDD" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT PTdisabledfields" />
                            </div>
                        </div>
                        <br />
                        @*/=================== Pavan 14/Mar/2019 =======================/*@

                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtBufferdays" class="ClsLabelProjectTxtBox ">Buffer days</label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtBufferdays" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT fieldsDisabled bydefaultdisabled" disabled="true" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 clsblue">
                                <label for="IdTxt_Pono" class="ClsLabelProjectTxtBox ">PO No<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 clsblue">
                                <input id="IdTxt_Pono" class="form-control ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtICNDate" class="ClsLabelProjectTxtBox ">NT Month<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtNTMonth" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                            </div>
                        </div>
                        <br />

                        @*/=================== End =======================/*@
                        <div class="row divredfont">
                            @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtNTValue" class="ClsLabelProjectTxtBox ">NT Value<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtNTValue" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred fieldsDisabled bydefaultdisabled" />
                                </div>*@

                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxt_GSTNumberShipping" class="ClsLabelProjectTxtBoxIdle">GST Number(Shipping)<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxt_GSTNumberShipping" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields " />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="chk_GST_Shipping" class="ClsLabelProjectTxtBox ">GST Same as Shipping</label>
                            </div>

                            <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12">
                                <input id="chk_GST_Shipping" type="checkbox" name="" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxt_GSTNumberBilling" class="ClsLabelProjectTxtBoxIdle">GST Number (Billing)<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxt_GSTNumberBilling" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields " />
                            </div>
                        </div>
                        <br />
                        <div class="row dispatcherhide">


                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtProjIdleManpowerDaysForAccordion" class="ClsLabelProjectTxtBoxIdle">Idle Man Days</label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="IdTxtProjIdleManpowerDaysForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader fieldsDisabled bydefaultdisabled" disabled="true" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 clsblue">
                                <label for="IdTxtProjSupplyStatusForAccordion" class="ClsLabelProjectTxtBox ">Supply Status<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 clsblue">
                                <select id="IdTxtProjSupplyStatusForAccordion" class="form-control ClsTxtProjectHeaderddl ProjectSubmitValidationPTblue ProjectSubmitValidationPT PTdisabledfields">
                                    <option value="0">--Select--</option>

                                </select>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="txtSupplyCompletionDate" class="ClsLabelProjectTxtBox ">Supply Completion Date</label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input id="txtSupplyCompletionDate" type="text" class="form-control input-sm ClsTxtProjectHeader  PTdisabledfields" />
                            </div>
                        </div>
                        <br />
                        <div class="" id="div_break2">
                            <div class="row divredfont">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtWarrantywbs" class="ClsLabelProjectTxtBox ">Warranty WBS</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtWarrantywbs" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT " />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxt_TransportationWBS" class="ClsLabelProjectTxtBoxIdle">Transportation WBS<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxt_TransportationWBS" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields " />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txt_TourWBS" class="ClsLabelProjectTxtBox ">Tour WBS<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="txt_TourWBS" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT PTdisabledfields" />
                                </div>
                            </div>
                            <br />
                            <div class="row divredfont">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txt_TravelWBS" class="ClsLabelProjectTxtBox ">Travel WBS</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="txt_TravelWBS" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT PTdisabledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txtHypothecation" class="ClsLabelProjectTxtBox ">Hypothecation<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="txtHypothecation" type="text" class="form-control input-sm ClsTxtProjectHeader  PTdisabledfields ProjectSubmitValidationPTred" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtSFN" class="ClsLabelProjectTxtBox ">Sales force number</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtSFN" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT " />
                                </div>
                                @*  <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="txtLubricant" class="ClsLabelProjectTxtBox "> Lubricant </label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="txtLubricant" type="text" class="form-control input-sm ClsTxtProjectHeader  PTdisabledfields ProjectSubmitValidationPTred"  />
                                    </div>

                                     <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="txtGrease" class="ClsLabelProjectTxtBox ">Grease</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="txtGrease" type="text" class="form-control input-sm ClsTxtProjectHeader  PTdisabledfields ProjectSubmitValidationPTred"  />
                                    </div>*@


                            </div>

                            <br />
                            <div class="row divredfont">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 clsblue">
                                    <label for="IdTxt_InvoiceAddress" class="ClsLabelProjectTxtBox ">Invoice Address<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 clsblue">
                                    <textarea id="IdTxt_InvoiceAddress" class="form-control  ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                                </div>
                            </div>

                            <br />
                            <div class="row divredfont">

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txtManualDisptachAddress" class="ClsLabelProjectTxtBox ">Manual Dispatch Address<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <textarea id="txtManualDisptachAddress" type="text" class="form-control input-sm ClsTxtProjectHeader  PTdisabledfields ProjectSubmitValidationPTred" />
                                </div>
                            </div>
                            <br />
                        </div>

                        <div class="" id="div_break3">
                            <div class="row dispatcherhide">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="chk_CheckSheet" class="ClsLabelProjectTxtBox ">Check Sheet</label>
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12">
                                    <input id="chk_CheckSheet" type="checkbox" name="" disabled="true" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="chk_InstallationProtocol" class="ClsLabelProjectTxtBox ">Installation Protocol</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12">
                                    <input id="chk_InstallationProtocol" type="checkbox" name="" disabled="true" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="chk_InstallationProtocol" class="ClsLabelProjectTxtBox ">Hydra Availability</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12">
                                    <input id="chk_HydraAvailability" type="checkbox" name="" disabled="true" />
                                </div>
                            </div>
                            <br />
                            <div class="row dispatcherhide">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="chk_IdelReport" class="ClsLabelProjectTxtBox ">Idle Report</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12">
                                    <input id="chk_IdelReport" type="checkbox" name="" disabled="true" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="chk_IdelReport" class="ClsLabelProjectTxtBox ">Commissioning</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12">
                                    <input id="chk_Commissioning" type="checkbox" name="" disabled="true" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="chk_IdelReport" class="ClsLabelProjectTxtBox ">Is Active</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12">
                                    <input id="IdTxtProjIsActive" type="checkbox" name="" disabled="true" />
                                </div>

                            </div>
                            <br />
                        </div>
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="txt_SharePointlink" class="ClsLabelProjectTxtBox ">Share Point link<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-2 col-xs-12">
                                <textarea id="txt_SharePointlink" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationPT PTdisabledfields" />
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="ProjectStatus" class="ClsLabelProjectTxtBox ">Project Status:</label>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-2 col-xs-12">
                                <select id="IdDdlProjectStatus" class="form-control ClsTxtProjectHeaderddl  fieldsDisabled bydefaultdisabled" disabled="true">
                                    <option value="0">--Select--</option>

                                </select>
                            </div>
                        </div>
                        <br />
                        <div class="row divredfont">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 clsblue">
                                <label for="IdTxt_SIRemark" class="ClsLabelProjectTxtBox ">Special DI instruction</label>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 clsblue">
                                <textarea id="IdTxt_SIRemark" class="form-control  ClsTxtProjectHeader ProjectSubmitValidationPTred PTdisabledfields" />
                            </div>
                        </div>
                        <br />
                    </div>
                </div>

                @*Installation Accordion*@
                <div class="panel panel-default" id="div_InstallationAccoridn">
                    <div class="panel-heading accordion-toggle collapsed" data-toggle="collapse" style='box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer' data-parent="#accordion" data-target="#IDProjectInstallationDetails">
                        <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;">Installation Details</h4>
                    </div>
                    <div id="IDProjectInstallationDetails" style="font-family: Arial; font-size: 13px; color: black;" class="panel-collapse collapse">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdSitInCharge" class="ClsLabelProjectTxtBox ">Site in-charge<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <div class="input-group">
                                        <input id="IdSitInCharge" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT RIEDisbaledfields" /><span class="input-group-addon" id="spn_IdSitInCharge"><i class="glyphicon glyphicon-search"></i></span>

                                    </div>

                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjCurrentStatus" class="ClsLabelProjectTxtBox ">Current Status<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <select id="IdTxtProjCurrentStatus" class="form-control ClsTxtProjectHeaderddl ProjectSubmitValidationIT RIEDisbaledfields">
                                        <option value="0">--Select--</option>

                                    </select>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txtMobilZationIntimationReceived" class="ClsLabelProjectTxtBox ">Mobilzation Intimation Received</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="txtMobilZationIntimationReceived" type="checkbox" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjComissionedDateForAccordion" class="ClsLabelProjectTxtBox ">Comissioned Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjComissionedDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader  RIEDisbaledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjMobilizationDate" class="ClsLabelProjectTxtBox ">Mobilization Date <span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjMobilizationDate" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT  RIEDisbaledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdInstalltionEngineer" class="ClsLabelProjectTxtBox ">Installation Engineer<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <div class="input-group">
                                        <input id="IdInstalltionEngineer" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT fieldsDisabled bydefaultdisabled" /><span class="input-group-addon" id="Idinstalltionengineeric"><i class="glyphicon glyphicon-search"></i></span>

                                    </div>

                                </div>

                            </div>
                            <br />
                            <div class="div_break4">
                                <div class="row dispatcherhide">

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProjContractorPersonAtSite" class="ClsLabelProjectTxtBox ">Mech-Contractor <span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <div class="input-group">
                                            <input id="IdTxtProjContractor" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT CheckContrator bydefaultdisabled" disabled="true" />
                                            <span class="input-group-addon" id="IdspnTxtProjContractor" style="border: 1px solid white">
                                                <i class="glyphicon glyphicon-search"></i>
                                            </span>
                                            <span class="input-group-addon" id="IdspnTxtProjContractorplus" style="border: 1px solid white">
                                                <i class="glyphicon glyphicon-plus"></i>
                                            </span>
                                        </div>

                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProjContractorPersonAtSite" class="ClsLabelProjectTxtBox ">Mech-Contractor Person At Site<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <div class="input-group">
                                            <input id="IdTxtProjContractorPersonAtSite" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT bydefaultdisabled" disabled="true" />
                                            <span class="input-group-addon" id="IdSpnContractorPersonAtSite" style="border: 1px solid white"><i class="glyphicon glyphicon-search"></i></span>
                                            <span class="input-group-addon" id="IdSpnContractorPersonAtSitePlus" style="border: 1px solid white"><i class="glyphicon glyphicon-plus"></i></span>
                                        </div>

                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProjContractorSiteMobile" class="ClsLabelProjectTxtBox ">Mech-Contractor Site Mobile<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdTxtProjContractorSiteMobile" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT RIEDisbaledfields" />
                                    </div>
                                </div>
                                <br />
                                <div class="row dispatcherhide">

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProjContractorPersonAtSite" class="ClsLabelProjectTxtBox ">Elect-Contractor <span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <div class="input-group">
                                            <input id="IdTxtProjElectContractor" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT CheckContrator bydefaultdisabled" disabled="true" />
                                            <span class="input-group-addon" id="IdspnTxtProjElectContractor" style="border: 1px solid white"><i class="glyphicon glyphicon-search"></i></span>
                                            <span class="input-group-addon" id="IdspnTxtProjElectContractorplus" style="border: 1px solid white"><i class="glyphicon glyphicon-plus"></i></span>
                                        </div>

                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProjElectContractorPersonAtSite" class="ClsLabelProjectTxtBox ">Elect-Contractor Person At Site<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <div class="input-group">
                                            <input id="IdTxtProjElectContractorPersonAtSite" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT bydefaultdisabled" disabled="true" />
                                            <span class="input-group-addon" id="IdSpnElectContractorPersonAtSite" style="border: 1px solid white"><i class="glyphicon glyphicon-search"></i></span>
                                            <span class="input-group-addon" id="IdSpnElectContractorPersonAtSiteplus" style="border: 1px solid white"><i class="glyphicon glyphicon-plus"></i></span>
                                        </div>

                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProjElectContractorSiteMobile" class="ClsLabelProjectTxtBox ">Elect-Contractor Site Mobile<span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdTxtProjElectContractorSiteMobile" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT RIEDisbaledfields" />
                                    </div>
                                </div>
                                <br />
                            </div>
                            <div class="row">

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjClientDGReadinessDateForAccordion" class="ClsLabelProjectTxtBox ">Client DG Readiness Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjClientDGReadinessDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader  RIEDisbaledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjInComingCableReadinessDateForAccordion" class="ClsLabelProjectTxtBox ">In Coming Cable Readiness Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjInComingCableReadinessDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader  RIEDisbaledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjClientNoofCivilGuidanceVisited" class="ClsLabelProjectTxtBox ">No of Civil Guidance Visited <span style="color: red; font-size: 17px;" class="addstar">*</span></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjClientNoofCivilGuidanceVisited" type="text" class="form-control input-sm ClsTxtProjectHeader ProjectSubmitValidationIT RIEDisbaledfields" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjFirstMobilizeDate" class="ClsLabelProjectTxtBox ">1st Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjFirstMobilizeDate" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjFirstDeMobilizeDateForAccordion" class="ClsLabelProjectTxtBox ">1st De-Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjFirstDeMobilizeDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjFirstDeMobilizeReasonForAccordion" class="ClsLabelProjectTxtBox ">1st De-Mobilize Reason</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjFirstDeMobilizeReasonForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjSecondMobilizationDateForAccordion" class="ClsLabelProjectTxtBox ">2nd Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjSecondMobilizationDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjSecondDeMobilizationDate" class="ClsLabelProjectTxtBox ">2nd De-Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjSecondDeMobilizationDate" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjSecondDeMobilizeReasonForAccordion" class="ClsLabelProjectTxtBox ">2nd De-Mobilize Reason</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjSecondDeMobilizeReasonForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>

                            </div>
                            <br />
                            <div class="row">

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjThirdMobilizationDateForAccordion" class="ClsLabelProjectTxtBox ">3rd Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjThirdMobilizationDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjThirdDeMobilizationDateForAccordion" class="ClsLabelProjectTxtBox ">3rd De-Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjThirdDeMobilizationDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjThirdDeMobilizeReasonForAccordion" class="ClsLabelProjectTxtBox ">3rd De-Mobilize Reason:</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjThirdDeMobilizeReasonForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>



                            </div>
                            <br />
                            <div class="row">

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjfouthMobilizationDateForAccordion" class="ClsLabelProjectTxtBox ">4th Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjfouthMobilizationDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjforthDeMobilizationDateForAccordion" class="ClsLabelProjectTxtBox ">4th De-Mobilize Date</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjforthDeMobilizationDateForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjforthDeMobilizeReasonForAccordion" class="ClsLabelProjectTxtBox ">4th De-Mobilize Reason</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjforthDeMobilizeReasonForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader RIEDisbaledfields" />
                                </div>



                            </div>
                            <br />
                            <div class="row dispatcherhide">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjDaysTillNow" class="ClsLabelProjectTxtBox ">Days Till Now</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjDaysTillNow" type="text" class="form-control input-sm ClsTxtProjectHeader fieldsDisabled bydefaultdisabled" disabled="true" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjRemainingDaysForAccordion" class="ClsLabelProjectTxtBox ">Remaining Days</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjRemainingDaysForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader fieldsDisabled bydefaultdisabled" disabled="true" />
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdTxtProjIdleHydraDaysForAccordion" class="ClsLabelProjectTxtBoxIdle">Idle Hydra Days</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <input id="IdTxtProjIdleHydraDaysForAccordion" type="text" class="form-control input-sm ClsTxtProjectHeader fieldsDisabled bydefaultdisabled" disabled="true" />
                                </div>
                            </div>
                            <br />
                            <div class="row">

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdSevicenEngineer" class="ClsLabelProjectTxtBox ">Primary Service  Engineer</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <div class="input-group">
                                        <input id="IdSevicenEngineer" type="text" class="form-control input-sm ClsTxtProjectHeader fieldsDisabled Dispatcherdisbaledfields" /><span class="input-group-addon" id="spn_IdSevicenEngineer"><i class="glyphicon glyphicon-search"></i></span>

                                    </div>

                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="IdsecSevicenEngineer" class="ClsLabelProjectTxtBox ">Secondary Service Engineer</label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <div class="input-group">
                                        <input id="IdsecSevicenEngineer" type="text" class="form-control input-sm ClsTxtProjectHeader fieldsDisabled Dispatcherdisbaledfields" /><span class="input-group-addon" id="IdspnsecSevicenEngineer"><i class="glyphicon glyphicon-search"></i></span>

                                    </div>

                                </div>



                            </div>
                            <div class="row" style="display: none">
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_RIEIC" placeholder="RIE IC" />

                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_IEIC" placeholder="IE IC" />


                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_PmgrIC" placeholder="Project Mgr IC" />

                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_creatbyIC" placeholder="CreatedbyIC" />


                                </div>

                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_ContratorIC" placeholder="Contractor IC" />


                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_ContractorPersonIC" placeholder="ContractorPerson IC" />


                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_ElecContratorIC" placeholder="MechContractor IC" />


                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_ElecContractorPersonIC" placeholder="MechContractorPerson IC" />


                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_PrimaryServiceEngineerIC" placeholder="Primary SEIC" />


                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_SecondaryServiceEngineerIC" placeholder="Secondary SEIC" />


                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                    <input type="text" class="form-control ClsTxtProjectHeader" id="txt_DispatcherIC" placeholder="Dispatcher IC" />


                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                @*Plant details Accordion*@
                <div class="panel panel-default" id="div_PlantAccoridn">
                    <div class="panel-heading accordion-toggle collapsed" style="box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer;" data-toggle="collapse" data-parent="#accordion" data-target="#IDPlantDetails">
                        <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;">Plant Details</h4>
                    </div>
                    <div id="IDPlantDetails" style="font-family: Arial; font-size: 13px; color: black;" class="panel-collapse collapse">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-2 col-sm-1 col-xs-12"></div>
                                <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12">
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                            <div style="width: inherit; overflow: auto;">
                                                <table id="IdTblPlantGrid"></table>
                                                <div id="IdDivPlantPager"></div>
                                            </div>
                                        </div>
                                    </div>



                                </div>

                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12"></div>
                            </div>
                            <br />
                            @*<fieldset>
                                    <h4 style="background:#327d78;color:white">Short Supply Details</h4>
                                </fieldset>
                                <br />*@
                            <div class="row dispatcherhide">
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12"></div>
                                <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12">
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                            <div style="width: inherit; overflow: auto;">
                                                <table id="IdTblShortSupplyGrid"></table>
                                                <div id="IdDivShortSupplyPager"></div>
                                            </div>
                                        </div>
                                    </div>



                                </div>

                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12"></div>
                            </div>
                            <br />
                            <div class="row" style="display: none">
                            </div>
                            <br />
                            <div class="row dispatcherhide">
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txt_TeritoryDesc" class="ClsLabelProjectTxtBox">Primary Description:</label>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-2 col-xs-12">
                                    <textarea id="txt_PrimaryDesc" class="form-control input-sm ClsTxtProjectHeader PTdisabledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txt_SecDesc" class="ClsLabelProjectTxtBox">Secondary Description:</label>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-2 col-xs-12">
                                    <textarea id="txt_SecDesc" class="form-control input-sm ClsTxtProjectHeader PTdisabledfields" />
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                </div>
                            </div>
                            <br />
                            <div class="row dispatcherhide">
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txt_TeritoryDesc" class="ClsLabelProjectTxtBox">Territory Description:</label>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-2 col-xs-12">
                                    <textarea id="txt_TeritoryDesc" class="form-control input-sm ClsTxtProjectHeader PTdisabledfields" />
                                </div>

                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                    <label for="txt_OthersDesc" class="ClsLabelProjectTxtBox">Others Description:</label>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-2 col-xs-12">
                                    <textarea id="txt_OthersDesc" class="form-control input-sm ClsTxtProjectHeader PTdisabledfields" />
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-2 col-xs-12">
                                </div>
                            </div>
                            @*<div class="row">*@
                            @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>*@
                            @*      <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                        <div style="width: inherit; overflow: auto;" id="IdDivHydraAvailabilityHistoryDetails">
                                            <table id='IdTblHydraAvailabilityHistoryGrid'></table>
                                            <div id='IdTblHydraAvailabilityHistoryPager'></div>
                                        </div>
                                    </div>
                                </div>*@

                        </div>
                    </div>
                </div>

                @*Movement Log Accordion*@
                <div class="panel panel-default" id="div_MovementLogAccoridn">
                    <div class="panel-heading accordion-toggle collapsed" style="box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer" data-toggle="collapse" data-parent="#accordion" data-target="#IDProjectMovementDetails">
                        <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;">Movement Log</h4>
                    </div>
                    <div id="IDProjectMovementDetails" style="font-family: Arial; font-size: 13px; color: black;" class="panel-collapse collapse">
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>
                                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                    <table id="IdTblMovementGrid"></table>
                                    <div id="IdDivMovementPager"></div>
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12"></div>
                                <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">
                                    <table id="IdTblStageMovementGrid"></table>
                                    <div id="IdDivStageMovementPager"></div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <br />
                <div class="modal-footer">
                    <button type="button" id="IdBtnProjectDraft" class="btn ButtonStyle ClsEnquiryFrombtn">Draft</button>
                    <button type="button" id="IdBtnProjectSubmit" class="btn ButtonStyle ClsEnquiryFrombtn">Submit</button>
                    <button type="button" id="IdBtnProjectPDFGenerate" class="btn ButtonStyle ClsEnquiryFrombtn">PDF Generate</button>
                    <button type="button" id="IdBtnProjectSendmail" class="btn ButtonStyle">Send mail</button>
                    <button type="button" id="IdBtnProjectCancel" class="btn ButtonStyle ClsEnquiryFrombtn closeProject">Cancel</button>
                </div>
            </div>
        </div>       
    </div>
    @*</div>*@

    @*</div>*@
    @*Modal For EmployeeDetails*@

    <div class="modal fade" id="EmployeeDetailsModal" tabindex="-1" style="overflow-y: auto; width: 100%; height: auto; padding-left: 0px; padding-left: 0px !important; position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; margin: auto; border-radius: 0px;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close CloseButtonHeader" data-dismiss="modal" style="margin-left: -10px;">&times;</button>
                    <h4 class="text-center" id="header_Request">Employee Details</h4>
                </div>
                <div class="modal-body">
                    <div class="container" style="width: inherit">
                        <div class="row" align="center">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">
                                <div style="width: inherit; overflow: auto;">
                                    <table id="TblEmployeeDetailsGrid"></table>
                                    <div id="div_EmployeeDetailsGridPager">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 " align="center">
                                <input type="button" value="Ok" class="btn ButtonStyle" id="BtnEmpOk" style="width: 68px" />
                                &nbsp;
                                <input type="button" value="Cancel" class="btn ButtonStyle" id='BtnCancelReq' data-dismiss="modal" style="width: 68px" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 " align="center">
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    @*Contractor Modal*@
    <div class="modal fade" id="ContratorDetailsModal" tabindex="-1" style="overflow-y: auto; width: 100%; height: auto; padding-left: 0px; padding-left: 0px !important; position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; margin: auto; border-radius: 0px;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close CloseButtonHeader" data-dismiss="modal" style="margin-left: -10px;">&times;</button>
                    <h4 class="text-center" id="header_Contrator">Contractor Details</h4>
                </div>
                <div class="modal-body">
                    <div class="container" style="width: inherit">
                        <div class="row" align="center">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">
                                <div style="width: inherit; overflow: auto;">
                                    <table id="TblContractorDetailsGrid"></table>
                                    <div id="div_ContractorGridPager">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 " align="center">
                                <input type="button" value="Ok" class="btn ButtonStyle" id="BtnContractorOk" style="width: 68px" />
                                &nbsp;
                                <input type="button" value="Cancel" class="btn ButtonStyle" id='BtnContractorCancel' data-dismiss="modal" style="width: 68px" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 " align="center">
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    @*Contractor Contact Person Details*@
    <div class="modal fade" id="ContratorPersonDetailsModal" tabindex="-1" style="overflow-y: auto; width: 100%; height: auto; padding-left: 0px; padding-left: 0px !important; position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; margin: auto; border-radius: 0px;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close CloseButtonHeader" data-dismiss="modal" style="margin-left: -10px;">&times;</button>
                    <h4 class="text-center" id="header_ContratorPerson">Contractor Details</h4>
                </div>
                <div class="modal-body">
                    <div class="container" style="width: inherit">
                        <div class="row" align="center">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">
                                <div style="width: inherit; overflow: auto;">
                                    <table id="TblContractorPersonDetailsGrid"></table>
                                    <div id="div_ContractorPersonGridPager">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 " align="center">
                                <input type="button" value="Ok" class="btn ButtonStyle" id="BtnContractoPersonrOk" style="width: 68px" />
                                &nbsp;
                                <input type="button" value="Cancel" class="btn ButtonStyle" id='BtnContractorPersonCancel' data-dismiss="modal" style="width: 68px" />
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 " align="center">
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row" style="display: none">
        <div class="col-lg-4">
            <input type="file" id="FileAttchments" />
        </div>
        <div class="col-lg-4">
            <button id="btn_test">Upload Images</button>
        </div>
    </div>
</body>
</html>

<script src="~/Assets/Jqgrid/grid.locale-en.js"></script>

<script src="~/Assets/Jqgrid/jquery.jqGrid_4.7.js"></script>
<script>

    var ProjectID = null;
    var Special_Instruction_Remarks = "";
    var Sale_force_no = "";
    var DI_PDF_Created_date = "";
    var DI_Sr_no = "";
    var Rev_no = "";
    var Indent_no = "";
    var Contractual_DeliveryDate_Othersvalue = "";
    var Usertype = $("#spn_UserDesignation").html();
    var LoginName = $("#spn_loggedinName").html();
    var LoginID = $("#spn_loggedinID").html();
    $("#IdBtnProjectSendmail").hide();
    if ('@ViewBag.ProjectsProjectCode' == '0')
    {
        function NewButton()
        {
            //alert(Usertype);
            $("#IdToAppend").html("");
            $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
            $("#IdToAppend").append("<Button id='IdBtnNewProject' class='AddNew'>New</Button>");
            $("#IdBtnProjectSendmail").hide();
            if (Usertype.toString().toLowerCase() == "pt" || Usertype.toString().toLowerCase() == "pm")
            {

                $("#IdBtnNewProject").css('display', 'block');


            } else
            {
                $("#IdBtnNewProject").css('display', 'none');
            }
        }

        ;
        NewButton();

        $(document).on('click', '#IdBtnNewProject', function ()
        {
            $("#IdBtnProjectPDFGenerate").hide();
            $("#IdDivModelViewProjects").modal({ backdrop: false });
            $(".ClsTxtProjectHeader").val("").css('border', '1px solid silver');
            $(".ClsTxtProjectHeaderddl").val("0").css('border', '1px solid silver');;
            ClearControls();
            ProjectID = 0;
            PlantGridForProjects();
            ShortSupplyDetailsForProjects();
            MomentGridForProjects();
            StageMomentGridForProjects();
            EditClick = "0";
            //Disable fields
            $(".bydefaultdisabled").prop("disabled", true);
            $(".RIEDisbaledfields").prop("disabled", false);
            $(".PTdisabledfields").prop("disabled", false);
            $(".Dispatcherdisbaledfields").prop("disabled", true);
            $("#IdBtnProjectDraft").css('display', 'inline-block');
            $("#IdBtnProjectSubmit").css('display', 'inline-block');
            $("#IdTxtProjIsActive").prop("disabled", false);
            $("#txtMobilZationIntimationReceived").prop("disabled", false);
            $("#Add_1").css('display', 'inline-block');
            $("#del_1").css('display', 'inline-block');
            $("#IdTblPlantGrid").jqGrid('showCol', "Select");
            $("#IdTblPlantGrid").jqGrid('showCol', "Edit");
            $("#ddl_ProjectCurrentStatus").prop("disabled", true);
            $("#dispatcherhide").css("display", 'block');
            if (Usertype.toString().toLowerCase() == "pt") {
                $("#div_PlantAccoridn").css("display", "none");
                $("#div_InstallationAccoridn").css("display", "none");

            } else {
                $("#div_PlantAccoridn").css("display", "block");
                $("#div_InstallationAccoridn").css("display", "block");
            }
            $("#IdTxtProjDODateForAccordion_lbl").show();
            $("#IdTxtProjDODateForAccordion").show();
        })
        newbuttonValue = 1;
    }

    if ('@ViewBag.ProjectsProjectCode' != '0')
    {
        newbuttonValue = 0;
        ProjectID = '@ViewBag.ProjectsProjectCode';
    }

    function LeftMenuInactive() {
        for (var i = 0; i < $("#IdLeftMenu li").length; i++) {
            $($("#IdLeftMenu li ")[i]).removeClass("menuActive");
        }
    }



    function Generate_DI_PDF() {
        var DataForPdf = [];
        var DataListForPdf =
            {
                ProjClient: $("#IdTxtProjClient").val(),
                PlantType: $("#IdDdlPlantType").children(":selected").text(),
                PlantStage: $("#IdDdlPlantStage").children(":selected").text(),
                Pono: $("#IdTxt_Pono").val(),
                PoData: $("#IdTxtPODate").val(),
                ProjCodeForAccordion: $("#IdTxtProjCodeForAccordion").val(),
                SoNumber: $("#IdTxtSONumber").val(),
                TransportationWBS: $("#IdTxt_TransportationWBS").val(),
                InvoiceAddress: $("#IdTxt_InvoiceAddress").val(),
                ManualDisptachAddress: $("#txtManualDisptachAddress").val(),
                ProjLocation: $("#IdTxtProjLocation").val(),
                ClientContactNo: $("#IdTxtProjClientContactNo").val(),
                ClientEmialID: $("#IdTxtProjClientEmialID").val(),
                SiteClientName: $("#IdTxtProjSiteClientName").val(),
                ProjSiteClientMobile: $("#IdTxtProjSiteClientMobile").val(),
                Hypothecation: $("#txtHypothecation").val(),
                GSTNumber: $("#IdTxt_GSTNumberShipping").val(),
                ProjCreatedBy: $("#IdTxtProjCreatedBy").val(),
                SIRemark: $("#IdTxt_SIRemark").val(),
                Special_Instruction_Remarks: Special_Instruction_Remarks,
                Sale_force_no: Sale_force_no,
                DI_PDF_Created_date: DI_PDF_Created_date,
                DI_Sr_no: DI_Sr_no,
                Rev_no: Rev_no,
                Indent_no: Indent_no,
                ProjectID: ProjectID
            };
        Intemlist = [];
        for (var i = 0; i < itemarr.length; i++)
        {
            var Itemarry =
                {
                    ItemType: itemarr[i].ItemTypeName,
                    Models_Name: itemarr[i].SIC_Models_Name
                }
            Intemlist.push(Itemarry);
        }
        DataForPdf.push(DataListForPdf);
        $.ajax({
            url: AbsolutePath("/Pdf/pdfdata"),
            type: 'POST',
            cache: false,
            async:false,
            datatype: "JSON",
            data:
            {
                "DataListForPdf": JSON.stringify(DataForPdf),
                "Itemarry": JSON.stringify(Intemlist),
            },
            success: function (resp) {
                // window.location.href = AbsolutePath("/Pdf/ExportToPdf");
                window.open
                  (
                  AbsolutePath('/Pdf/DispatchInstructionPdf'), '_blank' // <- This is what makes it open in a new window.
              );

                MainLandingGridForProjects();
            },
            error: function (resp) {
                AlertMessage("Some thing went wrong", "warning")
            },
        });
    };


    $(document).on('click', '#IdBtnProjectSendmail', function ()
    {
        if (DI_Sr_no != null && DI_Sr_no.trim() != "") {
            $.ajax({
                url: AbsolutePath('/Pdf/SendEmail?DI_Sr_no=' + DI_Sr_no),
                type: 'GET',
                datatype: "JSON",
                success: function (resp)
                {
                    AlertMessage("Mail Sten Sucessfully", "sucess")
                },
                error: function (resp) {
                }
            });
        }
    });
   
    $(document).on('click', '#IdBtnProjectPDFGenerate', function ()
    {
        $.ajax({
            url: AbsolutePath('/Pdf/PdfPresent?DI_Sr_no=' + DI_Sr_no),
            type: 'GET',
            datatype: "JSON",
            success: function (resp)
            {
                if (resp == "true")
                {
                    swal({
                                buttons:
                                {
                                    confirm: true,
                                    cancel: true,
                                },
                                title: "PDF for this Project is already generated, do you need another copy ?",
                                type: "warning",
                                confirmButtonText: "No",
                                confirmButtonColor: "#DD6B55",
                                showCancelButton: true,
                                cancelButtonText: "Yes", closeOnConfirm: true, closeOnCancel: true
                            },
                                function (isConfirm)
                                {
                                    window.onkeydown = null;
                                    window.onfocus = null;
                                    if (!isConfirm) {
                                        Generate_DI_PDF()
                                        $(".Colsemodal").trigger("click");
                                        NewButton();
                                    }
                                    else {
                                        //pavan
                                    }
                                });
                }
                else
                {
                    Generate_DI_PDF();
                    $(".Colsemodal").trigger("click");
                    NewButton();
                }
            },
            error: function (resp) {

            }
        });

        //if (DI_Sr_no != null && DI_Sr_no.trim() != "")
        //{
        //    swal({
        //        buttons:
        //        {
        //            confirm: true,
        //            cancel: true,
        //        },
        //        title: "PDF for this Project is already generated, do you need another copy ?",
        //        type: "warning",
        //        confirmButtonText: "No",
        //        confirmButtonColor: "#DD6B55",
        //        showCancelButton: true,
        //        cancelButtonText: "Yes", closeOnConfirm: true, closeOnCancel: true
        //    },
        //        function (isConfirm)
        //        {
        //            window.onkeydown = null;
        //            window.onfocus = null;
        //            if (!isConfirm) {
        //                Generate_DI_PDF()
        //                $(".Colsemodal").trigger("click");
        //                NewButton();
        //            }
        //            else {
        //                //pavan
        //            }
        //        });
        //}
        //else
        //{
        //    Generate_DI_PDF();
        //    $(".Colsemodal").trigger("click");
        //    NewButton();
        //}
    })

    //============================================= Project ==========================================//

    function ProjectLeftMenu() {
        $("#IdToAppend").html("");
        $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        $("#IdToAppend").append("<ul id='IdLeftMenu'></ul>")
        $("#IdLeftMenu").css("margin-left", "-30px");
        $("#IdLeftMenu").append("<li id='IdProjectProject'>Project</li>")
        $("#IdLeftMenu").append("<li id='IdProjectCheckSheet'>Progress Sheet</li>")
        $("#IdLeftMenu").append("<li id='IdProjectInstallationProtocalls'>Installation Protocolls</li>")
        $("#IdLeftMenu").append("<li id='IdProjectHydraAvailability'>Hydra Availability</li>")
        $("#IdLeftMenu").append("<li id='IdProjectIdleReport'>Idle Report</li>")
        $("#IdLeftMenu").append("<li id='IdProjectCommissioning'>Commissioning</li>")
        //  $("#IdLeftMenu").append("<li id='IdProjectShortSupplies'>Short Supplies</li>")
    }

    $(document).on('click', '#IdProjectProject', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Projects?ProjectId=" + 0));
        newbuttonValue = 1;
    })

    $(document).on('click', '#IdProjectInstallationProtocalls', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/InstallationProtocalls?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdProjectHydraAvailability', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/HydraAvailability?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdProjectCommissioning', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdProjectCheckSheet', function ()
    {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/CheckSheets?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdProjectIdleReport', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/IdleReport?ProjectId=" + ProjectID));
    })


   @* function ViewProjectDetails(Id)
    {
        if ('@ViewBag.ProjectsProjectCode' == '0')
        {
            ProjectLeftMenu();
        }
        $("#IdDivModelViewProjects").modal({ backdrop: false });
    }*@

    function AbsolutePath(url) {
        var path = '@Request.ApplicationPath';
        if (path == '/')
            return url;
        else
            return path + url;
    }
    //Ashwini Started Coding
    var EmployeeSearch = 0;
    var ContractorIC = null;
    var EmployeeType = null;
    var EmployeeIC = null;
    var EmployeeName = null;
    var SerachEmployee = 0;
    var BranchID = $("#spn_BrandID").html();

    var Arr_Edit = [];
    var RowIDCollection_Add = [];
    var MechContractorNameadd = null;
    var ElecContractorNameadd = null;
    var MechContractorNameaddIC = null;
    var ElecContractorNameaddIC = null;

   
    var EditClick = 0;
    var validation = "1";
    var PlantDetailsGridGridValidation = "1";
    var ProjectStatusIC = null;

    if (Usertype.toString().toLowerCase() == "pt" || Usertype.toString().toLowerCase() == "pm")
    {
        $("#IdBtnNewProject").css('display', 'block');


    } else {
        $("#IdBtnNewProject").css('display', 'none');
    }


    $("#IdSpnProjectSelect").click(function () {
        if ($("#ddl_States").children(":selected").val() != "0") {
            EmployeeSearch = '1';
            $("#header_Request").html("Regional Installation Engineer Details");
            $("#EmployeeDetailsModal").modal();
            GetEMployeesDetailsGrid();
            EmployeeType = "1";
        } else {

            AlertDialog("Please select state", "warning");
        }

    })
    $("#IdSpnDispatcher").click(function () {
        if ($("#ddl_States").children(":selected").val() != "0") {
            EmployeeSearch = '3';
            $("#header_Request").html("Dispatcher Details");
            $("#EmployeeDetailsModal").modal();
            GetEMployeesDetailsGrid();
            EmployeeType = "2";
        } else {
            AlertDialog("Please select state", "warning");
        }
    })
    $("#Idinstalltionengineeric").click(function () {
        if ($("#ddl_States").children(":selected").val() != "0") {
            EmployeeSearch = '2';
            $("#header_Request").html("Installation Engineer Details");
            $("#EmployeeDetailsModal").modal();
            GetEMployeesDetailsGrid();
            EmployeeType = "3";
        }
        else {

            AlertDialog("Please select state", "warning");
        }
    })
    $("#spn_IdSitInCharge").click(function () {
        if ($("#ddl_States").children(":selected").val() != "0") {
            EmployeeSearch = '2';
            $("#header_Request").html("Installation Engineer Details");
            $("#EmployeeDetailsModal").modal();
            GetEMployeesDetailsGrid();
            EmployeeType = "7";
        } else {
            AlertDialog("Please select state", "warning");
        }
    })
    $("#IdSpnProjectmgr").click(function () {
        if ($("#ddl_States").children(":selected").val() != "0") {
            EmployeeSearch = '4';
            $("#header_Request").html("Project Manager Details");
            $("#EmployeeDetailsModal").modal();
            GetEMployeesDetailsGrid();
            EmployeeType = "4";
        } else {
            AlertDialog("Please select state", "warning");
        }
    })
    $("#spn_IdSevicenEngineer").click(function () {
        if ($("#ddl_States").children(":selected").val() != "0") {
            EmployeeSearch = '0';
            $("#header_Request").html("Service Engineer Details");
            $("#EmployeeDetailsModal").modal();
            GetEMployeesDetailsGrid();
            EmployeeType = "5";
        } else {
            AlertDialog("Please select state", "warning");
        }
    })
    $("#IdspnsecSevicenEngineer").click(function () {
        if ($("#ddl_States").children(":selected").val() != "0") {
            EmployeeSearch = '0';
            $("#header_Request").html("Service Engineer Details");
            $("#EmployeeDetailsModal").modal();
            GetEMployeesDetailsGrid();
            EmployeeType = "6";
        } else {
            AlertDialog("Please select state", "warning");
        }
    })
    var ContractorType = 0;
    var ContractorPersonType = "0";
    $("#IdspnTxtProjContractor").click(function () {
        ContractorType = "1";
        $("#IdTxtProjContractor").val("");
        $("#txt_ContratorIC").val("");
        $("#IdTxtProjContractor").prop("disabled", true);
        $("#ContratorDetailsModal").modal();
        GetContratorDetailsGrid();
    })
    $("#IdspnTxtProjContractorplus").click(function () {
        $("#IdTxtProjContractor").val("");
        $("#txt_ContratorIC").val("");
        $("#IdTxtProjContractor").prop("disabled", false);
        ContractorType = 0;
    })
    $("#IdSpnContractorPersonAtSite").click(function () {
        if ($(txt_ContratorIC).val().trim() != "" || $("#IdTxtProjContractor").val().trim() != "") {
            $("#IdTxtProjContractorPersonAtSite").val("");
            $("#txt_ContractorPersonIC").val("");
            $("#IdTxtProjContractorPersonAtSite").prop("disabled", true);
            ContractorPersonType = "1";
            $("#ContratorPersonDetailsModal").modal();
            GetContractorPersonDetails();
        } else {
            ContractorPersonType = null;
            AlertDialog("Please select mech-contractor", "warning");
        }

    })


    function DecimalValidation(id, controllerlabel) {
        var value2 = parseFloat($(id).val().trim()).toFixed(2);
        if ($(id).val().trim() != "") {
            var value = parseFloat($(id).val().trim()).toFixed(2);
            if (value != 0.0) {
                var pattern = "^[0-9]+(\.[0-9]{1,2})?$";
                var exp = new RegExp(pattern);
                var result = exp.test($(id).val().trim());
                if (result != true) {
                    $(id).val('');
                    $(id).css('border', 'solid 1px red').val("").focus();
                    //alert("It allows values like ex:35, 457.54");
                    AlertDialog(controllerlabel + " allows values like ex:35, 457.54", "warning");

                }
                else {
                    $(id).css('border', '');
                }
            }
            else {
                $(id).val('');
                $(id).css('border', 'solid 1px red').val("").focus();
                AlertDialog(controllerlabel + "  allows values like ex:35, 457.54", "warning");
            }
        }
    }
    function NumberValidation(id, controllerlabel) {
        var value2 = parseFloat($(id).val().trim()).toFixed(2);
        if ($(id).val().trim() != "") {
            var value = parseFloat($(id).val().trim()).toFixed(2);
            if (value != 0.0) {
                var pattern = "^[0-9]+$";
                var exp = new RegExp(pattern);
                var result = exp.test($(id).val().trim());
                if (result != true) {
                    $(id).val('');
                    $(id).css('border', 'solid 1px red').val("").focus();
                    //alert("It allows values like ex:35, 457.54");
                    AlertDialog(controllerlabel + " allows values like ex:35, 457", "warning");

                }
                else {
                    $(id).css('border', '');
                }
            }
            else {
                $(id).val('');
                $(id).css('border', 'solid 1px red').val("").focus();
                AlertDialog(controllerlabel + "  allows values like ex:35, 457.54", "warning");
            }
        }
    }
    function CharacterValidation(id, controllerlabel) {
        var value2 = parseFloat($(id).val().trim()).toFixed(2);
        if ($(id).val().trim() != "") {
            var value = parseFloat($(id).val().trim()).toFixed(2);
            if (value != 0.0) {
                var pattern = "^[a-zA-Z ]+$";
                var exp = new RegExp(pattern);
                var result = exp.test($(id).val().trim());
                if (result != true) {
                    $(id).val('');
                    $(id).css('border', 'solid 1px red').val("").focus();
                    //alert("It allows values like ex:35, 457.54");
                    AlertDialog(controllerlabel + " allows only alphabets", "warning");

                }
                else {
                    $(id).css('border', '');
                }
            }
            else {
                $(id).val('');
                $(id).css('border', 'solid 1px red').val("").focus();
                AlertDialog(controllerlabel + "  allows only alphabets", "warning");
            }
        }
    }
    function validPhNumber(Selector) {
        $(Selector).blur(function () {
            if ($(Selector).val().trim() != "") {
                var pattern = "^[1-9][0-9]{9}$";
                var reg = new RegExp(pattern);
                var result = reg.test($(Selector).val().trim());
                if (result != true) {
                    $(Selector).val("").css("border", "1px solid red");
                    AlertDialog("Enter only 10 digit contact numbers Ex:9900990099", "warning");
                }
                else {
                    $(Selector).css("border", "1px solid lightgrey");
                }
            }
        });
    }
    function CommonValidationforAlphaNumbeicValues(Selector) {

        $(Selector).change(function () {
            var pattern = "^[A-z0-9]+$";
            var reg = new RegExp(pattern);
            var result = reg.test($(Selector).val().trim());
            if (result != true && $(Selector).val().trim() != "") {
                $(Selector).val("").css("border", "1px solid red");
                AlertDialog("Enter only alpha numeric values Ex: ASD123, 12356, QWSED", "warning");
            }
            else {
                $(Selector).css("border", "1px solid silver");
            }
        });

    }
    CommonValidationforAlphaNumbeicValues("#IdTxtSONumber");
    CommonValidationforAlphaNumbeicValues("#IdTxtSoldToCode");
    CommonValidationforAlphaNumbeicValues("#IdTxtShiptoCode");
    CommonValidationforAlphaNumbeicValues("#IdTxtServiceNetworkNo");
    validPhNumber("#IdTxtProjContractorSiteMobile");
    validPhNumber("#IdTxtProjElectContractorSiteMobile");
    $("#IdTxtProjClient").blur(function () {
        if ($("#IdTxtProjClient").val().trim() != "") {
            CharacterValidation(IdTxtProjClient, "Client(Owner) ");
        }
    })
    $("#IdTxtProjSiteClientName").blur(function () {
        if ($("#IdTxtProjSiteClientName").val().trim() != "") {
            CharacterValidation(IdTxtProjSiteClientName, "Site Client Name ");
        }
    })
    $("#IdTxtProjContractor").blur(function () {
        if ($("#IdTxtProjContractor").val().trim() != "") {
            CharacterValidation(IdTxtProjContractor, "Mech-Contractor ");
        }
    })
    $("#IdTxtProjContractorPersonAtSite").blur(function () {
        if ($("#IdTxtProjContractorPersonAtSite").val().trim() != "") {
            CharacterValidation(IdTxtProjContractorPersonAtSite, "Mech-Contractor Person At Site ");
        }
    })
    $("#IdTxtProjElectContractor").blur(function () {
        if ($("#IdTxtProjElectContractor").val().trim() != "") {
            CharacterValidation(IdTxtProjElectContractor, "Elect-Contractor  ");
        }

    })
    $("#IdTxtProjElectContractorPersonAtSite").blur(function () {
        if ($("#IdTxtProjElectContractorPersonAtSite").val().trim() != "") {
            CharacterValidation(IdTxtProjElectContractorPersonAtSite, "Elect-Contractor Person At Site  ");
        }
    })
    $("#IdSitInCharge").blur(function () {
        if ($("#IdSitInCharge").val().trim() != "") {
            CharacterValidation(IdSitInCharge, "Site in-charge  ");
        }
    })
    //$("#txt_OrderValue").blur(function () {
    //    if ($("#txt_OrderValue").val().trim() != "") {
    //        DecimalValidation(txt_OrderValue, "Order Value");
    //    }
    //})

    //$("#txtBasicValue").blur(function () {
    //    if ($("#txtBasicValue").val().trim() != "")
    //{
    //        var value2 = parseFloat($(txtBasicValue).val().trim()).toFixed(2);
    //        if ($(txtBasicValue).val().trim() != "") {
    //            var value = parseFloat($(txtBasicValue).val().trim()).toFixed(2);
    //            if (value != 0.0) {
    //                var pattern = "^[0-9]+(\.[0-9]{1,2})?$";
    //                var exp = new RegExp(pattern);
    //                var result = exp.test($(txtBasicValue).val().trim());
    //                if (result != true) {
    //                    $(txtBasicValue).val('');
    //                    $(txtBasicValue).css('border', 'solid 1px red').val("").focus();
    //                    //alert("It allows values like ex:35, 457.54");
    //                    AlertDialog("Basic value allows values like ex:35, 457.54", "warning");
    //                    $("#IdTxtNTValue").val("");
    //                }
    //                else {

    //                    $(txtBasicValue).css('border', '');
    //                    if ($("#txt_CreditNotValue").val().trim() != "") {
    //                        var BasicValue = $("#txtBasicValue").val().trim();
    //                        var CreditnoteValue = $(txt_CreditNotValue).val().trim();
    //                        if (parseFloat(BasicValue) <= parseFloat(CreditnoteValue)) {
    //                            AlertDialog("Basic value should be greater than credit note value", "warning");
    //                            $("#txtBasicValue").val("");
    //                            $("#IdTxtNTValue").val("");
    //                        } else {
    //                            var NTval = parseFloat(BasicValue) - parseFloat(CreditnoteValue);
    //                            $("#IdTxtNTValue").val(NTval);
    //                        }
    //                    }
    //                }
    //            }
    //            else {
    //                $(txtBasicValue).val('');
    //                $(txtBasicValue).css('border', 'solid 1px red').val("").focus();
    //                AlertDialog("Basic value allows values like ex:35, 457.54", "warning");
    //                $("#IdTxtNTValue").val("");
    //            }
    //        }

    //    }
    //})

    //$("#txt_CreditNotValue").blur(function () {
    //    if ($("#txt_CreditNotValue").val().trim() != "") {
    //        var value2 = parseFloat($(txt_CreditNotValue).val().trim()).toFixed(2);
    //        if ($(txt_CreditNotValue).val().trim() != "") {
    //            var value2 = parseFloat($(txt_CreditNotValue).val().trim()).toFixed(2);
    //            if (value2 != 0.0) {
    //                var pattern = "^[0-9]+(\.[0-9]{1,2})?$";
    //                var exp = new RegExp(pattern);
    //                var result = exp.test($(txt_CreditNotValue).val().trim());
    //                if (result != true) {
    //                    $(txt_CreditNotValue).val('');
    //                    $(txt_CreditNotValue).css('border', 'solid 1px red').val("").focus();
    //                    //alert("It allows values like ex:35, 457.54");
    //                    AlertDialog("Credit note value allows values like ex:35, 457.54", "warning");
    //                    $("#IdTxtNTValue").val("");
    //                }
    //                else {

    //                    $(txt_CreditNotValue).css('border', '');

    //                    //if ($("#txtBasicValue").val().trim() != "") {
    //                    //    var BasicValue = $("#txtBasicValue").val().trim();
    //                    //    var CreditnoteValue = $(txt_CreditNotValue).val().trim();
    //                    //    if (parseFloat(CreditnoteValue) >= parseFloat(BasicValue)) {
    //                    //        AlertDialog("Credit note value should be lesser than basic value", "warning");
    //                    //        $("#txt_CreditNotValue").val("");
    //                    //        $("#IdTxtNTValue").val("");
    //                    //    } else {
    //                    //        var NTval = parseFloat(BasicValue) - parseFloat(CreditnoteValue);
    //                    //        $("#IdTxtNTValue").val(parseFloat(NTval).toFixed(2));
    //                    //    }
    //                    //}
    //                }
    //            }
    //            else {
    //                $(txt_CreditNotValue).val('');
    //                $(txt_CreditNotValue).css('border', 'solid 1px red').val("").focus();
    //                AlertDialog("Credit note value allows values like ex:35, 457.54", "warning");
    //                $("#IdTxtNTValue").val("");
    //            }
    //        }

    //    }
    //})

    //$("#txt_CreditCoteNo").blur(function () {
    //    if ($("#txt_CreditCoteNo").val().trim() != "") {
    //        NumberValidation(txt_CreditCoteNo, "Credit Note Number");
    //    }
    //})
    $("#IdTxtProjClientNoofCivilGuidanceVisited").blur(function () {
        if ($("#IdTxtProjClientNoofCivilGuidanceVisited").val().trim() != "") {
            NumberValidation(IdTxtProjClientNoofCivilGuidanceVisited, "No of Civil Guidance Visited");
        }
    })
    $("#IdDdlPlantType").change(function () {
        for (var i = RowIDCollection_Add.length; i > 0; i--) {
            RowIDCollection_Add.pop(i);
        }
        for (var j = Arr_Edit.length; j > 0; j--) {
            Arr_Edit.pop(j);
        }


        if ($("#IdDdlPlantType").val() != "0") {
            if (ProjectID == "0") {
                for (var i = RowIDCollection_Add.length; i > 0; i--) {
                    RowIDCollection_Add.pop(i);
                }
                for (var j = Arr_Edit.length; j > 0; j--) {
                    Arr_Edit.pop(j);
                }

                PlantGridForProjects();
            } else {
                swal({
                    buttons:
                    {
                        confirm: true,
                        cancel: true,
                    },
                    title: "Are you sure, you want to change plant type",
                    type: "warning",
                    confirmButtonText: "No",
                    confirmButtonColor: "#DD6B55",
                    showCancelButton: true,
                    cancelButtonText: "Yes", closeOnConfirm: true, closeOnCancel: true
                },
                function (isConfirm) {
                    window.onkeydown = null;
                    window.onfocus = null;
                    if (!isConfirm) {
                        // Here Write code delete detailer
                        $.ajax({
                            url: AbsolutePath('/Project/DeleteProjectDetailerPlantDetaile?ProjectIC=' + ProjectID),
                            type: 'GET',
                            cache: false,
                            async: false,
                            datatype: "json",
                            success: function (resp, status, xhr) {
                                PlantGridForProjects();
                            }, error: function (e) {
                                AlertDialog("Fail to delete plant Details", "warning");
                            }
                        });

                    }
                    else {

                    }
                });
            }

        }

    })
    $("#IdDdlPlantStage").change(function () {
        for (var i = RowIDCollection_Add.length; i > 0; i--) {
            RowIDCollection_Add.pop(i);
        }
        for (var j = Arr_Edit.length; j > 0; j--) {
            Arr_Edit.pop(j);
        }

        if ($("#IdDdlPlantStage").val() != "0") {
            if (ProjectID == "0") {
                for (var i = RowIDCollection_Add.length; i > 0; i--) {
                    RowIDCollection_Add.pop(i);
                }
                for (var j = Arr_Edit.length; j > 0; j--) {
                    Arr_Edit.pop(j);
                }

                PlantGridForProjects();
            } else {
                swal({
                    buttons:
                    {
                        confirm: true,
                        cancel: true,
                    },
                    title: "Are you sure, you want to change plant stage",
                    type: "warning",
                    confirmButtonText: "No",
                    confirmButtonColor: "#DD6B55",
                    showCancelButton: true,
                    cancelButtonText: "Yes", closeOnConfirm: true, closeOnCancel: true
                },
                function (isConfirm) {
                    window.onkeydown = null;
                    window.onfocus = null;
                    if (!isConfirm) {
                        // Here Write code delete detailer
                        $.ajax({
                            url: AbsolutePath('/Project/DeleteProjectDetailerPlantDetaile?ProjectIC=' + ProjectID),
                            type: 'GET',
                            cache: false,
                            async: false,
                            datatype: "json",
                            success: function (resp, status, xhr) {
                                PlantGridForProjects();
                            }, error: function (e) {
                                AlertDialog("Fail to delete plant Details", "warning");
                            }
                        });

                    }
                    else {

                    }
                });
            }

        }

    })
    function ClearControls() {
        ProjectStatusIC = null;
        $(".ClsTxtProjectHeader").val("");
        $(".ClsLabelProjectTxtBoxIdle").val("");
        GetRegions();
        $("#Sel_Region").val("0");
        GetStates();
        $("#ddl_States").val("0");
        GetPlantTypes();
        $("#IdDdlPlantType").val("0");
        GetStageNames();
        $("#IdDdlPlantStage").val("0");
        GetProjectCurrentStatusNames();
        $("#ddl_ProjectCurrentStatus").val("0");
        GetProjectStatusnames();
        $("#IdDdlProjectStatus").val("0");
        GetCurrentStatusnames();
        $("#IdTxtProjCurrentStatus").val("0");
        GetZeroDateMileStoneValues();
        $("#IdTxtProjZeroDateMilestone").val("0");
        GetSupplyStatusnames();


        $("#IdTxtProjSupplyStatusForAccordion").val("0");
        GetOrderTerms();
        $("#txt_OrderTerms").val("0");
        $("#IdTxtProjIsActive").prop("checked", true);

        $("#chk_CheckSheet").prop("checked", false);
        $("#chk_InstallationProtocol").prop("checked", false);
        $("#chk_HydraAvailability").prop("checked", false);
        $("#chk_IdelReport").prop("checked", false);
        $("#chk_Commissioning").prop("checked", false);
        $("#txtMobilZationIntimationReceived").prop("checked", false);
        PlantGridForProjects();
        ShortSupplyDetailsForProjects();
        MomentGridForProjects();
        StageMomentGridForProjects();
        $("#IdTxtProjCreatedBy").val(LoginName);
        $("#txt_creatbyIC").val(LoginID);
        var d = new Date();
        $("#IdTxtProjCreatedDate").val(d.toString().split(' ')[2] + '-' + d.toString().split(' ')[1] + '-' + d.toString().split(' ')[3]);
        $(".fieldsDisabled").prop('disabled', true);
        MechContractorNameadd = null;
        ElecContractorNameadd = null;
        MechContractorNameaddIC = null;
        ElecContractorNameaddIC = null;
      //  $("#IdSpnDispatcher").removeClass("myclass1");

     //   $("#IdSpnProjectSelect").removeClass("myclass1");
        $("#IdSpnProjectmgr").removeClass("myclass1");
        $("#spn_IdSitInCharge").removeClass("myclass1");

      //  $("#Idinstalltionengineeric").removeClass("myclass1");
        $("#IdspnTxtProjContractor").removeClass("myclass1");
        $("#IdspnTxtProjContractorplus").removeClass("myclass1");

        $("#IdSpnContractorPersonAtSite").removeClass("myclass1");

        $("#IdSpnContractorPersonAtSitePlus").removeClass("myclass1");
        $("#IdspnTxtProjElectContractor").removeClass("myclass1");
        $("#IdspnTxtProjElectContractorplus").removeClass("myclass1");

        $("#IdSpnElectContractorPersonAtSite").removeClass("myclass1");

        $("#IdSpnElectContractorPersonAtSiteplus").removeClass("myclass1");

     //   $("#Idinstalltionengineeric").removeClass("myclass1");

        $("#spn_IdSevicenEngineer").addClass("myclass1");

        $("#IdspnsecSevicenEngineer").addClass("myclass1");

        $(".ProjectSubmitValidationPT").css("border", "1px solid silver");
        $(".ProjectSubmitValidationPTred").css("border", "1px solid silver");
        $(".ProjectSubmitValidationIT").css("border", "1px solid silver");

    }
    $("#IdSpnContractorPersonAtSitePlus").click(function () {
        if ($("#IdTxtProjContractor").val().trim() != "") {
            $("#IdTxtProjContractorPersonAtSite").val("");
            $("#txt_ContractorPersonIC").val("");
            $("#IdTxtProjContractorSiteMobile").val("");
            $("#IdTxtProjContractorPersonAtSite").prop("disabled", false);
            ContractorPersonType = null;
        } else {
            AlertDialog("Please enter mech-contractor name", 'warning');
        }

    })
    $("#IdspnTxtProjElectContractor").click(function () {
        ContractorType = "2";
        $("#IdTxtProjElectContractor").val("");
        $("#txt_ElecContratorIC").val("");
        $("#IdTxtProjElectContractor").prop("disabled", true);
        $("#ContratorDetailsModal").modal();
        GetContratorDetailsGrid();
    })
    $("#IdspnTxtProjElectContractorplus").click(function () {
        ContractorType = null;
        $("#IdTxtProjElectContractor").val("");
        $("#txt_ElecContratorIC").val("");
        $("#IdTxtProjElectContractor").prop("disabled", false);

    })
    $("#IdSpnElectContractorPersonAtSite").click(function () {
        if ($(txt_ElecContratorIC).val().trim() != "" || $(IdTxtProjElectContractor).val().trim() != "") {
            $("#IdTxtProjElectContractorPersonAtSite").val("");
            $("#txt_ElecContractorPersonIC").val("");
            $("#IdTxtProjElectContractorPersonAtSite").prop("disabled", true);
            ContractorPersonType = "2";
            $("#ContratorPersonDetailsModal").modal();
            GetContractorPersonDetails();
        } else {
            ContractorPersonType = null;
            AlertDialog("Please select Elect-contractor", "warning");
        }

    })
    $("#IdSpnElectContractorPersonAtSiteplus").click(function () {
        if ($("#IdTxtProjElectContractor").val().trim() != "") {
            $("#IdTxtProjElectContractorPersonAtSite").val("");
            $("#txt_ElecContractorPersonIC").val("");
            $("#IdTxtProjElectContractorPersonAtSite").prop("disabled", false);
            ContractorPersonType = null;
        } else {
            AlertDialog("Please enter elect-contractor name", "warning");
        }

    })

    var ContractorIC = null, ContractorPersonIC = null, ContractorName = null, ContractorPersonName = null, ContractorMbolie = null;
    var SearchContractor = "0", SerachCp = "0";
    function SelectContractor(id) {
        SearchContractor = "1";
        var RowData = $("#TblContractorDetailsGrid").getRowData(id);
        ContractorIC = RowData["ContractorIC"];
        ContractorName = RowData["ContractorName"];


    }
    function AlertDialog(text, type) {
        if (type == 'success') {
            swal({
                title: text,//text you want to show
                type: type, //type--success,warning,error,info
                html: true,
                confirmButtonText: "OK",
                showConfirmButton: true
            });
        }
        else {
            swal({
                title: text,//text you want to show
                type: type, //type--success,warning,error,info
                html: true,
                confirmButtonText: "OK",
                showConfirmButton: true
            });
        }
    }
    function SelectContractorPerson(id) {
        SerachCp = "1";
        var RowData = $("#TblContractorPersonDetailsGrid").getRowData(id);
        ContractorPersonIC = RowData["ContractorSitePersonDetailsIC"];
        ContractorPersonName = RowData["Name"];
        ContractorMbolie = RowData["MobileNumber"];


    }
    function SelectEmployee(id) {
        SerachEmployee = 1;
        var Rowdata = $("#TblEmployeeDetailsGrid").getRowData(id);
        EmployeeIC = Rowdata["Company_Employee_ID"];
        EmployeeName = Rowdata["Company_Employee_Name"];


    }

    $("#BtnEmpOk").click(function () {

        switch (EmployeeType) {
            case "1":
                if (SerachEmployee == 1) {
                    $("#txt_RIEIC").val(EmployeeIC);
                    $("#IdTxtProjAssignedTo").val(EmployeeName);
                    $("#EmployeeDetailsModal").modal('hide');
                    SerachEmployee = 0;
                } else {
                    AlertDialog("Please select regional installation engineer", "warning");
                }


                break;
            case "2":
                if (SerachEmployee == 1) {
                    $("#txt_DispatcherIC").val(EmployeeIC);
                    $("#IdTxtDispatcher").val(EmployeeName);
                    $("#EmployeeDetailsModal").modal('hide');
                    SerachEmployee = 0;
                } else {
                    AlertDialog("Please select dispatcher", "warning");
                }


                break;
            case "3":
                if (SerachEmployee == 1) {
                    $(txt_IEIC).val(EmployeeIC);
                    $(IdInstalltionEngineer).val(EmployeeName);

                    $("#EmployeeDetailsModal").modal('hide');
                    SerachEmployee = 0;
                } else {
                    AlertDialog("Please select installation engineer", "warning");
                }



                break;
            case "4":
                if (SerachEmployee == 1) {
                    $("#txt_PmgrIC").val(EmployeeIC);
                    $("#txt_Projectmgr").val(EmployeeName);

                    $("#EmployeeDetailsModal").modal('hide');
                    SerachEmployee = 0;
                } else {
                    AlertDialog("Please select project manager", "warning");
                }

                break;
            case "5":
                if (SerachEmployee == 1) {
                    $("#txt_PrimaryServiceEngineerIC").val(EmployeeIC);
                    $("#IdSevicenEngineer").val(EmployeeName);
                    $("#EmployeeDetailsModal").modal('hide');
                    SerachEmployee = 0;
                } else {
                    AlertDialog("Please select primary service engineer", "warning");
                }


                break;
            case "6":
                if (SerachEmployee == 1) {
                    $("#txt_SecondaryServiceEngineerIC").val(EmployeeIC);
                    $("#IdsecSevicenEngineer").val(EmployeeName);

                    $("#EmployeeDetailsModal").modal('hide');
                    SerachEmployee = 0;
                } else {
                    AlertDialog("Please select secondary service engineer", "warning");
                }

                break;
            case "7":
                if (SerachEmployee == 1) {
                    $("#IdSitInCharge").val(EmployeeName);
                    $("#EmployeeDetailsModal").modal('hide');
                    SerachEmployee = 0;
                } else {
                    AlertDialog("Please select site in charge", "warning");
                }
                break;
        }




    })
    $("#BtnCancelReq").click(function () {
        SerachEmployee = 0;
        EmployeeType = null;
        EmployeeIC = null;
        EmployeeName = null;
    })
    $("#BtnContractorOk").click(function () {
        switch (ContractorType) {
            case "1":
                if (SearchContractor == 1) {
                    $("#IdTxtProjContractor").val(ContractorName);
                    $("#txt_ContratorIC").val(ContractorIC);
                    $("#ContratorDetailsModal").modal('hide');
                    SearchContractor = 0;
                } else {
                    AlertDialog("Please select mech-contractor", "warning");
                }



                break;
            case "2":
                if (SearchContractor == 1) {

                    $("#IdTxtProjElectContractor").val(ContractorName);
                    $("#txt_ElecContratorIC").val(ContractorIC);
                    $("#ContratorDetailsModal").modal('hide');
                    SearchContractor = 0;
                } else {
                    AlertDialog("Please select elect-contractor", "warning");
                }

                break;

        }
    })
    $("#BtnContractorCancel").click(function () {
        SearchContractor = 0;
        ContractorType = null;
        ContractorName = null;
        ContractorIC = null;

    })
    $("#BtnContractoPersonrOk").click(function () {
        switch (ContractorPersonType) {
            case "1":

                if (SerachCp == "1") {
                    $("#IdTxtProjContractorPersonAtSite").val(ContractorPersonName);
                    $("#txt_ContractorPersonIC").val(ContractorPersonIC);
                    $("#IdTxtProjContractorSiteMobile").val(ContractorMbolie);
                    $("#ContratorPersonDetailsModal").modal('hide');
                    SerachCp = 0;
                } else {
                    AlertDialog("Please select mech-contractor person at site details", "warning");
                }

                break;
            case "2":
                if (SerachCp == "1") {
                    $("#IdTxtProjElectContractorPersonAtSite").val(ContractorPersonName);
                    $("#txt_ElecContractorPersonIC").val(ContractorPersonIC);
                    $("#IdTxtProjElectContractorSiteMobile").val(ContractorMbolie);
                    $("#ContratorPersonDetailsModal").modal('hide');
                    SerachCp = 0;
                } else {
                    AlertDialog("Please select elect-contractor person at site details", "warning");
                }

                break;

        }
    })
    $("#BtnContractorPersonCancel").click(function () {
        SerachCp = "0";
        ContractorPersonName = null;
        ContractorPersonIC = null;
        ContractorMbolie = null;
        ContractorPersonType = null;
    })
    function GetEMployeesDetailsGrid() {
        $("#TblEmployeeDetailsGrid").GridUnload();
        $("#TblEmployeeDetailsGrid").jqGrid({
            url: AbsolutePath('/Project/GetEmployeeDetails?Usertype=' + EmployeeSearch + '&&StateIC=' + $("#ddl_States").children(":selected").val() + '&&BranchIC=' + BranchID),
            datatype: 'json',
            width: "1000",
            height: '200',
            viewrecords: true,
            rowList: [5, 10, 15, 20],
            rownumbers: true,
            rownumWidth: 50,
            pager: "#div_EmployeeDetailsGridPager",
            rowNum: 20,
            //colNames: ["Select", "IC", "Name", "City", "RegionIC", "Region", "StateIC", "State", "Phone", "Email", "CountryIC", "ZipCode", "Address", "Code"],
            colModel: [{
                label: 'Select',
                name: "Select",
                width: 45,
                search: false,
                align: "center",
                formatter: function (a, b) {
                    return "<input type='radio' id='Party_" + b.rowId + "' name='ReqBy' class='SelectRow' onclick='SelectEmployee(" + b.rowId + ")' />"
                }
            },
            {
                label: 'Company_Employee_ID',
                name: "Company_Employee_ID",
                width: 50,
                align: 'right',
                editable: true,
                hidden: true,
            },
            {
                label: 'Employee Code',
                name: "Employee_ID",
                width: 100,
                align: 'left',
                editable: true,

            },
            {
                label: 'Employee Name',
                name: "Company_Employee_Name",
                width: 100,
                align: 'left',
                editable: true,

            },

            {
                label: 'Mobile #',
                name: "Company_Employee_MobileNumber",
                width: 100,
                editable: true,
            },
             {
                 label: 'Designation',
                 name: "RefMasterDetail_Name",
                 width: 100,
                 editable: true,
             },
            {
                label: 'Address',
                name: "Company_Employee_Address",
                width: 100,
                editable: true,
            },
            {
                label: 'Location',
                name: "Company_Employee_Location",
                width: 100,
                editable: true,
            },

            {
                label: 'Company_Employee_Designation_ID',
                name: "Company_Employee_Designation_ID",
                width: 100,
                editable: true,
                hidden: true
            },


            ],
            sortname: "Company_Employee_ID",
            sortorder: "asc",
            jsonReader:
                {
                    root: "rows",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow", 'hidden');
                $(".ui-jqgrid-bdiv").css("overflow-y", 'auto');
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
        });
        $("#TblEmployeeDetailsGrid").filterToolbar({
            resetIcon: "",
            defaultSearch: "cn",
            stringResult: true,
        });
    }
    function GetContratorDetailsGrid() {
        $("#TblContractorDetailsGrid").GridUnload();
        $("#TblContractorDetailsGrid").jqGrid({
            url: AbsolutePath('/Project/GetContratorDetails'),
            datatype: 'json',
            width: "500",
            height: '200',
            ignoreCase: true,
            viewrecords: true,
            rowList: [5, 10, 15, 20],
            rownumbers: true,
            rownumWidth: 50,
            pager: "#div_ContractorGridPager",
            rowNum: 20,
            //colNames: ["Select", "IC", "Name", "City", "RegionIC", "Region", "StateIC", "State", "Phone", "Email", "CountryIC", "ZipCode", "Address", "Code"],
            colModel: [{
                label: 'Select',
                name: "Select",
                width: 45,
                search: false,
                align: "center",
                formatter: function (a, b) {
                    return "<input type='radio' id='Contractor_" + b.rowId + "' name='ContratorBy' class='SelectRow' onclick='SelectContractor(" + b.rowId + ")' />"
                }
            },
            {
                label: 'ContractorIC',
                name: "ContractorIC",
                width: 50,
                align: 'right',
                editable: true,
                hidden: true,
            },

            {
                label: 'Contractor Name',
                name: "ContractorName",
                width: 100,
                align: 'left',
                editable: true,
                search: true,

            },
             {
                 label: 'IsActive',
                 name: "IsActive",
                 width: 100,
                 editable: true,
                 hidden: true
             },

            {
                label: 'Remarks',
                name: "Remarks",
                width: 100,
                editable: true,
                hidden: true
            },



            ],
            sortname: "ContractorIC",
            sortorder: "asc",
            loadonce: true,
            jsonReader:
                {
                    root: "rows",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow", 'hidden');
                $(".ui-jqgrid-bdiv").css("overflow-y", 'auto');
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
        });
        $("#TblContractorDetailsGrid").filterToolbar({
            resetIcon: "",
            stringResult: true,
            searchOnEnter: true,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],

            ignoreCase: true
        });
    }
    function GetContractorPersonDetails() {
        var ContracorICForajax = null;
        switch (ContractorType) {
            case "1":
                ContracorICForajax = $(txt_ContratorIC).val().trim();
                break;
            case "2":
                ContracorICForajax = $(txt_ElecContratorIC).val().trim();
                break;

        }
        $("#TblContractorPersonDetailsGrid").GridUnload();
        $("#TblContractorPersonDetailsGrid").jqGrid({
            url: AbsolutePath('/Project/GetContratorPersonDetails?ContratorIC=' + ContracorICForajax),
            datatype: 'json',
            ignoreCase: true,
            width: "700",
            height: '200',
            viewrecords: true,
            rowList: [5, 10, 15, 20],
            rownumbers: true,
            rownumWidth: 50,
            pager: "#div_ContractorPersonGridPager",
            rowNum: 20,
            //colNames: ["Select", "IC", "Name", "City", "RegionIC", "Region", "StateIC", "State", "Phone", "Email", "CountryIC", "ZipCode", "Address", "Code"],
            colModel: [{
                label: 'Select',
                name: "Select",
                width: 45,
                search: false,
                align: "center",
                formatter: function (a, b) {
                    return "<input type='radio' id='ContactPerson_" + b.rowId + "' name='ReqBy' class='SelectRow' onclick='SelectContractorPerson(" + b.rowId + ")' />"
                }
            },
            {
                label: 'ContractorSitePersonDetailsIC',
                name: "ContractorSitePersonDetailsIC",
                width: 50,
                align: 'right',
                editable: true,
                hidden: true,
            },
            {
                label: 'ContractorIC',
                name: "ContractorIC",
                width: 100,
                align: 'left',
                editable: true,
                hidden: true
            },
            {
                label: 'Contractor Name',
                name: "Name",
                width: 100,
                align: 'left',
                editable: true,

            },

            {
                label: 'Mobile #',
                name: "MobileNumber",
                width: 100,
                editable: true,
            },
             {
                 label: 'isActive',
                 name: "isActive",
                 width: 100,
                 editable: true,
                 hidden: true
             },
            {
                label: 'Remarks',
                name: "Remarks",
                width: 100,
                editable: true,
                hidden: true
            },


            ],
            sortname: "ContractorSitePersonDetailsIC",
            sortorder: "asc",
            jsonReader:
                {
                    root: "rows",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow", 'hidden');
                $(".ui-jqgrid-bdiv").css("overflow-y", 'auto');
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
        });
        $("#TblContractorPersonDetailsGrid").filterToolbar({
            resetIcon: "",
            defaultSearch: "cn",
            stringResult: true,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],

            ignoreCase: true
        });
    }
    //Function to load Region
    function GetStageNames() {
        $("#IdDdlPlantStage").empty();
        var regions = "<option value='0'>---Select---</option>";

        $.ajax({
            url: AbsolutePath('/Project/GetPlantStageNames'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.PlantStageIC + ">" + value.PlantStageName.toString() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.PlantStageIC + ">" + value.PlantStageName.toString() + "</option>";
                    }
                });
                $("#IdDdlPlantStage").append(regions);
            }
        });

    }
    $(".CheckContrator").blur(function () {

        var idval = $(this).attr('id');
        if ($("#" + idval).val().trim() != "") {
            $.ajax({
                url: AbsolutePath('/Project/CheckContractorExistsORNot'),
                type: 'GET',
                cache: false,
                async: false,
                datatype: "json",
                data: {
                    ContactName:
                        $("#" + idval).val().trim()
                },
                success: function (resdata, status, xhr) {

                    if (resdata == "1") {
                        if (EditClick == "1") {
                            switch (idval) {
                                case "IdTxtProjContractor":
                                    if (MechContractorNameadd.toString().toLowerCase() == $("#" + idval).val().trim().toString().toLowerCase()) {
                                        $(txt_ContratorIC).val(MechContractorNameaddIC);
                                    } else {
                                        $("#" + idval).val("");
                                        $(txt_ContratorIC).val("");
                                        AlertDialog("Contractor name is already exists", 'warning');
                                    }

                                    break;
                                case "IdTxtProjElectContractor":
                                    if (ElecContractorNameadd.toString().toLowerCase() == $("#" + idval).val().trim().toString().toLowerCase()) {
                                        $(txt_ElecContratorIC).val(ElecContractorNameaddIC);
                                    } else {
                                        $("#" + idval).val("");
                                        $(txt_ElecContratorIC).val("");
                                        AlertDialog("Contractor name is already exists", 'warning');
                                    }
                                    break;


                            }

                        } else {
                            $("#" + idval).val("");
                            AlertDialog("Contractor name is already exists", 'warning');
                        }

                    }
                }
            });
        }


    })
    function GetProjectCurrentStatusNames() {
        $("#ddl_ProjectCurrentStatus").empty();
        var regions = "<option value='0'>---Select---</option>";

        $.ajax({
            url: AbsolutePath('/Project/GetProjectCurrentStatusNames'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.ProjectCurrentStatusIC + ">" + value.ProjectCurrentStatusName.toString() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.ProjectCurrentStatusIC + ">" + value.ProjectCurrentStatusName.toString() + "</option>";
                    }
                });
                $("#ddl_ProjectCurrentStatus").append(regions);
            }
        });

    }
    function GetRegions() {
        var regions = "<option value='0'>---Select---</option>";
        $("#Sel_Region").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetRegions'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                console.log(resdata)
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        if (value.RefMasterDetail_Name.toString().toLowerCase() == "north" || value.RefMasterDetail_Name.toString().toLowerCase() == "south" || value.RefMasterDetail_Name.toString().toLowerCase() == "east" || value.RefMasterDetail_Name.toString().toLowerCase() == "west" || value.RefMasterDetail_Name.toString().toLowerCase() == "northeast" || value.RefMasterDetail_Name.toString().toLowerCase() == "southwest") {
                            regions = regions + "<option value=" + value.RefMasterDetail_ID + ">" + value.RefMasterDetail_Name.toString() + "</option>";
                        }

                    }
                    else {
                        if (value.RefMasterDetail_Name.toString().toLowerCase() == "north" || value.RefMasterDetail_Name.toString().toLowerCase() == "south" || value.RefMasterDetail_Name.toString().toLowerCase() == "east" || value.RefMasterDetail_Name.toString().toLowerCase() == "west" || value.RefMasterDetail_Name.toString().toLowerCase() == "northeast" || value.RefMasterDetail_Name.toString().toLowerCase() == "southwest") {
                            regions = regions + "<option value=" + value.RefMasterDetail_ID + ">" + value.RefMasterDetail_Name.toString() + "</option>";
                        }

                    }
                });
                $("#Sel_Region").append(regions);
            }
        });
    }
    $("#Sel_Region").change(function () {
        GetStates();
    })
    function GetStates() {
        var regions = "<option value='0'>---Select---</option>";
        $("#ddl_States").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetStates?RegionIC=' + $("#Sel_Region").children(":selected").val() + '&&CountryIC=' + $("#spn_CountryID").html()),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.State_ID + ">" + value.State_Name + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.State_ID + ">" + value.State_Name.toString() + "</option>";
                    }
                });
                $("#ddl_States").append(regions);
            }
        });
    }
    //Function to load Plantypes
    function GetPlantTypes() {
        var regions = "<option value='0'>---Select---</option>";
        $("#IdDdlPlantType").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetPlanttypes'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.Plant_typeId + ">" + value.Plant_typeName.toString().trim() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.Plant_typeId + ">" + value.Plant_typeName.toString().trim() + "</option>";
                    }
                });
                $("#IdDdlPlantType").append(regions);
            }
        });
    }
    function GetOrderTerms() {
        var regions = "<option value='0'>---Select---</option>";
        $("#txt_OrderTerms").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetOrderTermsNames'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.OrderTermsIC + ">" + value.OrderTermsName.toString().trim() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.OrderTermsIC + ">" + value.OrderTermsName.toString().trim() + "</option>";
                    }
                });
                $("#txt_OrderTerms").append(regions);
            }
        });
    }
    function GetZeroDateMileStoneValues() {
        var regions = "<option value='0'>---Select---</option>";
        $("#IdTxtProjZeroDateMilestone").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetZeroMileStoneDropDownValues'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.ZeroDateMileStoneId + ">" + value.ZeroDateMileStoneName.toString().trim() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.ZeroDateMileStoneId + ">" + value.ZeroDateMileStoneName.toString().trim() + "</option>";
                    }
                });
                $("#IdTxtProjZeroDateMilestone").append(regions);
            }
        });
    }
    function GetSupplyStatusnames() {
        var regions = "<option value='0'>---Select---</option>";
        $("#IdTxtProjSupplyStatusForAccordion").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetSupplyStatusNames'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.SupplyStatusId + ">" + value.SupplyStatusName.toString().trim() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.SupplyStatusId + ">" + value.SupplyStatusName.toString().trim() + "</option>";
                    }
                });
                $("#IdTxtProjSupplyStatusForAccordion").append(regions);
            }
        });
    }
    function GetProjectStatusnames() {
        var regions = "<option value='0'>---Select---</option>";
        $("#IdDdlProjectStatus").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetProjectStatusStatusNames'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.ProjectStatusId + ">" + value.ProjectStatusName.toString().trim() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.ProjectStatusId + ">" + value.ProjectStatusName.toString().trim() + "</option>";
                    }
                });
                $("#IdDdlProjectStatus").append(regions);
            }
        });
    }
    function GetCurrentStatusnames() {
        var regions = "<option value='0'>---Select---</option>";
        $("#IdTxtProjCurrentStatus").empty();
        $.ajax({
            url: AbsolutePath('/Project/GetCurrentStatusStatusNames'),
            type: 'GET',
            cache: false,
            async: false,
            datatype: "json",
            success: function (resdata, status, xhr) {
                $.each(resdata, function (index, value) {
                    if (index == 0) {
                        regions = regions + "<option value=" + value.InstallationCurrentStatusId + ">" + value.InstallationCurrentStatusName.toString().trim() + "</option>";
                    }
                    else {
                        regions = regions + "<option value=" + value.InstallationCurrentStatusId + ">" + value.InstallationCurrentStatusName.toString().trim() + "</option>";
                    }
                });
                $("#IdTxtProjCurrentStatus").append(regions);
            }
        });
    }
    
    $("#IdBtnProjectDraft").click(function ()
    {
        if (Usertype.toString().toUpperCase() == "PT" || Usertype.toString().toUpperCase() == "PM") {
            ProjectValidationForHeaderDetailsForSaveFOrProjectTeam();
            if ($("#IdDdlProjectStatus option:selected").val() == "0")
            {
                ProjectStatusIC = "1";
            } else
            {
                ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();

            }

            if (validation == "1")
            {
                SavePlantDetails();
            }
        } else
        {
            if (Usertype.toString().toUpperCase() == "HOD")
            {
                ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();
            } else if (Usertype.toString().toUpperCase() == "SICRIC")
            {
                if ($("#IdDdlProjectStatus option:selected").val() == "2")
                {
                    ProjectStatusIC = "2";
                } else
                {
                    ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();

                }

            } else if (Usertype.toString().toUpperCase() == "67") {

                if ($("#IdDdlProjectStatus option:selected").val() > "3")
                {
                    ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();
                }
                else
                {
                    ProjectStatusIC = "3";
                }
             
            }

            SavePlantDetails();
        }

    });
    $("#IdBtnProjectSubmit").click(function ()
    {

        if (Usertype.toString().toUpperCase() == "PM") {
            ProjectValidationForHeaderDetailsForSubmitFOrProjectTeam();
            if (validation == "1") {
                CheckPlantDetailsLenth();
                if (validation == "1") {
                    if ($("#IdInstalltionEngineer").val().trim() != "") {

                        if ($("#IdDdlProjectStatus option:selected").val() == "0" || $("#IdDdlProjectStatus option:selected").val() == "1" || $("#IdDdlProjectStatus option:selected").val() == "0" || $("#IdDdlProjectStatus option:selected").val() == "2")
                        {
                            ProjectStatusIC = "3";
                        } else
                        {
                            ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();

                        }




                    }
                    else
                    {

                        if ($("#IdDdlProjectStatus option:selected").val() == "0" || $("#IdDdlProjectStatus option:selected").val() == "1") {
                            ProjectStatusIC = "2";
                        } else {
                            ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();

                        }


                    }

                    SavePlantDetails();
                } else {

                }
            }
        } else if (Usertype.toString().toUpperCase() == "SICRIE") {
            ProjectValidationForHeaderDetailsForSubmitFOrInstallationTeam();
            if (validation == "1") {

                if ($("#IdDdlProjectStatus option:selected").val() == "1" || $("#IdDdlProjectStatus option:selected").val() == "2") {
                    ProjectStatusIC = "3";
                } else {
                    ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();

                }


                SavePlantDetails();
            }
        } else if (Usertype.toString().toUpperCase() == "67")
        {
            if ($("#IdSevicenEngineer").val().trim() != "" || $("#IdsecSevicenEngineer").val().trim() != "")
            {
                $("#IdSevicenEngineer").css('border', '1px solid gray');
                $("#IdsecSevicenEngineer").css('border', '1px solid gray');
                validation = "1";
                ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();
                SavePlantDetails();
            } else
            {
                $("#IdSevicenEngineer").css('border', '1px solid red');
                AlertDialog("Please fill mandatory field(s)", "warning");
                validation = "0";

            }

        } else if (Usertype.toString().toUpperCase() == "PT")
        {
            ProjectValidationForHeaderDetailsForSaveFOrProjectTeam();
            if ($("#IdDdlProjectStatus option:selected").val() == "0")
            {
                ProjectStatusIC = "1";
            } else
            {
                ProjectStatusIC = $("#IdDdlProjectStatus option:selected").val();

            }

            if (validation == "1") {
                SavePlantDetails();
            }

        }

    });
    function ProjectValidationForHeaderDetailsForSaveFOrProjectTeam() {
        var controlslen2 = 0;
        var Length2 = $(".ProjectSaveValidationPT").length;
        var count2 = $(".ProjectSaveValidationPT").length;
        for (var j = 0; j < count2; j++) {
            var num = $(".ProjectSaveValidationPT")[j];
            if ($(num).val().trim() != "" && $(num).val() != "0") {
                $(num).css('border', '');
                controlslen2++;
            }
            else {
                $(num).css('border', 'solid 1px red');
            }
        }
        if (controlslen2 == Length2) {

            validation = 1;

        } else {
            validation = 0;
            AlertDialog("Please fill mandatory field(s)", "warning");
        }
    }

    $("#IdBtnProjectCancel,#btnCLoseX").click(function () {
        for (var i = RowIDCollection_Add.length; i > 0; i--) {
            RowIDCollection_Add.pop(i);
        }
        for (var j = Arr_Edit.length; j > 0; j--) {
            Arr_Edit.pop(j);
        }
        // window.location.reload();
    })
    function ProjectValidationForHeaderDetailsForSubmitFOrProjectTeam() {

        var controlslen2 = 0;
        var Length2 = $(".ProjectSubmitValidationPT").length;
        var count2 = $(".ProjectSubmitValidationPT").length;
        for (var j = 0; j < count2; j++) {
            var num = $(".ProjectSubmitValidationPT")[j];
            if ($(num).val().trim() != "" && $(num).val() != "0") {
                $(num).css('border', '');
                controlslen2++;
            }
            else {
                $(num).css('border', 'solid 1px red');
            }
        }
        if (controlslen2 == Length2) {
            ProjectValidationForHeaderDetailsForProjectTeamredctls();
            if (validation == "1") {
                if ($("#IdSevicenEngineer").val().trim() != "" || $("#IdsecSevicenEngineer").val().trim() != "" || $("#IdSitInCharge").val().trim() != "") {
                    ProjectValidationForHeaderDetailsForSubmitFOrInstallationTeam();

                } else {
                    validation = 1;
                }
            }



        } else {
            validation = 0;
            AlertDialog("Please fill mandatory field(s)", "warning");
        }
    }
    function ProjectValidationForHeaderDetailsForProjectTeamredctls() {

        var controlslen2 = 0;
        var Length2 = $(".ProjectSubmitValidationPTred").length;
        var count2 = $(".ProjectSubmitValidationPTred").length;
        for (var j = 0; j < count2; j++) {
            var num = $(".ProjectSubmitValidationPTred")[j];
            if ($(num).val().trim() != "" && $(num).val() != "0") {
                $(num).css('border', '');
                controlslen2++;
            }
            else {
                $(num).css('border', 'solid 1px red');
            }
        }
        if (controlslen2 == Length2) {

            validation = 1;
        } else {
            validation = 0;
            AlertDialog("Please fill mandatory field(s)", "warning");
        }
    }
    function ProjectValidationForHeaderDetailsForSubmitFOrInstallationTeam() {
        var controlslen2 = 0;
        var Length2 = $(".ProjectSubmitValidationIT").length;
        var count2 = $(".ProjectSubmitValidationIT").length;
        for (var j = 0; j < count2; j++) {
            var num = $(".ProjectSubmitValidationIT")[j];
            if ($(num).val().trim() != "" && $(num).val() != "0") {
                $(num).css('border', '');
                controlslen2++;
            }
            else {
                $(num).css('border', 'solid 1px red');
            }
        }
        if (controlslen2 == Length2) {
            validation = 1;

        } else {
            validation = 0;
            AlertDialog("Please fill mandatory field(s)", "warning");
        }
    }

    function PlantDetailsGridGridValidationFun()
    {

        var rowid = $("#IdTblPlantGrid").getDataIDs();
        if (rowid.length > 0)
        {
            for (var i = 0; i < rowid.length; i++) {
                var Item_name = "";
                var Model = "";
                var NumberofMotors = "";
                var NumberofStages = "";
                if ($("#" + rowid[i] + "_ItemTypeName").val() != undefined) {
                    Item_name = $("#" + rowid[i] + "_ItemTypeName").val().toString().trim();
                } else {
                    Item_name = undefined;
                }
                if ($("#" + rowid[i] + "_SIC_Models_Name").val() != undefined) {
                    Model = $("#" + rowid[i] + "_SIC_Models_Name").val().toString().trim();
                } else {
                    Model = undefined;
                }
                if ($("#" + rowid[i] + "_No_Motors").val() != undefined) {
                    NumberofMotors = $("#" + rowid[i] + "_No_Motors").val().toString().trim();
                } else {
                    NumberofMotors = undefined;
                }
                if ($("#" + rowid[i] + "_PlantStageNumber").val() != undefined) {
                    NumberofStages = $("#" + rowid[i] + "_PlantStageNumber").val().toString().trim();
                } else
                {
                    NumberofStages = undefined;
                }
                if ($("#" + rowid[i] + "_ConveyorLength").val() != undefined) {
                    Conveyorlength = $("#" + rowid[i] + "_ConveyorLength").val().toString().trim();
                } else {
                    Conveyorlength = undefined;
                }
                if ($("#" + rowid[i] + "_ConveyorWidth").val() != undefined) {
                    Conveyorwidth = $("#" + rowid[i] + "_ConveyorWidth").val().toString().trim();
                } else {
                    Conveyorwidth = undefined;
                }
                var colNames = ["ItemTypeName", "SIC_Models_Name", "No_Motors", "PlantStageNumber"];

                if (Item_name != "0" && Model != "0" && NumberofMotors != "" && NumberofStages != "0") {
                    PlantDetailsGridGridValidation = "1";

                    $("#" + rowid[i] + "_ItemTypeName").css('border', '1px solid gray');
                    $("#" + rowid[i] + "_SIC_Models_Name").css('border', '1px solid gray');
                    $("#" + rowid[i] + "_No_Motors").css('border', '1px solid gray');
                    $("#" + rowid[i] + "_PlantStageNumber").css('border', '1px solid gray');
                }
                else {
                    for (var j = 0; j < colNames.length; j++) {
                        if ($("#" + rowid[i] + "_" + colNames[j]).val() != undefined) {
                            if ($("#" + rowid[i] + "_" + colNames[j]).val().toString().trim() == "" || $("#" + rowid[i] + "_" + colNames[j]).val() == "0") {
                                $("#" + rowid[i] + "_" + colNames[j]).css('border', '1px solid red');
                            }
                        }
                    }
                    PlantDetailsGridGridValidation = "0";
                    AlertDialog("Please fill all mandatory field(s)", "warning");
                    break;
                }
            }
        } else {
            PlantDetailsGridGridValidation = "1";
        }

    }
    function CheckPlantDetailsLenth() {
        var len = $("#IdTblPlantGrid").getDataIDs().length;
        if (parseInt(len) > 0) {
            validation = 1;
        } else {
            validation = 0;
        }

        if (validation == 0) {
            AlertDialog("Please enter plant details", "warning");
        }
    }
    function SavePlantDetails() {
        var json_data_PlantDetails_Add = [];
        var Header_JSONDATA = [];
        var Final_json_data = [];
        var jsonData_PlantDetails_edit = [];
        if (EditClick == "0") {
            //Add Mode
            //PlantValidationForHeaderDetails();
            if (validation == 1) {
                if (Usertype.toString().toLowerCase() == "pm") { PlantDetailsGridGridValidationFun(); } else {
                    PlantDetailsGridGridValidation = "1";
                }
                //
                if (PlantDetailsGridGridValidation == "1") {
                    if (RowIDCollection_Add.length > 0) {

                        $.each(RowIDCollection_Add, function (key, value) {

                            //   $("#IdTblPlantGrid").saveRow(value);
                            var Rowdata = $("#IdTblPlantGrid").getRowData(value);

                            var Itemtypename = $("#" + value + "_ItemTypeName").val();
                            var Model = $("#" + value + "_SIC_Models_Name").val();
                            var NumbeOFMotors = $("#" + value + "_No_Motors").val();
                            var NumbeOFStages = $("#" + value + "_PlantStageNumber").val();
                            var Conveyorlength = $("#" + value + "_ConveyorLength").val();
                            var Conveyorwidth = $("#" + value + "_ConveyorWidth").val();
                            //Rowdata["SIC_Models_Name"];

                            var json_res = {

                                ItemTypeIC: Itemtypename,
                                ModelID: Model,
                                numberofmotors: NumbeOFMotors,
                                StageID: NumbeOFStages,
                                Conveyorlength: Conveyorlength,
                                Conveyorwidth: Conveyorwidth
                            }
                            json_data_PlantDetails_Add.push(json_res);
                        })
                    }

                    var Json_header_dat = {
                        AddOrUpdate: '1',
                        LoggedPersonIC: $("#spn_loggedinID").html(),
                        ProjectIC: ProjectID,
                        ProjectName: $("#IdTxtProjName").val().trim(),
                        CreatedbyIC: $("#txt_creatbyIC").val().trim(),
                        CreatedDate: $("#IdTxtProjCreatedDate").val().trim(),
                        RegionIC: $("#Sel_Region option:selected").val(),
                        StateIC: $("#ddl_States option:selected").val(),
                        AssignToRIC: $("#txt_RIEIC").val(),
                        DispatcherIC: $("#txt_DispatcherIC").val(),
                        PlanttypeIC: $("#IdDdlPlantType option:selected").val(),
                        StageIC: $("#IdDdlPlantStage option:selected").val(),
                        Location: $(IdTxtProjLocation).val().trim(),
                        IsActive: $("#IdTxtProjIsActive").is(":checked") ? "1" : "0",
                        ProjectCode: $(IdTxtProjCodeForAccordion).val().trim(),
                        ClientOwner: $("#IdTxtProjClient").val().trim(),
                        ClientContactNO: $("#IdTxtProjClientContactNo").val().trim(),
                        SiteClientName: $("#IdTxtProjSiteClientName").val().trim(),
                        SiteClientMobile: $(IdTxtProjSiteClientMobile).val().trim(),
                        ProjectManagerIC: $(txt_PmgrIC).val().trim(),

                        SONumber: $("#IdTxtSONumber").val().trim(),
                        SOHFMDate: $("#txt_SOorHFMDate").val().trim(),
                        ShiptoCode: $("#IdTxtShiptoCode").val().trim(),
                        SoldToCOde: $("#IdTxtSoldToCode").val().trim(),
                        ServiceNetwrokNumber: $("#IdTxtServiceNetworkNo").val().trim(),

                        PODate: $(IdTxtPODate).val().trim(),
                        TourWBS: $(txt_TourWBS).val().trim(),
                        POReceivedDate: $(txtPOReceivedDate).val().trim(),
                        //OrderValue: $(txt_OrderValue).val().trim(),
                        OrderTerms: $("#txt_OrderTerms option:selected").val(),
                      //  BasicValue: $(txtBasicValue).val().trim(),
                      //  CreditNoteValue: $(txt_CreditNotValue).val().trim(),
                       // CreditNoteNo: $(txt_CreditCoteNo).val().trim(),
                        SupplyComDate: $(txtSupplyCompletionDate).val().trim(),
                        ProjectCurrentStatusIC: $("#ddl_ProjectCurrentStatus option:selected").val(),
                        LOIDate: $("#IdTxtLOIDate").val().trim(),
                        DODate: $("#IdTxtProjDODateForAccordion").val().trim(),
                        CINoteReceiviedDate: $("#IdTxtProjClNoteRecievedForAccordion").val().trim(),
                        LayoutApprovalDate: $("#IdTxtProjLayoutApproval").val().trim(),
                        DispatchClearanceDate: $("#IdTxtProjSiteClearanceForAccordion").val().trim(),
                        ContractualDeliveryDay: $("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim(),

                        ZerMileStoneIC: $("#IdTxtProjZeroDateMilestone option:selected").val(),
                        ContractulDelivery: $("#IdTxtProjContractualDeliveryForAccordion").val().trim(),
                        SupplyStausIC: $("#IdTxtProjSupplyStatusForAccordion option:selected").val().trim(),
                        TargetDate: $("#IdTxtProjTargetDate").val().trim(),
                        ContractulRemaining: $("#IdTxtProjContractualRemainingDaysForAccordion").val().trim(),
                        IdleManDays: $("#IdTxtProjIdleManpowerDaysForAccordion").val().trim(),
                        ProjectStatusIC: ProjectStatusIC,
                        SiteInCharge: $("#IdSitInCharge").val().trim(),
                        InstallationCSIC: $("#IdTxtProjCurrentStatus option:selected").val(),
                        MobIntimationReceived: $(txtMobilZationIntimationReceived).is(":checked") ? "1" : "0",

                        CommissionedDate: $("#IdTxtProjComissionedDateForAccordion").val().trim(),
                        MobilizationDate: $("#IdTxtProjMobilizationDate").val().trim(),
                        IEIC: $("#txt_IEIC").val().trim(),
                        MechContractorIC: $("#txt_ContratorIC").val().trim(),
                        MechContractorName: $(IdTxtProjContractor).val().trim(),
                        MechConPersonIC: $("#txt_ContractorPersonIC").val().trim(),
                        MechConPersonName: $(IdTxtProjContractorPersonAtSite).val().trim(),
                        MechConPersonMobile: $("#IdTxtProjContractorSiteMobile").val().trim(),


                        ElectContractorIC: $("#txt_ElecContratorIC").val().trim(),
                        ElectContractorName: $(IdTxtProjElectContractor).val().trim(),
                        ElectConPersonIC: $("#txt_ElecContractorPersonIC").val().trim(),
                        ElectConPersonName: $(IdTxtProjContractorPersonAtSite).val().trim(),
                        ElectConPersonMobile: $("#IdTxtProjElectContractorSiteMobile").val().trim(),
                        ClientDGDate: $("#IdTxtProjClientDGReadinessDateForAccordion").val().trim(),
                        InComingCableReadinessDate: $("#IdTxtProjInComingCableReadinessDateForAccordion").val().trim(),


                        NoOFCivilGudance: $("#IdTxtProjClientNoofCivilGuidanceVisited").val().trim(),
                        FirstMobDate: $("#IdTxtProjFirstMobilizeDate").val().trim(),
                        FirstDeMobDate: $("#IdTxtProjFirstDeMobilizeDateForAccordion").val().trim(),
                        FirstDeReason: $("#IdTxtProjFirstDeMobilizeReasonForAccordion").val().trim(),
                        SecondMobDate: $("#IdTxtProjSecondMobilizationDateForAccordion").val().trim(),

                        SecondDeMObDate: $("#IdTxtProjSecondDeMobilizationDate").val().trim(),
                        SecondDeMobReason: $("#IdTxtProjSecondDeMobilizeReasonForAccordion").val().trim(),
                        ThirdMobDate: $("#IdTxtProjThirdMobilizationDateForAccordion").val().trim(),
                        ThirdDeMObDate: $("#IdTxtProjThirdDeMobilizationDateForAccordion").val().trim(),
                        ThirdDeMobReason: $("#IdTxtProjThirdDeMobilizeReasonForAccordion").val().trim(),

                        FourthMobDate: $("#IdTxtProjfouthMobilizationDateForAccordion").val().trim(),
                        FourthDeMobDate: $("#IdTxtProjforthDeMobilizationDateForAccordion").val().trim(),
                        FourthDeMobReason: $("#IdTxtProjforthDeMobilizeReasonForAccordion").val().trim(),
                        DaysTillNow: $("#IdTxtProjDaysTillNow").val().trim(),
                        RemainingDays: $("#IdTxtProjRemainingDaysForAccordion").val().trim(),
                        IdleHyDraDays: $("#IdTxtProjIdleHydraDaysForAccordion").val().trim(),
                        PrimaryServiceEngineerIC: $("#txt_PrimaryServiceEngineerIC").val().trim(),
                        SecondaryServiceEngineerIC: $("#txt_SecondaryServiceEngineerIC").val().trim(),
                     //   NTvalue: $(IdTxtNTValue).val().trim(),
                        ICnDate: $(IdTxtICNDate).val().trim(),
                        PrimaryDesc: $(txt_PrimaryDesc).val().trim(),
                        SecondaryDesc: $(txt_SecDesc).val().trim(),
                        TerioryDesc: $(txt_TeritoryDesc).val().trim(),
                        OthersDesc: $(txt_OthersDesc).val().trim(),
                        Sharepontlink: $(txt_SharePointlink).val().trim(),
                        ClientEmailId: $(IdTxtProjClientEmialID).val().trim(),
                        GSTNumber: $("#IdTxt_GSTNumberShipping").val().trim(),
                        InvoiceAddress: $("#IdTxt_InvoiceAddress").val().trim(),
                        Hypothecation: $(txtHypothecation).val().trim(),
                        ManualDispatchAddress: $("#txtManualDisptachAddress").val().trim(),
                        SiteClient_EmailID: $(IdTxtProjClientEmialID).val().trim(),
                        PONumber: $(IdTxt_Pono).val().trim(),
                        TransportationWBS: $(IdTxt_TransportationWBS).val().trim(),
                        NTMonth: $(IdTxtNTMonth).val().trim(),

                        //=================================== pavan kumar v ===================================//

                        Warranty_wbs: $("#IdTxtWarrantywbs").val().trim(),
                        Projected_Contractual_Delivery: $("#txt_Projected_CDD").val().trim(),
                        Buffer_days: $("#IdTxtBufferdays").val().trim(),
                        Lubricant: "",
                        Grease: "",
                        //Lubricant: $("#txtLubricant").val().trim(),
                        // Grease: $("#txtGrease").val().trim(),

                        //=================================== pavan kumar v 3rd June ===================================//
                        Travel_WBS: $("#txt_TravelWBS").val().trim(),
                        Txt_SFN: $("#IdTxtSFN").val().trim(),
                        SI_Remark: $("#IdTxt_SIRemark").val().trim(),
                        GSTSameAsShipping: $("#chk_GST_Shipping").is(":checked") ? "1" : "0",
                        GST_Number_Billing: $("#IdTxt_GSTNumberBilling").val().trim(),
                        Contractual_DeliveryDate_Others:Contractual_DeliveryDate_Othersvalue
                    }

                    Header_JSONDATA.push(Json_header_dat);
                    var json_FinalData_insert = {
                        HeaderDatalenght: Header_JSONDATA.length,
                        ProjectHeaderDetails: Header_JSONDATA,
                        PlantDetailsAddlenght: json_data_PlantDetails_Add.length,
                        PlantDetailsAddDetails: json_data_PlantDetails_Add,
                        PlantDetailsEditlenght: jsonData_PlantDetails_edit.length,
                        PlantDetailsEditDetails: jsonData_PlantDetails_edit
                    }
                    console.log(json_FinalData_insert)
                    $.ajax({
                        url: AbsolutePath('/Project/SaveProjectDetailsInfo'),
                        type: "POST", datatype: 'json', data: json_FinalData_insert,
                        success: function (resp) {
                            if (resp == "1") {
                                $("#IdDivModelViewProjects").modal("hide");
                                MainLandingGridForProjects();
                                // HideLoadingImg();
                                AlertDialog("Saved sucessfully", "success");
                                for (var i = RowIDCollection_Add.length; i > 0; i--) {
                                    RowIDCollection_Add.pop(i);
                                }
                                for (var j = Arr_Edit.length; j > 0; j--) {
                                    Arr_Edit.pop(j);
                                }
                                for (var k = Header_JSONDATA.length; k > 0; k--) {
                                    Header_JSONDATA.pop(k);
                                }
                            } else {
                                // HideLoadingImg();
                                AlertDialog("Fail to Save", "warning");
                            }
                        }
                    })

                } else {

                }


            } else {
                AlertDialog("Please fill all mandatory field(s)", "warning");
            }

        } else {
            // Update Mode

            // PlantValidationForHeaderDetails();
            if (validation == 1) {
                if (Usertype.toString().toLowerCase() == "pm") { PlantDetailsGridGridValidationFun(); } else {
                    PlantDetailsGridGridValidation = "1";
                }

                if (PlantDetailsGridGridValidation == "1") {


                    if (RowIDCollection_Add.length > 0) {

                        $.each(RowIDCollection_Add, function (key, value) {

                            //$("#IdTblPlantGrid").saveRow(value);
                            var Rowdata = $("#IdTblPlantGrid").getRowData(value);
                            var Itemtypename = $("#" + value + "_ItemTypeName").val();
                            var Model = $("#" + value + "_SIC_Models_Name").val();
                            var NumbeOFMotors = $("#" + value + "_No_Motors").val();
                            var NumbeOFStages = $("#" + value + "_PlantStageNumber").val();
                            //Rowdata["SIC_Models_Name"];

                            var json_res = {

                                ItemTypeIC: Itemtypename,
                                ModelID: Model,
                                numberofmotors: NumbeOFMotors,
                                StageID: NumbeOFStages
                            }
                            json_data_PlantDetails_Add.push(json_res);
                        })
                    }

                    if (Arr_Edit.length > 0) {
                        $.each(Arr_Edit, function (key, value) {

                            // $("#IdTblPlantGrid").saveRow(value);
                            var Rowdata = $("#IdTblPlantGrid").getRowData(value);
                            var Itemtypename = $("#" + value + "_ItemTypeName").val();
                            var Model = $("#" + value + "_SIC_Models_Name").val();
                            var PlantDetailsID = $("#" + value + "_ProjectPlantDetailsId").val();
                            var NumbeOFMotors = $("#" + value + "_No_Motors").val();
                            //Rowdata["ProjectPlantDetailsId"];
                            var NumbeOFStages = $("#" + value + "_PlantStageNumber").val();
                            //Rowdata["SIC_Models_Name"];
                            var Projectic = $("#" + value + "_ProjectId").val();
                            var Conveyorlength = $("#" + value + "_ConveyorLength").val();
                            var Conveyorwidth = $("#" + value + "_ConveyorWidth").val();
                            //Rowdata["ProjectId"];
                            var json_res = {
                                ProjectID: Projectic,
                                PlantDetailerID: PlantDetailsID,
                                ItemTypeIC: Itemtypename,
                                ModelID: Model,
                                numberofmotors: NumbeOFMotors,
                                StageID: NumbeOFStages,
                                Conveyorlength: Conveyorlength,
                                Conveyorwidth: Conveyorwidth
                            }
                            jsonData_PlantDetails_edit.push(json_res);
                        })
                    }


                    var Json_header_dat =
                        {
                            AddOrUpdate: '0',
                            LoggedPersonIC: $("#spn_loggedinID").html(),
                            ProjectIC: ProjectID,
                            ProjectName: $("#IdTxtProjName").val().trim(),
                            CreatedbyIC: $("#txt_creatbyIC").val().trim(),
                            CreatedDate: $("#IdTxtProjCreatedDate").val().trim(),
                            RegionIC: $("#Sel_Region option:selected").val(),
                            StateIC: $("#ddl_States option:selected").val(),
                            AssignToRIC: $("#txt_RIEIC").val(),
                            DispatcherIC: $("#txt_DispatcherIC").val(),
                            PlanttypeIC: $("#IdDdlPlantType option:selected").val(),
                            StageIC: $("#IdDdlPlantStage option:selected").val(),
                            Location: $(IdTxtProjLocation).val().trim(),
                            IsActive: $("#IdTxtProjIsActive").is(":checked") ? "1" : "0",
                            ProjectCode: $(IdTxtProjCodeForAccordion).val().trim(),
                            ClientOwner: $("#IdTxtProjClient").val().trim(),
                            ClientContactNO: $("#IdTxtProjClientContactNo").val().trim(),
                            SiteClientName: $("#IdTxtProjSiteClientName").val().trim(),
                            SiteClientMobile: $(IdTxtProjSiteClientMobile).val().trim(),
                            ProjectManagerIC: $(txt_PmgrIC).val().trim(),

                            SONumber: $("#IdTxtSONumber").val().trim(),
                            SOHFMDate: $("#txt_SOorHFMDate").val().trim(),
                            ShiptoCode: $("#IdTxtShiptoCode").val().trim(),
                            SoldToCOde: $("#IdTxtSoldToCode").val().trim(),
                            ServiceNetwrokNumber: $("#IdTxtServiceNetworkNo").val().trim(),

                            PODate: $(IdTxtPODate).val().trim(),
                            TourWBS: $(txt_TourWBS).val().trim(),
                            POReceivedDate: $(txtPOReceivedDate).val().trim(),
                           // OrderValue: $(txt_OrderValue).val().trim(),
                            OrderTerms: $("#txt_OrderTerms option:selected").val(),
                          //  BasicValue: $(txtBasicValue).val().trim(),
                         //   CreditNoteValue: $(txt_CreditNotValue).val().trim(),
                          //  CreditNoteNo: $(txt_CreditCoteNo).val().trim(),
                            SupplyComDate: $(txtSupplyCompletionDate).val().trim(),
                            ProjectCurrentStatusIC: $("#ddl_ProjectCurrentStatus option:selected").val(),
                            LOIDate: $("#IdTxtLOIDate").val().trim(),
                            DODate: $("#IdTxtProjDODateForAccordion").val().trim(),
                            CINoteReceiviedDate: $("#IdTxtProjClNoteRecievedForAccordion").val().trim(),
                            LayoutApprovalDate: $("#IdTxtProjLayoutApproval").val().trim(),
                            DispatchClearanceDate: $("#IdTxtProjSiteClearanceForAccordion").val().trim(),
                            ContractualDeliveryDay: $("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim(),

                            ZerMileStoneIC: $("#IdTxtProjZeroDateMilestone option:selected").val(),
                            ContractulDelivery: $("#IdTxtProjContractualDeliveryForAccordion").val().trim(),
                            SupplyStausIC: $("#IdTxtProjSupplyStatusForAccordion option:selected").val().trim(),
                            TargetDate: $("#IdTxtProjTargetDate").val().trim(),
                            ContractulRemaining: $("#IdTxtProjContractualRemainingDaysForAccordion").val().trim(),
                            IdleManDays: $("#IdTxtProjIdleManpowerDaysForAccordion").val().trim(),
                            ProjectStatusIC: ProjectStatusIC,
                            SiteInCharge: $("#IdSitInCharge").val().trim(),
                            InstallationCSIC: $("#IdTxtProjCurrentStatus option:selected").val(),
                            MobIntimationReceived: $(txtMobilZationIntimationReceived).is(":checked") ? "1" : "0",

                            CommissionedDate: $("#IdTxtProjComissionedDateForAccordion").val().trim(),
                            MobilizationDate: $("#IdTxtProjMobilizationDate").val().trim(),
                            IEIC: $("#txt_IEIC").val().trim(),
                            MechContractorIC: $("#txt_ContratorIC").val().trim(),
                            MechContractorName: $(IdTxtProjContractor).val().trim(),
                            MechConPersonIC: $("#txt_ContractorPersonIC").val().trim(),
                            MechConPersonName: $(IdTxtProjContractorPersonAtSite).val().trim(),
                            MechConPersonMobile: $("#IdTxtProjContractorSiteMobile").val().trim(),


                            ElectContractorIC: $("#txt_ElecContratorIC").val().trim(),
                            ElectContractorName: $(IdTxtProjElectContractor).val().trim(),
                            ElectConPersonIC: $("#txt_ElecContractorPersonIC").val().trim(),
                            ElectConPersonName: $(IdTxtProjContractorPersonAtSite).val().trim(),
                            ElectConPersonMobile: $("#IdTxtProjElectContractorSiteMobile").val().trim(),
                            ClientDGDate: $("#IdTxtProjClientDGReadinessDateForAccordion").val().trim(),
                            InComingCableReadinessDate: $("#IdTxtProjInComingCableReadinessDateForAccordion").val().trim(),


                            NoOFCivilGudance: $("#IdTxtProjClientNoofCivilGuidanceVisited").val().trim(),
                            FirstMobDate: $("#IdTxtProjFirstMobilizeDate").val().trim(),
                            FirstDeMobDate: $("#IdTxtProjFirstDeMobilizeDateForAccordion").val().trim(),
                            FirstDeReason: $("#IdTxtProjFirstDeMobilizeReasonForAccordion").val().trim(),
                            SecondMobDate: $("#IdTxtProjSecondMobilizationDateForAccordion").val().trim(),

                            SecondDeMObDate: $("#IdTxtProjSecondDeMobilizationDate").val().trim(),
                            SecondDeMobReason: $("#IdTxtProjSecondDeMobilizeReasonForAccordion").val().trim(),
                            ThirdMobDate: $("#IdTxtProjThirdMobilizationDateForAccordion").val().trim(),
                            ThirdDeMObDate: $("#IdTxtProjThirdDeMobilizationDateForAccordion").val().trim(),
                            ThirdDeMobReason: $("#IdTxtProjThirdDeMobilizeReasonForAccordion").val().trim(),

                            FourthMobDate: $("#IdTxtProjfouthMobilizationDateForAccordion").val().trim(),
                            FourthDeMobDate: $("#IdTxtProjforthDeMobilizationDateForAccordion").val().trim(),
                            FourthDeMobReason: $("#IdTxtProjforthDeMobilizeReasonForAccordion").val().trim(),
                            DaysTillNow: $("#IdTxtProjDaysTillNow").val().trim(),
                            RemainingDays: $("#IdTxtProjRemainingDaysForAccordion").val().trim(),
                            IdleHyDraDays: $("#IdTxtProjIdleHydraDaysForAccordion").val().trim(),
                            PrimaryServiceEngineerIC: $("#txt_PrimaryServiceEngineerIC").val().trim(),
                            SecondaryServiceEngineerIC: $("#txt_SecondaryServiceEngineerIC").val().trim(),
                          //  NTvalue: $(IdTxtNTValue).val().trim(),
                            ICnDate: $(IdTxtICNDate).val().trim(),
                            PrimaryDesc: $(txt_PrimaryDesc).val().trim(),
                            SecondaryDesc: $(txt_SecDesc).val().trim(),
                            TerioryDesc: $(txt_TeritoryDesc).val().trim(),
                            OthersDesc: $(txt_OthersDesc).val().trim(),
                            Sharepontlink: $(txt_SharePointlink).val().trim(),
                            ClientEmailId: $(IdTxtProjClientEmialID).val().trim(),
                            GSTNumber: $("#IdTxt_GSTNumberShipping").val().trim(),
                            InvoiceAddress: $("#IdTxt_InvoiceAddress").val().trim(),
                            Hypothecation: $(txtHypothecation).val().trim(),
                            ManualDispatchAddress: $("#txtManualDisptachAddress").val().trim(),
                            SiteClient_EmailID: $(IdTxtProjClientEmialID).val().trim(),
                            PONumber: $(IdTxt_Pono).val().trim(),
                            TransportationWBS: $(IdTxt_TransportationWBS).val().trim(),
                            NTMonth: $(IdTxtNTMonth).val().trim(),

                            //=================================== pavan kumar v ===================================//

                            Warranty_wbs: $("#IdTxtWarrantywbs").val().trim(),
                            Projected_Contractual_Delivery: $("#txt_Projected_CDD").val().trim(),
                            Buffer_days: $("#IdTxtBufferdays").val().trim(),
                            Lubricant: "",
                            Grease: "",
                            //Lubricant: $("#txtLubricant").val().trim(),
                            //Grease: $("#txtGrease").val().trim(),
                            //=================================== pavan kumar v 3rd June ===================================//
                            Travel_WBS: $("#txt_TravelWBS").val().trim(),
                            Txt_SFN: $("#IdTxtSFN").val().trim(),
                            SI_Remark: $("#IdTxt_SIRemark").val().trim(),
                            GSTSameAsShipping: $("#chk_GST_Shipping").is(":checked") ? "1" : "0",
                            GST_Number_Billing: $("#IdTxt_GSTNumberBilling").val().trim(),
                            Contractual_DeliveryDate_Others: Contractual_DeliveryDate_Othersvalue
                        }
                    Header_JSONDATA.push(Json_header_dat);
                    var json_FinalData_insert = {
                        HeaderDatalenght: Header_JSONDATA.length,
                        ProjectHeaderDetails: Header_JSONDATA,
                        PlantDetailsAddlenght: json_data_PlantDetails_Add.length,
                        PlantDetailsAddDetails: json_data_PlantDetails_Add,
                        PlantDetailsEditlenght: jsonData_PlantDetails_edit.length,
                        PlantDetailsEditDetails: jsonData_PlantDetails_edit
                    }
                    console.log(json_FinalData_insert);


                    $.ajax({
                        url: AbsolutePath('/Project/SaveProjectDetailsInfo'),
                        type: "POST", datatype: 'json', data: json_FinalData_insert,
                        success: function (resp) {
                            if (resp == "1") {
                                $("#IdDivModelViewProjects").modal("hide");
                                MainLandingGridForProjects();
                                // HideLoadingImg();
                                AlertDialog("Updated sucessfully", "success");
                                for (var i = RowIDCollection_Add.length; i > 0; i--) {
                                    RowIDCollection_Add.pop(i);
                                }
                                for (var j = Arr_Edit.length; j > 0; j--) {
                                    Arr_Edit.pop(j);
                                }
                                for (var k = Header_JSONDATA.length; k > 0; k--) {
                                    Header_JSONDATA.pop(k);
                                }
                            } else {
                                //HideLoadingImg();
                                AlertDialog("Fail to Update", "warning");
                            }
                        }
                    });

                } else {
                }
            } else {
                AlertDialog("Please fill all mandatory field(s)", "warning");
            }
        }
    }

    function EditProjectDetails(id)
    {
        if (Usertype.toString().toLowerCase() == "pm" || Usertype.toString().toLowerCase() == "hod")
        {
            //alert(Usertype.toString())
           
            $("#IdBtnProjectPDFGenerate").show();
            $("#IdTxtProjDODateForAccordion_lbl").show();
            $("#IdTxtProjDODateForAccordion").show();
        }
        else
        {
            $("#IdBtnProjectPDFGenerate").hide();
            $("#IdTxtProjDODateForAccordion_lbl").hide();
            $("#IdTxtProjDODateForAccordion").hide();
        }

        $("#IdBtnProjectDraft").css("display", 'inline-block');
        $("#IdBtnProjectSubmit").css("display", 'inline-block');

        $(".dispatcherhide").css("display", "block");
        EditClick = "1";

        var row_data = $("#IdTblProjectsGrid").getRowData(id);
        console.log(row_data);
        var ProjectStatusIC = row_data["Project_StatusIC"];     
        ClearControls();
        $(".divredfont").css('display', 'block');
        $("#div_break1 br").css('display', 'block')
        $("#div_break2 br").css('display', 'block')
        $("#div_break3 br").css('display', 'block')
        $("#div_break4 br").css('display', 'block')
        $(".clsblue").css('display', 'block');
        $("#IdTxtProjDODateForAccordion").val(row_data["DO_Date"]);
        if (Usertype.toString().toLowerCase() == "pt" || Usertype.toString().toLowerCase() == "pm")
        {
                     
            $("#IdTxtProjContractualDeliveryForAccordion").val(row_data["Contractual_Delivery_date"]);
            $(".divredfont").css('display', 'block');
            $(".clsblue").css('display', 'block');

            $("#Idinstalltionengineeric").removeClass("myclass1");
            $("#Idinstalltionengineeric").removeClass("myclass1");
            $("#IdSpnDispatcher").removeClass("myclass1");
            $("#spn_IdSitInCharge").removeClass("myclass1");
            $("#spn_IdSevicenEngineer").addClass("myclass1");
            $("#IdspnsecSevicenEngineer").addClass("myclass1");

            if (ProjectStatusIC == "1" || ProjectStatusIC == "11" || ProjectStatusIC == "2")
            {
                $(".bydefaultdisabled").prop("disabled", true);
                $(".PTdisabledfields").prop("disabled", false);
                $(".RIEDisbaledfields").prop("disabled", false);
                $(".Dispatcherdisbaledfields").prop("disabled", true);
                $("#IdBtnProjectDraft").css('display', 'inline-block');
                $("#IdBtnProjectSubmit").css('display', 'inline-block');             
                $("#IdSpnProjectSelect").removeClass("myclass1");
                $("#IdSpnProjectmgr").removeClass("myclass1");                          
                $("#IdspnTxtProjContractor").removeClass("myclass1");
                $("#IdspnTxtProjContractorplus").removeClass("myclass1");
                $("#IdSpnContractorPersonAtSite").removeClass("myclass1");
                $("#IdSpnContractorPersonAtSitePlus").removeClass("myclass1");
                $("#IdspnTxtProjElectContractor").removeClass("myclass1");
                $("#IdspnTxtProjElectContractorplus").removeClass("myclass1");
                $("#IdSpnElectContractorPersonAtSite").removeClass("myclass1");
                $("#IdSpnElectContractorPersonAtSiteplus").removeClass("myclass1");                            
                $("#IdTxtProjIsActive").prop("disabled", false);
                $("#txtMobilZationIntimationReceived").prop("disabled", false);
                $("#txtSupplyCompletionDate").prop("disabled", false);
                $("#IdTxtProjMobilizationDate").prop("disabled", false);
                $("#IdTxtProjComissionedDateForAccordion").prop("disabled", false);
            }

            else
            {
               
                $(".bydefaultdisabled").prop("disabled", true);
                $(".PTdisabledfields").prop("disabled", true);
                $(".RIEDisbaledfields").prop("disabled", true);
                $(".Dispatcherdisbaledfields").prop("disabled", true);
                $("#IdBtnProjectDraft").css('display', 'inline-block');
                $("#IdBtnProjectSubmit").css('display', 'inline-block');

             //   $("#IdSpnDispatcher").addClass("myclass1");

              //  $("#IdSpnProjectSelect").addClass("myclass1");
                $("#IdSpnProjectmgr").addClass("myclass1");
               // $("#spn_IdSitInCharge").addClass("myclass1");

              //  $("#Idinstalltionengineeric").addClass("myclass1");
                $("#IdspnTxtProjContractor").addClass("myclass1");
                $("#IdspnTxtProjContractorplus").addClass("myclass1");

                $("#IdSpnContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnContractorPersonAtSitePlus").addClass("myclass1");
                $("#IdspnTxtProjElectContractor").addClass("myclass1");
                $("#IdspnTxtProjElectContractorplus").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSiteplus").addClass("myclass1");

            //    $("#Idinstalltionengineeric").addClass("myclass1");

               // $("#spn_IdSevicenEngineer").addClass("myclass1");

               // $("#IdspnsecSevicenEngineer").addClass("myclass1");
                $("#IdTxtProjIsActive").prop("disabled", true);
                $("#txtMobilZationIntimationReceived").prop("disabled", true);
                $("#txtSupplyCompletionDate").prop("disabled", false);
                $("#IdTxtProjMobilizationDate").prop("disabled", false);
                $("#IdTxtProjComissionedDateForAccordion").prop("disabled", false);

            }

        }
        else if (Usertype.toString().toLowerCase() == "hod")
        {
          
            $(".divredfont").css('display', 'block');
            $(".clsblue").css('display', 'block');
            $("#IdTxtProjContractualDeliveryForAccordion").val(row_data["Contractual_Delivery_date"]);

            $(".bydefaultdisabled").prop("disabled", true);
            $(".PTdisabledfields").prop("disabled", false);
            $(".RIEDisbaledfields").prop("disabled", false);
            $(".Dispatcherdisbaledfields").prop("disabled", true);
            $("#IdBtnProjectDraft").css('display', 'inline-block');
            $("#IdBtnProjectSubmit").css('display', 'inline-block');

            $("#IdSpnDispatcher").removeClass("myclass1");

            $("#IdSpnProjectSelect").removeClass("myclass1");
            $("#IdSpnProjectmgr").removeClass("myclass1");
            $("#spn_IdSitInCharge").removeClass("myclass1");

           $("#Idinstalltionengineeric").removeClass("myclass1");
            $("#IdspnTxtProjContractor").removeClass("myclass1");
            $("#IdspnTxtProjContractorplus").removeClass("myclass1");

            $("#IdSpnContractorPersonAtSite").removeClass("myclass1");

            $("#IdSpnContractorPersonAtSitePlus").removeClass("myclass1");
            $("#IdspnTxtProjElectContractor").removeClass("myclass1");
            $("#IdspnTxtProjElectContractorplus").removeClass("myclass1");

            $("#IdSpnElectContractorPersonAtSite").removeClass("myclass1");

            $("#IdSpnElectContractorPersonAtSiteplus").removeClass("myclass1");

           $("#Idinstalltionengineeric").removeClass("myclass1");

            $("#spn_IdSevicenEngineer").removeClass("myclass1");

            $("#IdspnsecSevicenEngineer").removeClass("myclass1");
            $("#IdTxtProjIsActive").prop("disabled", false);
            $("#txtMobilZationIntimationReceived").prop("disabled", false);
        }

        else if (Usertype.toString().toLowerCase() == "sicrie")
        {
        
            $("#IdTxtProjContractualDeliveryForAccordion").val(row_data["Contractual_DeliveryDate_Others"]);
            $(".divredfont").css('display', 'none');
            $("#div_break1 br").css('display', 'none')
            $("#div_break2 br").css('display', 'none')

            $("#div_break3 br").css('display', 'block')
            $("#div_break4 br").css('display', 'block')
            $(".clsblue").css('display', 'block');

            $("#spn_IdSitInCharge").removeClass("myclass1");
            $("#Idinstalltionengineeric").removeClass("myclass1");

            if (ProjectStatusIC == "2" || ProjectStatusIC == "11")
            {
                $(".bydefaultdisabled").prop("disabled", true);
                $(".PTdisabledfields").prop("disabled", true);
                $(".RIEDisbaledfields").prop("disabled", false);
                $(".Dispatcherdisbaledfields").prop("disabled", true);
                $("#IdBtnProjectDraft").css('display', 'inline-block');
                $("#IdBtnProjectSubmit").css('display', 'inline-block');

                $("#IdSpnDispatcher").addClass("myclass1");

                $("#IdSpnProjectSelect").addClass("myclass1");
                $("#IdSpnProjectmgr").addClass("myclass1");              
                $("#IdspnTxtProjContractor").removeClass("myclass1");
                $("#IdspnTxtProjContractorplus").removeClass("myclass1");

                $("#IdSpnContractorPersonAtSite").removeClass("myclass1");

                $("#IdSpnContractorPersonAtSitePlus").removeClass("myclass1");
                $("#IdspnTxtProjElectContractor").removeClass("myclass1");
                $("#IdspnTxtProjElectContractorplus").removeClass("myclass1");

                $("#IdSpnElectContractorPersonAtSite").removeClass("myclass1");

                $("#IdSpnElectContractorPersonAtSiteplus").removeClass("myclass1");

              

                $("#spn_IdSevicenEngineer").addClass("myclass1");

                $("#IdspnsecSevicenEngineer").addClass("myclass1");
                $("#IdTxtProjIsActive").prop("disabled", true);
                $("#txtMobilZationIntimationReceived").prop("disabled", false);

                $("#txtSupplyCompletionDate").prop("disabled", false);
                $("#IdTxtProjMobilizationDate").prop("disabled", false);
                $("#IdTxtProjComissionedDateForAccordion").prop("disabled", false);

            } else
            {
              
                $(".bydefaultdisabled").prop("disabled", true);
                $(".PTdisabledfields").prop("disabled", true);
                $(".RIEDisbaledfields").prop("disabled", true);
                $(".Dispatcherdisbaledfields").prop("disabled", true);
                $("#IdBtnProjectDraft").css('display', 'inline-block');
                $("#IdBtnProjectSubmit").css('display', 'inline-block');

               $("#IdSpnDispatcher").addClass("myclass1");

               $("#IdSpnProjectSelect").addClass("myclass1");
                $("#IdSpnProjectmgr").addClass("myclass1");
               // $("#spn_IdSitInCharge").addClass("myclass1");

              //  $("#Idinstalltionengineeric").addClass("myclass1");
                $("#IdspnTxtProjContractor").addClass("myclass1");
                $("#IdspnTxtProjContractorplus").addClass("myclass1");

                $("#IdSpnContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnContractorPersonAtSitePlus").addClass("myclass1");
                $("#IdspnTxtProjElectContractor").addClass("myclass1");
                $("#IdspnTxtProjElectContractorplus").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSiteplus").addClass("myclass1");

                $("#Idinstalltionengineeric").addClass("myclass1");

                $("#spn_IdSevicenEngineer").addClass("myclass1");

                $("#IdspnsecSevicenEngineer").addClass("myclass1");
                $("#IdTxtProjIsActive").prop("disabled", true);
                $("#txtMobilZationIntimationReceived").prop("disabled", true);
                $("#txtSupplyCompletionDate").prop("disabled", false);
                $("#IdTxtProjMobilizationDate").prop("disabled", false);
                $("#IdTxtProjComissionedDateForAccordion").prop("disabled", false);

            }


        }
        else
        {
            $(".divredfont").css('display', 'none');
            $(".clsblue").css('display', 'none');
            $(".dispatcherhide").css("display", "none");
            $(".divredfont br").css('display', 'none');
            //$(".divredfont").next().remove();
            $("#div_break1 br").css('display', 'none')
            $("#div_break2 br").css('display', 'none')

            $("#div_break3 br").css('display', 'none')
            $("#div_break4 br").css('display', 'none')
            $("#IdTxtProjContractualDeliveryForAccordion").val(row_data["Contractual_DeliveryDate_Others"]);
            $("#spn_IdSevicenEngineer").removeClass("myclass1");

            $("#IdspnsecSevicenEngineer").removeClass("myclass1");
            if (ProjectStatusIC == "2" || ProjectStatusIC == "3" || ProjectStatusIC == "12") {
                $(".bydefaultdisabled").prop("disabled", true);
                $(".PTdisabledfields").prop("disabled", true);
                $(".RIEDisbaledfields").prop("disabled", true);
                $(".Dispatcherdisbaledfields").prop("disabled", true);
                $("#IdBtnProjectDraft").css('display', 'inline-block');
                $("#IdBtnProjectSubmit").css('display', 'inline-block');

              $("#IdSpnDispatcher").addClass("myclass1");

                $("#IdSpnProjectSelect").addClass("myclass1");
                $("#IdSpnProjectmgr").addClass("myclass1");
                $("#spn_IdSitInCharge").addClass("myclass1");

                $("#Idinstalltionengineeric").addClass("myclass1");
                $("#IdspnTxtProjContractor").addClass("myclass1");
                $("#IdspnTxtProjContractorplus").addClass("myclass1");

                $("#IdSpnContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnContractorPersonAtSitePlus").addClass("myclass1");
                $("#IdspnTxtProjElectContractor").addClass("myclass1");
                $("#IdspnTxtProjElectContractorplus").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSiteplus").addClass("myclass1");

                $("#Idinstalltionengineeric").addClass("myclass1");

               
                $("#IdTxtProjIsActive").prop("disabled", true);
                $("#txtMobilZationIntimationReceived").prop("disabled", true);

            } else
            {               
                $(".bydefaultdisabled").prop("disabled", true);
                $(".PTdisabledfields").prop("disabled", true);
                $(".RIEDisbaledfields").prop("disabled", true);
                $(".Dispatcherdisbaledfields").prop("disabled", true);
                //$("#IdBtnProjectDraft").css('display', 'none');
                //$("#IdBtnProjectSubmit").css('display', 'none');

                $("#IdSpnDispatcher").addClass("myclass1");

               $("#IdSpnProjectSelect").addClass("myclass1");
                $("#IdSpnProjectmgr").addClass("myclass1");
                $("#spn_IdSitInCharge").addClass("myclass1");

                $("#Idinstalltionengineeric").addClass("myclass1");
                $("#IdspnTxtProjContractor").addClass("myclass1");
                $("#IdspnTxtProjContractorplus").addClass("myclass1");

                $("#IdSpnContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnContractorPersonAtSitePlus").addClass("myclass1");
                $("#IdspnTxtProjElectContractor").addClass("myclass1");
                $("#IdspnTxtProjElectContractorplus").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSite").addClass("myclass1");

                $("#IdSpnElectContractorPersonAtSiteplus").addClass("myclass1");

                $("#Idinstalltionengineeric").addClass("myclass1");

                $("#spn_IdSevicenEngineer").addClass("myclass1");

                $("#IdspnsecSevicenEngineer").addClass("myclass1");
                $("#IdTxtProjIsActive").prop("disabled", true);
                $("#txtMobilZationIntimationReceived").prop("disabled", true);

            }
        }



        ProjectID = row_data["ProjectDetailsId"];
        $("#IdTxtProjName").val(row_data["ProjectName"]);
        $("#IdTxtProjCreatedBy").val(row_data["CreatedBYName"]);
        $("#IdTxtProjCreatedDate").val(row_data["Created_Date"]);
        GetRegions();
        $("#Sel_Region").val(row_data["RegionIC"]);
        $("#Sel_Region").trigger("change");
        $("#ddl_States").val(row_data["StatenameIC"]);
        $("#IdTxtProjAssignedTo").val(row_data["RIEName"]);
        $("#IdTxtDispatcher").val(row_data["DispatcherName"]);
        $("#IdDdlPlantType").val(row_data["PlantTypeIC"]);
        $("#IdDdlPlantStage").val(row_data["Stage"]);
        $("#IdTxtProjLocation").val(row_data["Location"]);
        $("#IdTxtProjCodeForAccordion").val(row_data["Project_Code"]);
        $("#IdTxtProjClient").val(row_data["Site_Client_Name"]);
        $("#IdTxtProjClientContactNo").val(row_data["Site_Client_Mobile"]);
        $("#IdTxtProjSiteClientName").val(row_data["ClientContact"]);
        $("#IdTxtProjSiteClientMobile").val(row_data["ClientContactno"]);
        $("#txt_Projectmgr").val(row_data["ProjectManagerName"]);
        $("#IdTxtSONumber").val(row_data["SONumber"]);
        $("#txt_SOorHFMDate").val(row_data["SOorHFMDate"]);
        $("#IdTxtShiptoCode").val(row_data["ShiptoCode"]);
        $("#IdTxtSoldToCode").val(row_data["SoldToCode"]);
        $("#IdTxtServiceNetworkNo").val(row_data["ServiceNetworkNo"]);
        $("#IdTxtPODate").val(row_data["PODate"]);
        $("#txt_TourWBS").val(row_data["TourWBS"]);
        $("#txtPOReceivedDate").val(row_data["POReceivedDate"]);
        //$("#txt_OrderValue").val(row_data["OrderValue"]);
        $("#txt_OrderTerms").val(row_data["OrderTerms"]);

       // $("#txtBasicValue").val(row_data["BasicValue"]);
       // $("#txt_CreditNotValue").val(row_data["CreditNoteValue"]);
       // $("#txt_CreditCoteNo").val(row_data["CreditNoteNo"]);
        $("#txtSupplyCompletionDate").val(row_data["SupplyCompletionDate"]);
        $("#ddl_ProjectCurrentStatus").val(row_data["ProjectCurrentStatusIC"]);
        $("#IdTxtLOIDate").val(row_data["LOIDate"]);     
        $("#IdTxtProjClNoteRecievedForAccordion").val(row_data["Cl_Note_Recieved_date"]);
        $("#IdTxtProjLayoutApproval").val(row_data["Layout_Approval_date"]);
        $("#IdTxtProjSiteClearanceForAccordion").val(row_data["Site_Clearance_date"]);
        $("#IdTxtProjContractualDeliveryDaysForAccordion").val(row_data["Contractual_Delivery_Days"]);
        $("#IdTxtProjZeroDateMilestone").val(row_data["Zero_Date_MilestoneIC"]);
      //  $("#IdTxtProjContractualDeliveryForAccordion").val(row_data["Contractual_Delivery_date"]);
        $("#IdTxtProjSupplyStatusForAccordion").val(row_data["Supply_StatusIC"]);
        $("#IdTxtProjTargetDate").val(row_data["Target_Date"]);
        $("#IdTxtProjContractualRemainingDaysForAccordion").val(row_data["Contractual_Remaining_Days"]);
        $("#IdTxtProjIdleManpowerDaysForAccordion").val(row_data["Idle_Manpower_Days"]);
        $("#IdDdlProjectStatus").val(row_data["Project_StatusIC"]);
        $("#IdSitInCharge").val(row_data["Site_In_Charge_EmployeeIC"]);
        $("#IdTxtProjCurrentStatus").val(row_data["Current_StatusIC"]);
        $("#IdTxtProjComissionedDateForAccordion").val(row_data["Comissioned_Date"]);
        $("#IdTxtProjMobilizationDate").val(row_data["Mobilization_Date"]);
        $("#IdInstalltionEngineer").val(row_data["IEName"]);
        $("#IdTxtProjContractor").val(row_data["ContractorName"]);
        MechContractorNameadd = row_data["ContractorName"];
        ElecContractorNameadd = row_data["MechContractorName"];
        MechContractorNameaddIC = row_data["ContractorIC"];
        ElecContractorNameaddIC = row_data["MechContractorIC"];
        $("#IdTxtProjContractorPersonAtSite").val(row_data["ContractorPersonName"]);
        $("#IdTxtProjContractorSiteMobile").val(row_data["MobileNumber"]);
        $("#IdTxtProjElectContractor").val(row_data["MechContractorName"]);
        $("#IdTxtProjElectContractorPersonAtSite").val(row_data["MechContractorPersonName"]);
        $("#IdTxtProjElectContractorSiteMobile").val(row_data["MechContractorPersonNumber"]);
        $("#IdTxtProjClientDGReadinessDateForAccordion").val(row_data["Client_DG_Readiness_Date"]);
        $("#IdTxtProjInComingCableReadinessDateForAccordion").val(row_data["In_Coming_Cable_Readiness_Date"]);
        $("#IdTxtProjClientNoofCivilGuidanceVisited").val(row_data["NoofCivilGuidanceVisited"]);
        $("#IdTxtProjFirstMobilizeDate").val(row_data["First_Mobilize_Date"]);
        $("#IdTxtProjFirstDeMobilizeDateForAccordion").val(row_data["First_De_Mobilize_Date"]);
        $("#IdTxtProjFirstDeMobilizeReasonForAccordion").val(row_data["FirstDemobilizationReason"]);
        $("#IdTxtProjSecondMobilizationDateForAccordion").val(row_data["Second_Mobilization_Date"]);
        $("#IdTxtProjSecondDeMobilizationDate").val(row_data["Second_De_Mobilization_Date"]);
        $("#IdTxtProjSecondDeMobilizeReasonForAccordion").val(row_data["SecondDemobilizationReason"]);
        $("#IdTxtProjThirdMobilizationDateForAccordion").val(row_data["Third_Mobilization_Date"]);
        $("#IdTxtProjThirdDeMobilizationDateForAccordion").val(row_data["Third_De_Mobilization_Date"]);
        $("#IdTxtProjThirdDeMobilizeReasonForAccordion").val(row_data["ThridDemobilizationReason"]);
        $("#IdTxtProjfouthMobilizationDateForAccordion").val(row_data["fourthMobilizationDate"]);
        $("#IdTxtProjforthDeMobilizationDateForAccordion").val(row_data["fourthDeMobilizationDate"]);
        $("#IdTxtProjforthDeMobilizeReasonForAccordion").val(row_data["fourthDeMobilizationReason"]);
       // changeDaytillNow();
       $("#IdTxtProjDaysTillNow").val(row_data["Days_Till_Now"]);
        $("#IdTxtProjRemainingDaysForAccordion").val(row_data["Remaining_Days"]);
        $("#IdTxtProjIdleHydraDaysForAccordion").val(row_data["Idle_Hydra_Days"]);
        $("#IdSevicenEngineer").val(row_data["PrimaryServiceengineer"]);
        $("#IdsecSevicenEngineer").val(row_data["SecondaryServiceEngineer"]);
        $("#txt_RIEIC").val(row_data["Assigned_To_RIE"]);
        $("#txt_IEIC").val(row_data["Assign_To_IE"]);
        $("#txt_PmgrIC").val(row_data["Project_Manager"]);
        $("#txt_creatbyIC").val(row_data["Created_by"]);
        $("#txt_ContratorIC").val(row_data["ContractorIC"]);
        $("#txt_ContractorPersonIC").val(row_data["ContractorPersonatSiteIC"]);
        $("#txt_ElecContratorIC").val(row_data["MechContractorIC"]);
        $("#txt_ElecContractorPersonIC").val(row_data["MechContractorPersonatSiteIC"]);
        $("#txt_PrimaryServiceEngineerIC").val(row_data["AssignToServiceEngineer"]);
        $("#txt_SecondaryServiceEngineerIC").val(row_data["AssignToSecondaryServiceEngineer"]);
        $("#txt_DispatcherIC").val(row_data["AssignTODispatcher"]);
       // $("#IdTxtNTValue").val(row_data["NTValue"]);
        $("#IdTxtICNDate").val(row_data["ICNDate"]);
        //
        $("#IdTxt_Pono").val(row_data["PONumber"]);
        $("#IdTxt_TransportationWBS").val(row_data["TransportationWBS"]);
        $("#IdTxt_GSTNumberShipping").val(row_data["GST_Number"]);
        $("#IdTxt_InvoiceAddress").val(row_data["InvoiceAddress"]);
        $("#txtHypothecation").val(row_data["Hypothecation"]);
        $("#txtManualDisptachAddress").val(row_data["ManualDispatchAddress"]);
        $("#IdTxtProjClientEmialID").val(row_data["ClientEmailID"]);
        $("#IdTxtNTMonth").val(row_data["NTMonth"])
        var IsActive = row_data["IsActive"]
        if (IsActive == "True") {
            $("#ProjectIsActive").prop("checked", true);
        } else {
            $("#ProjectIsActive").prop("checked", false);
        }
        var Checksheet_Submitted = row_data["IS_CheckSheet_Completed"];
        if (Checksheet_Submitted == "True") {
            $("#chk_CheckSheet").prop("checked", true);
        } else {
            $("#chk_CheckSheet").prop("checked", false);
        }
        var IdleReportSubmitted = row_data["IS_IdleReoprtSubmitted"];
        if (IdleReportSubmitted == "True") {
            $("#chk_IdelReport").prop("checked", true);
        } else {
            $("#chk_IdelReport").prop("checked", false);
        }

        var hydraReportSubmitted = row_data["IS_HydraAvaliability_Completed"];
        if (hydraReportSubmitted == "True") {
            $("#chk_HydraAvailability").prop("checked", true);
        } else {
            $("#chk_HydraAvailability").prop("checked", false);
        }

        var ISCommissioningSubmitted = row_data["IS_Commissioning_Completed"];
        if (ISCommissioningSubmitted == "True") {
            $("#chk_Commissioning").prop("checked", true);
        } else {
            $("#chk_Commissioning").prop("checked", false);
        }

        var IsIPSubmitted = row_data["IS_InstallationProtocol_Completed"];
        if (IsIPSubmitted == "True") {
            $("#chk_InstallationProtocol").prop("checked", true);
        } else {
            $("#chk_InstallationProtocol").prop("checked", false);
        }

        var IsMIR = row_data["MobilZationIntimationReceived"];
        if (IsMIR == "True") {
            $("#txtMobilZationIntimationReceived").prop("checked", true);
        } else {
            $("#txtMobilZationIntimationReceived").prop("checked", false);
        }
        $("#txt_SharePointlink").val(row_data.SharePointlink);
        $("#txt_PrimaryDesc").val(row_data.PrimaryDesc);
        $("#txt_SecDesc").val(row_data.SecondaryDesc);
        $("#txt_TeritoryDesc").val(row_data.TeritoryDesc);
        $("#txt_OthersDesc").val(row_data.OthersDesc);
        PlantGridForProjects();
        ShortSupplyDetailsForProjects();
        MomentGridForProjects();
        StageMomentGridForProjects();
        if ('@ViewBag.ProjectsProjectCode' == '0') {
            ProjectLeftMenu();
        }


        if (Usertype.toString().toLowerCase() == "pt") {
            $("#div_PlantAccoridn").css("display", "none");
            $("#div_InstallationAccoridn").css("display", "none");

        } else {
            $("#div_PlantAccoridn").css("display", "block");
            $("#div_InstallationAccoridn").css("display", "block");
        }
        $("#IdDivModelViewProjects").modal({ backdrop: false });
        $("#ddl_ProjectCurrentStatus").prop("disabled", true);

        //pavan111

        $("#IdTxtWarrantywbs").val(row_data.Warranty_wbs);
        $("#txt_Projected_CDD").val(row_data.Projected_Contractual_Delivery);
        $("#IdTxtBufferdays").val(row_data.Buffer_days);


        $("#IdTxt_SIRemark").val(row_data.Special_Instruction_Remarks);
        $("#txt_TravelWBS").val(row_data.Travel_WBS);
        $("#IdTxtSFN").val(row_data.Sale_force_no);


        Special_Instruction_Remarks = row_data.Special_Instruction_Remarks;
        Sale_force_no = row_data.Sale_force_no;
        DI_PDF_Created_date = row_data.DI_PDF_Created_date;
        DI_Sr_no = row_data.DI_Sr_no;
        Rev_no = row_data.Rev_no;
        Indent_no = row_data.Indent_no;

        if (row_data.GSTSameAsShipping == "True")
        {
            $("#chk_GST_Shipping").prop("checked", true);
        } else {
            $("#chk_GST_Shipping").prop("checked", false);
        }
        $("#IdTxt_GSTNumberBilling").val(row_data.GST_Number_Billing)

        $.ajax({
            url: AbsolutePath('/Pdf/PdfPresent?DI_Sr_no=' + DI_Sr_no),
            type: 'GET',
            datatype: "JSON",
            success: function (resp)
            {
                if (resp == "true")
                {
                    if (Usertype.toString().toUpperCase() == "HOD" || Usertype.toString().toUpperCase() == "PM")
                    {
                        $("#IdBtnProjectSendmail").show();
                    }
                    
                }
                else
                {
                    $("#IdBtnProjectSendmail").hide();
                }
            },
            error: function (resp)
            {

            }
        });


        if ($("#IdDdlProjectStatus option:selected").val() == "5")
        {
           
               $("#IdSpnDispatcher").addClass("myclass1");
              $("#IdSpnProjectSelect").addClass("myclass1");
              $("#spn_IdSitInCharge").addClass("myclass1");
              $("#Idinstalltionengineeric").addClass("myclass1");
              $("#Idinstalltionengineeric").addClass("myclass1");
             $("#spn_IdSevicenEngineer").addClass("myclass1");
             $("#IdspnsecSevicenEngineer").addClass("myclass1");

             $("#IdBtnProjectDraft").hide();
             $("#IdBtnProjectSubmit").hide();
             $("#IdBtnProjectPDFGenerate").hide();
             $("#IdBtnProjectSendmail").hide();

        }
        else
        {
            $("#IdSpnDispatcher").removeClass("myclass1");
            $("#IdSpnProjectSelect").removeClass("myclass1");
            $("#spn_IdSitInCharge").removeClass("myclass1");
            $("#Idinstalltionengineeric").removeClass("myclass1");
            $("#Idinstalltionengineeric").removeClass("myclass1");
            $("#spn_IdSevicenEngineer").removeClass("myclass1");
            $("#IdspnsecSevicenEngineer").removeClass("myclass1");
        }
    }
    function ViewProjectDetails(id)
    {
        EditClick = "1";
        var row_data = $("#IdTblProjectsGrid").getRowData(id);
        console.log(row_data);
        ClearControls();
        ProjectID = row_data["ProjectDetailsId"];
        $("#IdTxtProjName").val(row_data["ProjectName"]);

        $("#IdTxtProjCreatedBy").val(row_data["CreatedBYName"]);
        $("#IdTxtProjCreatedDate").val(row_data["Created_Date"]);
        $("#Sel_Region").val(row_data["RegionIC"]);
        $("#ddl_States").val(row_data["StatenameIC"]);
        $("#IdTxtProjAssignedTo").val(row_data["RIEName"]);
        $("#IdTxtDispatcher").val(row_data["DispatcherName"]);
        $("#IdDdlPlantType").val(row_data["PlantTypeIC"]);
        $("#IdDdlPlantStage").val(row_data["Stage"]);
        $("#IdTxtProjLocation").val(row_data["Location"]);
        $("#IdTxtProjCodeForAccordion").val(row_data["Project_Code"]);
        $("#IdTxtProjClient").val(row_data["Site_Client_Name"]);
        $("#IdTxtProjClientContactNo").val(row_data["Site_Client_Mobile"]);
        $("#IdTxtProjSiteClientName").val(row_data["ClientContact"]);
        $("#IdTxtProjSiteClientMobile").val(row_data["ClientContactno"]);
        $("#txt_Projectmgr").val(row_data["ProjectManagerName"]);
        $("#IdTxtSONumber").val(row_data["SONumber"]);
        $("#txt_SOorHFMDate").val(row_data["SOorHFMDate"]);
        $("#IdTxtShiptoCode").val(row_data["ShiptoCode"]);
        $("#IdTxtSoldToCode").val(row_data["SoldToCode"]);
        $("#IdTxtServiceNetworkNo").val(row_data["ServiceNetworkNo"]);
        $("#IdTxtPODate").val(row_data["PODate"]);
        $("#txt_TourWBS").val(row_data["TourWBS"]);
        $("#txtPOReceivedDate").val(row_data["POReceivedDate"]);
        //$("#txt_OrderValue").val(row_data["OrderValue"]);
        $("#txt_OrderTerms").val(row_data["OrderTerms"]);

        //$("#txtBasicValue").val(row_data["BasicValue"]);
      //  $("#txt_CreditNotValue").val(row_data["CreditNoteValue"]);
     //   $("#txt_CreditCoteNo").val(row_data["CreditNoteNo"]);
        $("#txtSupplyCompletionDate").val(row_data["SupplyCompletionDate"]);
        $("#ddl_ProjectCurrentStatus").val(row_data["ProjectCurrentStatusIC"]);
        $("#IdTxtLOIDate").val(row_data["LOIDate"]);
        $("#IdTxtProjDODateForAccordion").val(row_data["DO_Date"]);
        $("#IdTxtProjClNoteRecievedForAccordion").val(row_data["Cl_Note_Recieved_date"]);
        $("#IdTxtProjLayoutApproval").val(row_data["Layout_Approval_date"]);
        $("#IdTxtProjSiteClearanceForAccordion").val(row_data["Site_Clearance_date"]);
        $("#IdTxtProjContractualDeliveryDaysForAccordion").val(row_data["Contractual_Delivery_Days"]);
        $("#IdTxtProjZeroDateMilestone").val(row_data["Zero_Date_MilestoneIC"]);
        $("#IdTxtProjContractualDeliveryForAccordion").val(row_data["Contractual_Delivery_date"]);
        $("#IdTxtProjSupplyStatusForAccordion").val(row_data["Supply_StatusIC"]);
        $("#IdTxtProjTargetDate").val(row_data["Target_Date"]);
        $("#IdTxtProjContractualRemainingDaysForAccordion").val(row_data["Contractual_Remaining_Days"]);
        $("#IdTxtProjIdleManpowerDaysForAccordion").val(row_data["Idle_Manpower_Days"]);
        $("#IdDdlProjectStatus").val(row_data["Project_StatusIC"]);
        $("#IdSitInCharge").val(row_data["Site_In_Charge_EmployeeIC"]);
        $("#IdTxtProjCurrentStatus").val(row_data["Current_StatusIC"]);
        $("#IdTxtProjComissionedDateForAccordion").val(row_data["Comissioned_Date"]);
        $("#IdTxtProjMobilizationDate").val(row_data["Mobilization_Date"]);
        $("#IdInstalltionEngineer").val(row_data["IEName"]);
        $("#IdTxtProjContractor").val(row_data["ContractorName"]);
        $("#IdTxtProjContractorPersonAtSite").val(row_data["ContractorPersonName"]);
        $("#IdTxtProjContractorSiteMobile").val(row_data["MobileNumber"]);
        $("#IdTxtProjElectContractor").val(row_data["MechContractorName"]);
        $("#IdTxtProjElectContractorPersonAtSite").val(row_data["MechContractorPersonName"]);
        $("#IdTxtProjElectContractorSiteMobile").val(row_data["MechContractorPersonNumber"]);
        $("#IdTxtProjClientDGReadinessDateForAccordion").val(row_data["Client_DG_Readiness_Date"]);
        $("#IdTxtProjInComingCableReadinessDateForAccordion").val(row_data["In_Coming_Cable_Readiness_Date"]);
        $("#IdTxtProjClientNoofCivilGuidanceVisited").val(row_data["NoofCivilGuidanceVisited"]);
        $("#IdTxtProjFirstMobilizeDate").val(row_data["First_Mobilize_Date"]);
        $("#IdTxtProjFirstDeMobilizeDateForAccordion").val(row_data["First_De_Mobilize_Date"]);
        $("#IdTxtProjFirstDeMobilizeReasonForAccordion").val(row_data["FirstDemobilizationReason"]);
        $("#IdTxtProjSecondMobilizationDateForAccordion").val(row_data["Second_Mobilization_Date"]);
        $("#IdTxtProjSecondDeMobilizationDate").val(row_data["Second_De_Mobilization_Date"]);
        $("#IdTxtProjSecondDeMobilizeReasonForAccordion").val(row_data["SecondDemobilizationReason"]);
        $("#IdTxtProjThirdMobilizationDateForAccordion").val(row_data["Third_Mobilization_Date"]);
        $("#IdTxtProjThirdDeMobilizationDateForAccordion").val(row_data["Third_De_Mobilization_Date"]);
        $("#IdTxtProjThirdDeMobilizeReasonForAccordion").val(row_data["ThridDemobilizationReason"]);
        $("#IdTxtProjfouthMobilizationDateForAccordion").val(row_data["fourthMobilizationDate"]);
        $("#IdTxtProjforthDeMobilizationDateForAccordion").val(row_data["fourthDeMobilizationDate"]);
        $("#IdTxtProjforthDeMobilizeReasonForAccordion").val(row_data["fourthDeMobilizationReason"]);
        $("#IdTxtProjDaysTillNow").val(row_data["Days_Till_Now"]);
        $("#IdTxtProjRemainingDaysForAccordion").val(row_data["Remaining_Days"]);
        $("#IdTxtProjIdleHydraDaysForAccordion").val(row_data["Idle_Hydra_Days"]);
        $("#IdSevicenEngineer").val(row_data["PrimaryServiceengineer"]);
        $("#IdsecSevicenEngineer").val(row_data["SecondaryServiceEngineer"]);
        $("#txt_RIEIC").val(row_data["Assigned_To_RIE"]);
        $("#txt_IEIC").val(row_data["Assign_To_IE"]);
        $("#txt_PmgrIC").val(row_data["Project_Manager"]);
        $("#txt_creatbyIC").val(row_data["Created_by"]);
        $("#txt_ContratorIC").val(row_data["ContractorIC"]);
        $("#txt_ContractorPersonIC").val(row_data["ContractorPersonatSiteIC"]);
        $("#txt_ElecContratorIC").val(row_data["MechContractorIC"]);
        $("#txt_ElecContractorPersonIC").val(row_data["MechContractorPersonatSiteIC"]);
        $("#txt_PrimaryServiceEngineerIC").val(row_data["AssignToServiceEngineer"]);
        $("#txt_SecondaryServiceEngineerIC").val(row_data["AssignToSecondaryServiceEngineer"]);
        $("#txt_DispatcherIC").val(row_data["AssignTODispatcher"]);
        var IsActive = row_data["IsActive"]
        if (IsActive == "True") {
            $("#ProjectIsActive").prop("checked", true);
        } else {
            $("#ProjectIsActive").prop("checked", false);
        }
        var Checksheet_Submitted = row_data["IS_CheckSheet_Completed"];
        if (Checksheet_Submitted == "True") {
            $("#chk_CheckSheet").prop("checked", true);
        } else {
            $("#chk_CheckSheet").prop("checked", false);
        }
        var IdleReportSubmitted = row_data["IS_IdleReoprtSubmitted"];
        if (IdleReportSubmitted == "True") {
            $("#chk_IdelReport").prop("checked", true);
        } else {
            $("#chk_IdelReport").prop("checked", false);
        }

        var hydraReportSubmitted = row_data["IS_HydraAvaliability_Completed"];
        if (hydraReportSubmitted == "True") {
            $("#chk_HydraAvailability").prop("checked", true);
        } else {
            $("#chk_HydraAvailability").prop("checked", false);
        }

        var ISCommissioningSubmitted = row_data["IS_Commissioning_Completed"];
        if (ISCommissioningSubmitted == "True") {
            $("#chk_Commissioning").prop("checked", true);
        } else {
            $("#chk_Commissioning").prop("checked", false);
        }

        var IsIPSubmitted = row_data["IS_InstallationProtocol_Completed"];
        if (IsIPSubmitted == "True") {
            $("#chk_InstallationProtocol").prop("checked", true);
        } else {
            $("#chk_InstallationProtocol").prop("checked", false);
        }

        var IsMIR = row_data["MobilZationIntimationReceived"];
        if (IsMIR == "True") {
            $("#txtMobilZationIntimationReceived").prop("checked", true);
        } else {
            $("#txtMobilZationIntimationReceived").prop("checked", false);
        }
        $("#txt_SharePointlink").val(row_data.Sharepontlink);
        $("#txt_PrimaryDesc").val(row_data.PrimaryDesc);
        $("#txt_SecDesc").val(row_data.SecondaryDesc);
        $("#txt_TeritoryDesc").val(row_data.TerioryDesc);
        $("#txt_OthersDesc").val(row_data.OthersDesc);
        PlantGridForProjects();
        MomentGridForProjects();
        ShortSupplyDetailsForProjects();
        StageMomentGridForProjects();
        if ('@ViewBag.ProjectsProjectCode' == '0') {
            ProjectLeftMenu();
        }
        $("#IdDivModelViewProjects").modal({ backdrop: false });
        $("#IdBtnProjectDraft").css("display", 'none');
        $("#IdBtnProjectSubmit").css("display", 'none');
        $("#IdTxtWarrantywbs").val(row_data.Warranty_wbs);
        $("#txt_Projected_CDD").val(row_data.Projected_Contractual_Delivery);
        $("#IdTxtBufferdays").val(row_data.Buffer_days);

        $("#IdTxt_SIRemark").val(row_data.Special_Instruction_Remarks);
        $("#txt_TravelWBS").val(row_data.Travel_WBS);
        $("#IdTxtSFN").val(row_data.Sale_force_no);

        Special_Instruction_Remarks = row_data.Special_Instruction_Remarks;
        Sale_force_no = row_data.Sale_force_no;
        DI_PDF_Created_date = row_data.DI_PDF_Created_date;
        DI_Sr_no = row_data.DI_Sr_no;
        Rev_no = row_data.Rev_no;
        Indent_no = row_data.Indent_no;
    }
    function MainLandingGridForProjects() {
        $("#IdTblProjectsGrid").GridUnload();
        $("#IdTblProjectsGrid").jqGrid
            ({
                caption: "Project Details",
                url: AbsolutePath("/Project/GetProjectDetailsLandingGrid?Usertype=" + Usertype + "&&EmployeeIC=" + LoginID),
                datatype: "json",
                height: "auto",
                viewrecords: true,
                rownumbers: true,
                rowList: [15, 20, 50, 100],
                rowNum: 20,
                pager: "#IdDivProjectsPager",
                colModel: [
                    {
                        name: 'Edit', width: 60, align: 'center', formatter: function (a, b) {
                            return "<span class='glyphicon glyphicon-pencil ClsViewInvoiceDetails' onclick='EditProjectDetails(" + b.rowId + ")' id='IdProject_EditImg" + b.rowId + "'></span>";
                        }, search: false,
                    },

                    { name: "ProjectDetailsId", label: "ProjectID", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Created_by", label: "Created by", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Created_Date", label: "Created Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Project_StatusIC", label: "Project StatusIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "Assigned_To_RIE", label: "Assigned To RIE", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                          { name: "PlantTypeIC", label: "Plant TypeIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

                    { name: "RegionIC", label: "Region IC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "IEName", label: "IEName", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "StatenameIC", label: "Statename IC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

                      { name: "MechContractorPersonatSiteIC", label: "MechContractorPersonatSiteIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "MechContractorIC", label: "MechContractorIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "AssignToSecondaryServiceEngineer", label: "AssignToSecondaryServiceEngineer", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

                      { name: "Project_Manager", label: "Project Manager", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "DO_Date", label: "DO Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Cl_Note_Recieved_date", label: "ClNoteRecieved date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Layout_Approval_date", label: "Layout Approval date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Site_Clearance_date", label: "SiteClearance date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Contractual_Delivery_Days", label: "Contractual_Delivery_Days", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Zero_Date_MilestoneIC", label: "Zero_Date_MilestoneIC ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Contractual_Delivery_date", label: "Contractual_Delivery_date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "Supply_StatusIC", label: "Supply_StatusIC date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Target_Date", label: "Target_Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Contractual_Remaining_Days", label: "Contractual_Remaining_Days ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Idle_Manpower_Days", label: "Idle_Manpower_Days ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Site_In_Charge_EmployeeIC", label: "Site_In_Charge_EmployeeIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Current_StatusIC", label: "Current_StatusIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Mobilization_Date", label: "Mobilization_Date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Comissioned_Date", label: "Comissioned_Date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "ContractorIC", label: "ContractorIC ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "ContractorPersonatSiteIC", label: "ContractorPersonatSiteIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Client_DG_Readiness_Date", label: "Client_DG_Readiness_Date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "In_Coming_Cable_Readiness_Date", label: "In_Coming_Cable_Readiness_Date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                        { name: "First_Mobilize_Date", label: "First_Mobilize_Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "First_De_Mobilize_Date", label: "First_De_Mobilize_Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Second_Mobilization_Date", label: "Second_Mobilization_Date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Second_De_Mobilization_Date", label: "Second_De_Mobilization_Date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "Third_Mobilization_Date", label: "Third_Mobilization_Date ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Third_De_Mobilization_Date", label: "Third_De_Mobilization_Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Assign_To_IE", label: "Assign_To_IE ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "IS_CheckSheet_Completed", label: "IS_CheckSheet_Completed ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                                             { name: "IS_InstallationProtocol_Completed", label: "IS_InstallationProtocol_Completed", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "IS_HydraAvaliability_Completed", label: "IS_HydraAvaliability_Completed", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "IS_IdleReoprtSubmitted", label: "IS_IdleReoprtSubmitted ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "IS_Commissioning_Completed", label: "IS_Commissioning_Completed ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Days_Till_Now", label: "Days_Till_Now ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "Remaining_Days", label: "Remaining_Days ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Idle_Hydra_Days", label: "Idle_Hydra_Days", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "IsActive", label: "IsActive ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "Remarks", label: "Remarks ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "SONumber", label: "SONumber", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "ShiptoCode", label: "ShiptoCode", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "SoldToCode", label: "SoldToCode ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "ServiceNetworkNo", label: "ServiceNetworkNo ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "PODate", label: "PODate ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "LOIDate", label: "LOIDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "AssignToServiceEngineer", label: "AssignToServiceEngineer", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "CreatedBYName", label: "CreatedBYName", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "RIEName", label: "RIEName ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "RegionName", label: "RegionName ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "AssignTODispatcher", label: "AssignTODispatcher ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "FirstDemobilizationReason", label: "FirstDemobilizationReason", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "SecondDemobilizationReason", label: "SecondDemobilizationReason", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "ThridDemobilizationReason", label: "ThridDemobilizationReason", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "DispatcherName", label: "DispatcherName ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "RegionName", label: "RegionName ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "AssignTODispatcher", label: "AssignTODispatcher ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "ContractorName", label: "ContractorName ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "ContractorPersonName", label: "ContractorPersonName ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "MobileNumber", label: "MobileNumber ", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },


                      { name: "PrimaryServiceengineer", label: "PrimaryServiceengineer", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "SecondaryServiceEngineer", label: "SecondaryServiceEngineer", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "MechContractorName", label: "MechContractorName", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "MechContractorPersonName", label: "MechContractorPersonName", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "MechContractorPersonNumber", label: "MechContractorPersonNumber", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "ClientContact", label: "ClientContact", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "ClientContactno", label: "ClientContactno", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "SOorHFMDate", label: "SOorHFMDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "TourWBS", label: "TourWBS", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },


                       { name: "POReceivedDate", label: "POReceivedDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "OrderValue", label: "OrderValue", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "OrderTerms", label: "OrderTerms", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "BasicValue", label: "BasicValue", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "CreditNoteNo", label: "CreditNoteNo", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "CreditNoteValue", label: "CreditNoteValue", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "SupplyCompletionDate", label: "SupplyCompletionDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "NoofCivilGuidanceVisited", label: "NoofCivilGuidanceVisited", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "fourthMobilizationDate", label: "fourthMobilizationDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

                      { name: "fourthDeMobilizationDate", label: "fourthDeMobilizationDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "fourthDeMobilizationReason", label: "fourthDeMobilizationReason", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "MobilZationIntimationReceived", label: "MobilZationIntimationReceived", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Stage", label: "Stage", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "ProjectCurrentStatusIC", label: "ProjectCurrentStatusIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "CreditNoteValue", label: "CreditNoteValue", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "SupplyCompletionDate", label: "SupplyCompletionDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "NoofCivilGuidanceVisited", label: "NoofCivilGuidanceVisited", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                      { name: "fourthMobilizationDate", label: "fourthMobilizationDate", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

                    //--Colmuns needs to Show
                    { name: "ProjectName", label: "Project Name ", width: "100", align: "left", editable: true, search: true, searchable: true, },
                    { name: "Project_Code", label: "Project Code", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                   { name: "Site_Client_Name", label: "Client", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                   { name: "State_Name", label: "State", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, },
                    { name: "Site_Client_Mobile", label: "Mobile #", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Plant_typeName", label: "Plant Type", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                     { name: "PlantStageName", label: "Stage", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },

                    { name: "Location", label: "Location", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "ProjectManagerName", label: "Project Manager", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                     { name: "ProjectCurrentStatusName", label: "Current Status", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, },
                    { name: "ProjectStatusName", label: "Project Status", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "ProgressPercentage", label: "Progress %", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                {
                    name: "SharePointlink", label: "Share Point Link", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, formatter: 'showlink', editable: false, formatoptions: { baseLinkUrl: 'javascript:', showAction: "Link('", addParam: "');" }
                },
                //
                  { name: "PrimaryDesc", label: "Primary Description", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "SecondaryDesc", label: "Secondary Description", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "TeritoryDesc", label: "Teritory Description", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                { name: "OthersDesc", label: "Others Description", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true, hidden: true },
                { name: "NTValue", label: "NT Value", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                { name: "ICNDate", label: "ICN Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true, hidden: true },


                { name: "ClientEmailID", label: "ClientEmailID", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                     { name: "GST_Number", label: "GST Number", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "InvoiceAddress", label: "Invoice Address", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                { name: "Hypothecation", label: "Hypothecation", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true, hidden: true },
                { name: "ManualDispatchAddress", label: "Manual DispatchAddress", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                { name: "SiteClient_EmailID", label: "SiteClient EmailID", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                  { name: "PONumber", label: "PO Number", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                   { name: "TransportationWBS", label: "TransportationWBS", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "NTMonth", label: "NTMonth", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },


                    //pavan111
                    { name: "Warranty_wbs", label: "Warranty wbs", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                   { name: "Projected_Contractual_Delivery", label: "Projected Contractual Delivery", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                    { name: "Buffer_days", label: "Buffer days", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

                    { name: "Travel_WBS", label: "Travel WBS", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "Special_Instruction_Remarks", label: "Special Instruction Remarks", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "Sale_force_no", label: "Sale force no", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "DI_PDF_Created_date", label: "DI PDF Created date", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "DI_Sr_no", label: "DI Sr no", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "Rev_no", label: "Rev no", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "Indent_no", label: "Indent no", width: "100", align: "left", resizable: true, hidden: true },

                    { name: "GSTSameAsShipping", label: "GSTSameAsShipping", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "GST_Number_Billing", label: "GST_Number_Billing", width: "100", align: "left", resizable: true, hidden: true },
                    { name: "Contractual_DeliveryDate_Others", label: "Contractual_DeliveryDate_Others", width: "100", align: "left", resizable: true, hidden: true },
                    
                ],
                caption: "Project Details",
                loadComplete: function (data) {
                    console.log(data)
                    $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                    var Gridlen = $("#IdTblProjectsGrid").getDataIDs().length;
                    if (parseInt(Gridlen) > 0) {
                        $("#Grid_export").css("display", "inline-block");
                    } else {
                        $("#Grid_export").css("display", "none");
                    }
                },
                sortname: "ProjectDetailsId",
                sortorder: "asc",
                jsonReader:
          {
              root: "rows",
              total: "total",
              records: "records",
              repeatitems: false,
          },
            });


        $("#IdTblProjectsGrid").filterToolbar({
            resetIcon: "",
            stringResult: true,
            searchOnEnter: true,
        });


        $("#IdTblProjectsGrid").navGrid("#IdDivProjectsPager", { search: false, edit: false, add: false, del: false, refresh: false });



        $("#IdTblProjectsGrid").navButtonAdd("#IdDivProjectsPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                MainLandingGridForProjects();
            }
        });
        $("#IdTblProjectsGrid").navButtonAdd("#IdDivProjectsPager", {
            title: 'Export',
            id: 'Grid_export',
            caption: "",
            buttonicon: 'ui-icon-bookmark',
            onClickButton: function () {
                window.open(AbsolutePath("/Project/ExportToPdf"), '_blank')
            }
        });
    };

    function Link(id) {

        var row = id.split("=");
        var row_ID = row[1];
        var sitename = $("#IdTblProjectsGrid").getCell(row_ID, 'SharePointlink');
        var url = "http://" + sitename; // sitename will be like google.com or yahoo.com

        window.open(url);


    }
    MainLandingGridForProjects();

    function MomentGridForProjects() {

        $("#IdTblMovementGrid").GridUnload();
        $("#IdTblMovementGrid").jqGrid({
            url: AbsolutePath("/Project/GetProjectMovementlogDetails?ProjectIC=" + ProjectID),
            datatype: "json",
            mtype: "GET",
            height: 'auto',
            width: '650',
            viewrecords: true,
            rownumbers: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,

            pager: "#IdDivMovementPager",
            colModel: [
                { label: "ProjectMovementDetailsIC", name: "ProjectMovementDetailsIC", width: "100", align: "left", resizable: true, sortable: true, search: true, hidden: true, editable: true, },
                { label: "ProjectId", name: "ProjectId", width: "100", align: "left", resizable: true, sortable: true, search: true, hidden: true, editable: true, },
                 { label: "ProjectStatus_IC", name: "ProjectStatus_IC", width: "100", align: "left", resizable: true, sortable: true, search: true, hidden: true, editable: true, },
                { label: "ProjectId", name: "Modified_By", width: "100", align: "left", resizable: true, sortable: true, search: true, hidden: true, editable: true, },

               { label: "Project Status", name: "ProjectStatusName", width: "100", align: "left", resizable: true, sortable: true, search: true, editable: true, },
                { label: "Modified By", name: "Company_Employee_Name", width: "100", align: "left", resizable: true, sortable: true, search: true, editable: true, },
                { label: "Modified Date & Time", name: "Modified_Date", width: "100", align: "left", resizable: true, sortable: true, search: true, editable: true, },

            ],

            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            sortname: "ProjectMovementDetailsIC",
            sortorder: "asc",
            jsonReader:
            {
                root: "rows",
                total: "total",
                records: "records",
                repeatitems: false,
            },


            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); }
        });
        $("#IdTblMovementGrid").navGrid("#IdDivMovementPager", { add: false, del: false, edit: false, refresh: false, search: false });





        $("#IdTblMovementGrid").filterToolbar({
            resetIcon: "",
            stringResult: true,
            searchOnEnter: true,
        });

        $("#IdTblMovementGrid").navButtonAdd("#IdDivMovementPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                MomentGridForProjects();
            }
        });
    };

    // Stage movement Log

    function StageMomentGridForProjects() {

        $("#IdTblStageMovementGrid").GridUnload();
        $("#IdTblStageMovementGrid").jqGrid({
            url: AbsolutePath("/Project/GetProjectStageWiseMovementlogDetails?ProjectIC=" + ProjectID),
            datatype: "json",
            mtype: "GET",
            height: 'auto',
            width: '800',
            viewrecords: true,
            rownumbers: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            sortname: "ProjectStageWiseMomentlogIC",
            sortorder: "asc",
            pager: "#IdDivStageMovementPager",
            colModel: [
                { name: "ProjectStageWiseMomentlogIC", label: "ProjectStageWiseMomentlogIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                { name: "ProjectIC", label: "ProjectId", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                 { name: "StageIC", label: "StageIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

                 { name: "Modifiedby", label: "Modifiedby", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                { name: "StageStatusIC", label: "StageStatusIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },

               { name: "PlantStageNumber", label: "Stage", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "ProtocolStageStatusName", label: "Status Name", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                  { name: "Company_Employee_Name", label: "Modified By", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "ModifiedDate", label: "Modified Date & Time", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "Remarks", label: "Remarks", width: "200", align: "left", resizable: true, sortable: true, search: true, searchable: true },
            ],

            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            jsonReader:
           {
               root: "rows",
               total: "total",
               records: "records",
               repeatitems: false,
           },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); }
        });
        $("#IdTblStageMovementGrid").navGrid("#IdDivStageMovementPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $("#IdTblStageMovementGrid").filterToolbar({
            resetIcon: "",
            stringResult: true,
            searchOnEnter: true,
        });
        $("#IdTblStageMovementGrid").navButtonAdd("#IdDivStageMovementPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                StageMomentGridForProjects();
            }
        });
    };




    //========================================================================================//

    var I
    var itemarr = []
    function PlantGridForProjects() {

        $("#IdTblPlantGrid").GridUnload();
        $("#IdTblPlantGrid").jqGrid({
            url: AbsolutePath("/Project/GetPlantProjectDetails?ProjectIC=" + ProjectID),
            datatype: "json",
            mtype: "GET",
            height: 'auto',
            width: '650',
            viewrecords: true,
            rownumbers: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            caption: 'Equipment Details',
            pager: "#IdDivPlantPager",
            celledit: true,
            colModel: [

                   {
                       name: "Edit",
                       align: 'center',
                       width: 50,
                       resizable: false,
                       sortable: false,
                       search: false,
                       editable: false,
                       formatter: function (a, b) {
                           var elem1 = "<i class='fa fa-pencil ClsPencilIconContact' style='color:#041e42;cursor:pointer' title='Click Here For Edit' id='IdPencilIconContact_" + b.rowId + "' onclick='EditImageFunPlantDetails(id)'  ></i>";
                           return elem1;
                       }
                   },
                 {
                     label: "Select",
                     name: "Select",
                     width: 50,
                     search: false,
                     align: "center",
                     formatter: function (a, b) {
                         return "<input type='checkbox' id='PlantDetails_" + b.rowId + "' class='SelectPlantDetails' />"
                     }
                 },
                 {
                     name: "ProjectPlantDetailsId", label: "ProjectPlantDetailsId", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

                 },
                 {
                     name: "ProjectId", label: "ProjectId", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

                 },
                 {
                     name: "ItemType_ID", label: "ItemTypeName", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

                 },
                 {
                     name: "ItemType_model_ID", label: "SIC_Models_Name", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

                 },
                {
                    name: "ItemTypeName", label: "Item Type <span style='color:red'>*<span>", width: "140", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                    edittype: 'select',
                    editoptions: {
                        class: '',
                        dataUrl: AbsolutePath("/Project/GetItemTypes?PlantType=" + $("#IdDdlPlantType").children(":selected").text()),
                        dataEvents:
                        [{
                            type: 'change',
                            fn: function myfunction() {

                                var rowID = $(this).attr('id').split("_")[0];
                                var cntrlid = $(this).attr('id');
                                $("#" + cntrlid).css('width', '100%');
                                $("#" + rowID + "_SIC_Models_Name").css('width', '100%');
                                var valueofitemtype = $(this).children(':selected').val();
                                //var modelID = $('#' + $($(IdTblPlantGrid).getCell(1, 'ItemType_model_ID')).attr('id')).selector;
                                if (valueofitemtype != "0") {
                                    $('#' + $($(IdTblPlantGrid).getCell(rowID, 'SIC_Models_Name')).attr('id')).empty();

                                    $.ajax({
                                        url: AbsolutePath('/Project/GetMOdels?Itemtype=' + valueofitemtype),

                                        type: "GET",
                                        async: false,
                                        cache: false,
                                        success: function (resdata, status, xhr) {
                                            var Modelnames = "<option value='0'>---Select---</option>"
                                            $.each(resdata, function (index, value) {
                                                if (index == 0) {
                                                    Modelnames = Modelnames + "<option value=" + value.SIC_ModelsIC + ">" + value.SIC_Models_Name.toString() + "</option>";
                                                }
                                                else {
                                                    Modelnames = Modelnames + "<option value=" + value.SIC_ModelsIC + ">" + value.SIC_Models_Name.toString() + "</option>";
                                                }
                                            });
                                            $('#' + $($(IdTblPlantGrid).getCell(rowID, 'SIC_Models_Name')).attr('id')).append(Modelnames);

                                            if (valueofitemtype == "12") {

                                                $("#" + rowID + "_ConveyorLength").val("").attr('disabled', false);
                                                $("#" + rowID + "_ConveyorWidth").val("").attr('disabled', false);
                                                //$("#IdTblPlantGrid" + rowID).setColProp('length', { editable: false });

                                                //$("#IdTblPlantGrid").setColProp('length', { editable: true });
                                                //$("#IdTblPlantGrid").setColProp('width', { editable: true });
                                            }
                                            else {
                                                $("#" + rowID + "_ConveyorLength").val("").attr('disabled', true);
                                                $("#" + rowID + "_ConveyorWidth").val("").attr('disabled', true);
                                            }
                                        }
                                    })
                                } else {
                                    AlertDialog("Please select item type", "warning");
                                    $('#' + $($(IdTblPlantGrid).getCell(rowID, 'SIC_Models_Name')).attr('id')).empty();
                                    var Modelnames = "<option value='0'>---Select---</option>";
                                    $('#' + $($(IdTblPlantGrid).getCell(rowID, 'SIC_Models_Name')).attr('id')).append(Modelnames);
                                }

                            }

                        }]

                    }
                },
                  {
                      name: "SIC_Models_Name", label: "Model <span style='color:red'>*<span>", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                      edittype: 'select',
                      editoptions: {
                          value: { 0: '---Select---', },
                          dataEvents: [{
                              type: 'change',
                              fn: function modelfunction() {

                                  var rowID = $(this).attr('id').split("_")[0];
                                  var cntrlid = $(this).attr('id');
                                  var ItemTypename = $("#" + rowID + "_ItemTypeName").children(":selected").text();

                                  var valueofitemtype = $(this).children(':selected').val();
                                  //if (valueofitemtype != "0" && ItemTypename.toString().trim().toLowerCase() != "cnv_structure") {

                                  //--------------------pavankumar66

                                  if (valueofitemtype != "0") {
                                      $.ajax({
                                          url: AbsolutePath('/Project/GetInstallationProtocolExistsORNOT?ModelIC=' + valueofitemtype),
                                          type: "GET",
                                          async: false,
                                          cache: false,
                                          success: function (resdata, status, xhr)
                                          {
                                              if (resdata == "0") {
                                                  AlertDialog("Installation protocol doesn't exits for this model", "warning");
                                                  $("#" + rowID + "_SIC_Models_Name").val("0");
                                              }
                                          }
                                      })
                                  }

                              }


                          }]
                      }
                  },





              {
                  name: "No_Motors", label: "# Motors <span style='color:red'>*<span>", width: "100", align: "right", resizable: true, sortable: true, search: true, searchable: true, editable: true, editoptions: {
                      dataEvents: [{
                          type: 'blur',
                          fn: function myfun() {
                              var NUmberofMotors = $(this).val().toString().trim();
                              if (NUmberofMotors != "") {
                                  if (isNaN(NUmberofMotors)) {
                                      AlertDialog("Please enter # Motors like : 1,2", "warning");
                                      $(this).val("");
                                  } else {

                                      if (parseInt(NUmberofMotors) > 2 || parseInt(NUmberofMotors) == 0) {
                                          AlertDialog("# Motors accepts only 1 or 2", "warning");
                                          $(this).val("");
                                      }
                                  }
                              } else { }
                          }

                      }]

                  }

              },
              //
               {
                   name: "Stage_number", label: "StageIC", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

               },

              {
                  name: "PlantStageNumber", label: "Stage # <span style='color:red'>*<span>", width: "140", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                  edittype: 'select',
                  editoptions: {
                      class: '',
                      dataUrl: AbsolutePath("/Project/GetStageNumberNames"),
                      dataEvents:
                        [{
                            type: 'change',
                            fn: function myfunction() {

                                var rowID = $(this).attr('id').split("_")[0];
                                var cntrlid = $(this).attr('id');
                                $("#" + cntrlid).css('width', '100%');

                                var valueofitemtype = $(this).children(':selected').val();
                                var allowtoadd = "1";
                                if (valueofitemtype != "0") {

                                    var PlantStageval = $("#IdDdlPlantStage").children(":selected").val();

                                    switch (PlantStageval) {
                                        case '1':
                                            if (valueofitemtype == "1") {
                                                allowtoadd = "1";
                                            } else {
                                                allowtoadd = "0";
                                            }
                                            break;
                                        case '2':
                                            if (valueofitemtype == "1" || valueofitemtype == "2") {
                                                allowtoadd = "1";
                                            } else {
                                                allowtoadd = "0";
                                            }
                                            break;
                                        case '3':
                                            if (valueofitemtype == "1" || valueofitemtype == "2" || valueofitemtype == "3") {
                                                allowtoadd = "1";
                                            } else {
                                                allowtoadd = "0";
                                            }
                                            break;
                                        case '4':
                                            if (valueofitemtype == "1" || valueofitemtype == "2" || valueofitemtype == "3" || valueofitemtype == "4") {
                                                allowtoadd = "1";
                                            } else {
                                                allowtoadd = "0";
                                            }
                                            break;
                                        case '5':

                                            if (valueofitemtype == "1" || valueofitemtype == "2" || valueofitemtype == "3" || valueofitemtype == "4" || valueofitemtype == "5") {
                                                allowtoadd = "1";
                                            } else {
                                                allowtoadd = "0";
                                            }
                                            break;



                                    }
                                    if (allowtoadd == "0") {
                                        AlertDialog("You can stage numbers to the number of plant stages you have selected", "warning");
                                        $("#" + cntrlid).val("0");
                                    }


                                }
                                else {
                                    AlertDialog("Please select stage number", "warning");

                                }

                            }
                        }]
                  }
              },
               {
                   name: "ConveyorLength", label: "ConveyorLength", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, editable: true, edittype: 'text'

               },
                {
                    name: "ConveyorWidth", label: "ConveyorWidth ", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, editable: true, edittype: 'text'

                },


              //


            ],

            loadComplete: function (data) {

                console.log(data.rows)
                itemarr = data.rows
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');

                if (Usertype.toString().toLowerCase() == "pt" || Usertype.toString().toLowerCase() == "pm" || Usertype.toString().toLowerCase() == "hod") {
                    var ProjstatsuIC = $("#IdDdlProjectStatus").children(":selected").val();
                    if (ProjstatsuIC == "1" || ProjstatsuIC == "11" || ProjstatsuIC == "0") {

                        $("#Add_1").css('display', 'inline-block');
                        $("#del_1").css('display', 'inline-block');
                        $("#IdTblPlantGrid").jqGrid('showCol', "Select");
                        $("#IdTblPlantGrid").jqGrid('showCol', "Edit");
                    } else {
                        $("#Add_1").css('display', 'none');
                        $("#del_1").css('display', 'none');
                        $("#IdTblPlantGrid").jqGrid('hideCol', "Select");
                        $("#IdTblPlantGrid").jqGrid('hideCol', "Edit");
                    }

                }
                else {
                    $("#Add_1").css('display', 'none');
                    $("#del_1").css('display', 'none');

                    $("#IdTblPlantGrid").jqGrid('hideCol', 'Edit');
                    $("#IdTblPlantGrid").jqGrid('hideCol', 'Select');
                }





            },
            sortname: "ProjectPlantDetailsId",
            sortorder: "asc",
            jsonReader:
           {
               root: "rows",
               total: "total",
               records: "records",
               repeatitems: false,
           },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); }
        });
        $("#IdTblPlantGrid").navGrid("#IdDivPlantPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $("#IdTblPlantGrid").filterToolbar({
            resetIcon: "",
            stringResult: true,
            searchOnEnter: true,
        });


        $("#IdTblPlantGrid").navButtonAdd("#IdDivPlantPager",
            {
                title: 'Add',
                caption: "",
                id: 'Add_1',
                buttonicon: 'ui-icon-plus',
                onClickButton: function () {

                    if ($("#IdDdlPlantType").children(":selected").val() == "0" || $("#IdDdlPlantStage").children(":selected").val() == "0") {
                        AlertDialog("Please select plant type and plant stages", "warning");
                    } else {
                        var RowId = $("#IdTblPlantGrid").getDataIDs();
                        if (RowId == 0) {
                            NewRowId = 1;
                        }
                        else {
                            NewRowId = RowId.length + 1;
                        }
                        var Obj = {};
                        Obj.SlNum = NewRowId;
                        $("#IdTblPlantGrid").addRowData(NewRowId, Obj);
                        $("#IdTblPlantGrid").editRow(NewRowId);
                        $("#IdTblPlantGrid").find("tr").eq(NewRowId).find(".ClsPencilIconContact").replaceWith("<i class='fa fa-minus ClsDeleteIcon' style='color:#041e42;cursor:pointer'  title='Click Here For Delete' id='IdDeleteIcon_" + NewRowId + "' >");

                        RowIDCollection_Add.push(NewRowId);
                        $("#PlantDetails_" + NewRowId).prop("checked", false);
                        $("#PlantDetails_" + NewRowId).prop("disabled", true);
                        //Delete
                        $(".ClsDeleteIcon").click(function () {
                            var delid = $($(this).parent().parent()).attr("id")
                            $("#IdTblPlantGrid").delRowData(delid);
                            for (var i = 0; i < RowIDCollection_Add.length; i++) {
                                if (delid == RowIDCollection_Add[i]) {
                                    RowIDCollection_Add.splice(i, 1);
                                }
                            }
                        })



                        $("#IdTblPlantGrid").jqGrid('editRow', NewRowId, { key: true });
                        $("#" + NewRowId + "_ConveyorLength").attr('disabled', true);
                        $("#" + NewRowId + "_ConveyorWidth").attr('disabled', true);

                    }
                }
            });
        $("#IdTblPlantGrid").navButtonAdd("#IdDivPlantPager",
            {
            title: 'Delete',
            caption: "",
            id: 'del_1',
            buttonicon: 'ui-icon-trash',
            onClickButton: function () {

                var arr_checkedId = [];
                var RowiD = 0;
                var checkedlen = $("#IdTblPlantGrid").find(".SelectPlantDetails:checked").length;
                $(".SelectPlantDetails:checked").each(function () {
                    var id = $(this).parent().parent().attr("id");
                    RowiD = id;

                    var rowdataID = $("#IdTblPlantGrid").getCell(id, "ProjectPlantDetailsId");
                    var jsondata =
                        {
                            PlantsDetailerID: rowdataID
                        };
                    arr_checkedId.push(jsondata);
                })
                if (checkedlen > 0) {

                    $.ajax({
                        url: AbsolutePath('/Project/DeleteProjectPlantsDetailer'),
                        data: { "ID": arr_checkedId, "length": checkedlen },
                        type: "POST",
                        async: false,
                        cache: false,
                        success: function (respdata, status, xhr) {
                            if (respdata == "1") {
                                PlantGridForProjects();

                                AlertDialog("Deleted Successfully", "success");
                                for (var i = RowIDCollection_Add.length; i > 0; i--) {
                                    RowIDCollection_Add.pop(i);
                                }
                                for (var j = Arr_Edit.length; j > 0; j--) {
                                    Arr_Edit.pop(j);
                                }
                                for (var k = arr_checkedId.length; k > 0; k--) {
                                    arr_checkedId.pop(k);
                                }
                            } else {
                                AlertDialog("Fail to Delete", "warning");
                            }
                        }
                    })

                }
            }
        })

        $("#IdTblPlantGrid").navButtonAdd("#IdDivPlantPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                for (var i = RowIDCollection_Add.length; i > 0; i--) {
                    RowIDCollection_Add.pop(i);
                }
                for (var j = Arr_Edit.length; j > 0; j--) {
                    Arr_Edit.pop(j);
                }

                PlantGridForProjects();
            }
        });



    };
    // ShortSupply Detailss
    function ShortSupplyDetailsForProjects() {

        $("#IdTblShortSupplyGrid").GridUnload();
        $("#IdTblShortSupplyGrid").jqGrid({
            url: AbsolutePath("/Project/GetShortSupplyProjectDetails?ProjectIC=" + ProjectID),
            datatype: "json",
            mtype: "GET",
            height: 'auto',
            width: '850',
            viewrecords: true,
            rownumbers: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            caption: "Short Supply Details",
            pager: "#IdDivShortSupplyPager",
            colModel: [


                 {
                     name: "ShortSupplyDetailsIC", label: "ShortSupplyDetailsIC", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

                 },
                 {
                     name: "ProjectID", label: "ProjectID", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

                 },
                 {
                     name: "EquipmentName", label: "Equipment", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,

                 },
                  {
                      name: "Item_Name", label: "Item Name", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,

                  },
                 {
                     name: "Quantity", label: "Quantity", width: "100", align: "right", resizable: true, sortable: true, search: true, searchable: true, editable: true

                 },

              //
               {
                   name: "ExpectedDeliveryDate", label: "Expected DeliveryDate", width: "220", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,

               },
 {
     name: "Supply_StatusIC", label: "Supply_StatusIC", width: "20", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

 },
      {
          name: "ActualDeliveryDate", label: "Actual DeliveryDate", width: "220", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true

      },
      {
          name: "IE_EmployeeIC", label: " IE_EmployeeIC", width: "20", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, hidden: true

      },
       {
           name: "ShortSupplyStatus", label: " Status", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true

       },
       {
           name: "Company_Employee_Name", label: "Installation Engineer", width: "180", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true

       },
        {
            name: "LOG_Date", label: "LOG Date", width: "180", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true

        },
                 {
                     name: "Remarks", label: "Remarks", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true

                 },
              //


            ],

            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');


            },
            sortname: "ShortSupplyDetailsIC",
            sortorder: "asc",
            jsonReader:
           {
               root: "rows",
               total: "total",
               records: "records",
               repeatitems: false,
           },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); }
        });
        $("#IdTblShortSupplyGrid").navGrid("#IdDivShortSupplyPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblShortSupplyGrid').jqGrid('filterToolbar',
            {
                resetIcon: "",
                defaultSearch: "cn",
                stringResult: true,
            });




        $("#IdTblShortSupplyGrid").navButtonAdd("#IdDivShortSupplyPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {


                ShortSupplyDetailsForProjects();
            }
        });

        $("#IdTblShortSupplyGrid").navButtonAdd("#IdDivShortSupplyPager",

            {
            title: 'Export',
            caption: "",
            buttonicon: 'ui-icon-bookmark',
            onClickButton: function ()
            {
               
                window.location.href = AbsolutePath("/Project/ExportToexcelShortSupply?ProjectId=" + ProjectID)
            }
        });

    };


    function EditImageFunPlantDetails(id) {

        var RowId = Number(id.split('_')[1]);
        var Row_Data = $("#IdTblPlantGrid").getRowData(RowId);

        $("#IdTblPlantGrid").find("tr").eq(RowId).find("input[type=checkbox]").attr("disabled", false);
        $("#IdTblPlantGrid").find("tr").eq(RowId).find("input[type=checkbox]").attr("name", "CheckPlusContact");
        $("#IdTblPlantGrid").editRow(RowId, { key: true });
        $("#IdTblPlantGrid").find("tr").eq(RowId).find("#IdPencilIconContact_" + RowId).replaceWith("<i class='fa fa-refresh ClsRefreshIconContact' style='color:#041e42;cursor:pointer'  title='Click Here For Save' id='IdRefreshIconContact_" + RowId + "' onclick='ChangeRefreshContact(id)' >");
        Arr_Edit.push(RowId);
        var valueofitemtype = 0;
        var SelectedItemType = 0;
        var SelectedModel = 0;
        valueofitemtype = Row_Data["ItemType_ID"];
        SelectedModel = Row_Data["ItemType_model_ID"];
        $("#" + RowId + "_ItemTypeName").css('width', '100%');
        $.ajax({
            url: AbsolutePath('/Project/GetMOdels?Itemtype=' + valueofitemtype),
            async: false,
            cache: false,
            success: function (resdata) {


                var IC;
                $("#" + RowId + "_SIC_Models_Name").css('width', '100%');
                $('#' + $($(IdTblPlantGrid).getCell(RowId, 'SIC_Models_Name')).attr('id')).empty();
                var Modelnames = "<option value='0'>---Select---</option>";
                if (resdata.length != 0) {


                    $.each(resdata, function (index, value) {

                        if (index == 0) {
                            Modelnames = Modelnames + "<option value=" + value.SIC_ModelsIC + ">" + value.SIC_Models_Name.toString() + "</option>";
                        }
                        else {
                            Modelnames = Modelnames + "<option value=" + value.SIC_ModelsIC + ">" + value.SIC_Models_Name.toString() + "</option>";
                        }
                        if (SelectedModel == value.SIC_ModelsIC) {
                            SelectedModel = value.SIC_ModelsIC;

                        }
                        else {
                        }
                    });
                    $('#' + $($(IdTblPlantGrid).getCell(RowId, 'SIC_Models_Name')).attr('id')).append(Modelnames);


                    $("#" + $($(IdTblPlantGrid).getCell(RowId, "SIC_Models_Name")).attr('id')).val(SelectedModel);
                } else {
                    $('#' + $($(IdTblPlantGrid).getCell(RowId, 'SIC_Models_Name')).attr('id')).append(Modelnames);
                }
            },
            error: function () {
            },

        })
    }
    function ChangeRefreshContact(id) {



        var RowId = Number(id.split('_')[1]);
        $("#IdTblPlantGrid").find("tr").eq(RowId).find("input[type=checkbox]").attr("disabled", true);
        var IsActive = $("#IdRefreshIconContact_" + RowId).parents("tr").find(".ClsChkIsActiveContact").prop("checked");
        $("#IdTblPlantGrid").restoreRow(RowId);

        $("#IdTblPlantGrid").find("tr").eq(RowId).find("#IdRefreshIconContact_" + RowId).replaceWith("<i class='fa fa-pencil ClsPencilIconContact' style='color:#041e42;cursor:pointer' title='Click Here For Edit' id='IdPencilIconContact_" + RowId + "' onclick='EditImageFunPlantDetails(id)'  ></i>");
        var index = Arr_Edit.indexOf(RowId);
        Arr_Edit.splice(index, 1);

    }


    //=========================================================================================//

    //$("#IdTxtProjCreatedDate").datepicker({ "minDate": "+1D", "dateFormat": "dd-M-yy" });
    $("#txt_SOorHFMDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    })
    $("#IdTxtProjDODateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtICNDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjLayoutApproval").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjFirstMobilizeDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjFirstDeMobilizeDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjSecondMobilizationDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjSecondDeMobilizationDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjThirdMobilizationDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjThirdDeMobilizationDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjClNoteRecievedForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {
            //var todaydateval = new Date();
            //var CInoteRecevied = new Date(date);
            //var diffDays = todaydateval.getDate() - CInoteRecevied.getDate();
            //$("#IdTxtProjDaysTillNow").val(diffDays);
            changeDaytillNow();


        }
    });

    function changeDaytillNow()
    {


        var date1 = null;
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf("MSIE ");
        if (msie > 0) {
            date1 = new Date($(IdTxtProjClNoteRecievedForAccordion).val());
        } else {
            date1 = $(IdTxtProjClNoteRecievedForAccordion).val().trim();
            date1 = date1.replace("-", " ");
            date1 = new Date(date1);

        } 
        var date2 = new Date();
        var timeDiff = Math.abs(date2.getTime() - date1.getTime());
        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24))
        $("#IdTxtProjDaysTillNow").val(diffDays);
        

    }
    $("#IdTxtProjClientEmialID").blur(function () {

        if ($("#IdTxtProjClientEmialID").val().trim() != "") {
            CommonEmailValidation("#IdTxtProjClientEmialID");
        }

    })
    $("#IdTxt_GSTNumberShipping").blur(function () {
        if ($("#IdTxt_GSTNumberShipping").val().trim() != "") {
            var pat = "^([A-z0-9]+)$";
            var val = $(this).val().trim();
            var res = new RegExp(pat);
            if (res.test(val) == true) {
                $(this).css("border", "1px solid silver");
            } else {
                $(this).css("border", "1px solid red");

                AlertDialog("GST Number accepts only alphanumeric characters eg:27AADCR1240D1ZW", "warning");
                $(this).val("");
            }

        }
    })

    $("#IdTxt_Pono").blur(function () {
        if ($("#IdTxt_Pono").val().trim() != "") {
            var pat = "^([A-z0-9]+)$";
            var val = $(this).val().trim();
            var res = new RegExp(pat);
            if (res.test(val) == true) {
                $(this).css("border", "1px solid silver");
            } else {
                $(this).css("border", "1px solid red");

                AlertDialog("PO Number accepts only alphanumeric characters eg:27AADCR1240D1ZW", "warning");
                $(this).val("");
            }

        }
    })
    function CommonEmailValidation(EmailID) {   //Regular Expression for Email id

        var emailIdText = $(EmailID).val().trim();

        if (emailIdText != "") {
            var input = emailIdText;
            var matchEx = input.match(/\w+([-+.']\w+)*@@\w+([-.]\w+)*\.\w+([-.]\w+)*/g);
            if (matchEx == null || matchEx != input) {
                AlertDialog("Invalid email address", "warning");
                $(EmailID).css("border", "1px solid red");
                $(EmailID).val("");
            }
            else {
                $(EmailID).css("border", "1px solid silver");
            }
        }

    }

    var MonthNmaes = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    $("#IdTxtProjSiteClearanceForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (scdate) {           
            var result = null;
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf("MSIE ");
            if (msie > 0) {
                result = new Date(scdate);
            } else {
                result = $(this).val().trim();
                result = result.replace("-", " ");
                result = new Date(result);

            }

            var NoofContractualdays = $("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim();
            if (NoofContractualdays != "") {
                CalContractualDeliveryDate(result, NoofContractualdays);
            } else {
                $("#IdTxtProjContractualDeliveryForAccordion").val("");
                $(IdTxtProjTargetDate).val("");
                $("#IdTxtProjContractualRemainingDaysForAccordion").val("");
                $("#IdTxtProjRemainingDaysForAccordion").val("");
            }

            AutomaticallyChangeCurrentStatus();
        }
    });
    function CalContractualDeliveryDate(result, NoofContractualdays)
    {      
        result.setDate(result.getDate() + parseInt(NoofContractualdays));
        var dd = result.getDate();
        var mm = result.getMonth() + 1;
        var y = result.getFullYear();
        var resdat = dd + '-' + MonthNmaes[parseInt(mm) - 1] + '-' + y;
        $(IdTxtProjContractualDeliveryForAccordion).val(resdat);
        CalCulateRemainingDays();
        $(IdTxtProjTargetDate).val(resdat);
    }

    function CalProjectedContractualDeliveryDays()
    {
        if ($("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim() !== "")
        {
            if (parseInt($("#txt_Projected_CDD").val().trim()) > parseInt($("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim())) {
                $("#txt_Projected_CDD").val("");
                $("#IdTxtBufferdays").val("");
                AlertDialog("Projected contractual delivery days should be less than contractual delivery days", "warning")
            }

            else
            {
                var Buffer = (parseInt($("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim()) * 1) - (parseInt($("#txt_Projected_CDD").val().trim()) * 1);
                $("#IdTxtBufferdays").val(Buffer);

                var result = $("#IdTxtProjSiteClearanceForAccordion").val();
                result = result.replace("-", " ");
                result = new Date(result);
                var NoofContractualdays = $("#txt_Projected_CDD").val().trim()
                CalContractualDeliveryDateForOther(result, NoofContractualdays);
            }
        }
        else {
            $("#txt_Projected_CDD").val("");
            $("#IdTxtBufferdays").val("");
            AlertDialog("Please enter contractual delivery days", "warning")
        }


    };
    function CalContractualDeliveryDateForOther(result, NoofContractualdays)
    {       
      
        result.setDate(result.getDate() + parseInt(NoofContractualdays));          
        var dd = result.getDate();
        var mm = result.getMonth() + 1;
        var y = result.getFullYear();
        var resdat = dd + '-' + MonthNmaes[parseInt(mm) - 1] + '-' + y;
        Contractual_DeliveryDate_Othersvalue = resdat;      
    }
    $(document).on('blur', "#txt_Projected_CDD", function ()
    {
        if ($(this).val().trim() != "")
        {
            var pattern = "^[0-9]+$";
            var exp = new RegExp(pattern);
            var result = exp.test($("#txt_Projected_CDD").val().trim());
            if (result != true) {
                $("#txt_Projected_CDD").val('');
                $("#txt_Projected_CDD").css('border', 'solid 1px red').val("").focus();
                AlertDialog("Projected contractual delivery days allows values like ex:35, 45", "warning");

            } else {
                $("#txt_Projected_CDD").css('border', '');
                CalProjectedContractualDeliveryDays();
            }
        }

    });



    function CalCulateRemainingDays() {

        var date1 = null;
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf("MSIE ");
        if (msie > 0) {
            date1 = new Date($(IdTxtProjContractualDeliveryForAccordion).val());
        } else {
            date1 = $(IdTxtProjContractualDeliveryForAccordion).val().trim();
            date1 = date1.replace("-", " ");
            date1 = new Date(date1);

        }
        //   var date1 = new Date($(IdTxtProjContractualDeliveryForAccordion).val());
        var date2 = new Date();
        var timeDiff = Math.abs(date2.getTime() - date1.getTime());
        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24))
        $("#IdTxtProjContractualRemainingDaysForAccordion").val(diffDays);
        $("#IdTxtProjRemainingDaysForAccordion").val(diffDays);
    }

    $("#IdTxtProjContractualDeliveryDaysForAccordion").blur(function () {

        var valcd = $("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim();
        if (valcd != "") {
            if (isNaN(valcd)) {
                AlertDialog("Contractual Delivery Days accepts only numbers between 0 to 150", "warning");
                $("#IdTxtProjContractualDeliveryDaysForAccordion").val("").css('border', '1px solid red');
            } else {

                if ((parseInt(valcd) >= 0) && (parseInt(valcd) <= 150)) {
                    $("#IdTxtProjContractualDeliveryDaysForAccordion").css('border', '1px solid gray');
                    if ($(IdTxtProjSiteClearanceForAccordion).val().trim() != "") {

                        var NoofContractualdays = $("#IdTxtProjContractualDeliveryDaysForAccordion").val().trim();
                        var result = null;
                        var ua = window.navigator.userAgent;
                        var msie = ua.indexOf("MSIE ");
                        if (msie > 0) {
                            result = new Date($(IdTxtProjSiteClearanceForAccordion).val());
                        } else {
                            result = $(IdTxtProjSiteClearanceForAccordion).val().trim();
                            result = result.replace("-", " ");
                            result = new Date(result);

                        }


                        CalContractualDeliveryDate(result, NoofContractualdays)
                    } else {
                        AlertDialog("Please enter dispatch clearance", "warning");

                    }

                } else {
                    $("#IdTxtProjContractualDeliveryDaysForAccordion").val("").css('border', '1px solid red');
                    AlertDialog("Contractual Delivery Days accepts only numbers between 0 to 150", "warning");
                }

            }

        } else {
            $(IdTxtProjContractualDeliveryForAccordion).val("");
            $(IdTxtProjTargetDate).val("");
            $("#IdTxtProjContractualRemainingDaysForAccordion").val("");
            $("#IdTxtProjRemainingDaysForAccordion").val("");
        }


    })

    $("#IdTxtProjContractualDeliveryForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjTargetDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });


    $("#IdTxtPODate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtLOIDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjMobilizationDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {
            AutomaticallyChangeCurrentStatus();
            $("#IdTxtProjFirstMobilizeDate").val($(this).val());
        }
    });
    $("#IdTxtProjComissionedDateForAccordion,#IdTxtProjMobilizationDate,#IdTxtLOIDate,#IdTxtPODate,#IdTxtProjTargetDate,#IdTxtProjContractualDeliveryForAccordion,#txt_SOorHFMDate,#IdTxtICNDate,#IdTxtProjSiteClearanceForAccordion").keypress(function (event) {
        event.preventDefault();
    }).on('keydown', function (e) {
        if (e.keyCode == 8)
            event.preventDefault();
    });


    $("#IdTxtProjFirstMobilizeDate,#IdTxtProjFirstDeMobilizeDateForAccordion,#IdTxtProjSecondMobilizationDateForAccordion,#IdTxtProjSecondDeMobilizationDate,#IdTxtProjThirdMobilizationDateForAccordion,#IdTxtProjThirdDeMobilizationDateForAccordion,#IdTxtProjfouthMobilizationDateForAccordion,#IdTxtProjforthDeMobilizationDateForAccordion").keypress(function (event) {
        event.preventDefault();
    }).on('keydown', function (e) {
        if (e.keyCode == 8)
            event.preventDefault();
    });


    $("#IdTxtProjDODateForAccordion,#IdTxtProjClNoteRecievedForAccordion,#IdTxtProjLayoutApproval,#IdTxtPODate,#IdTxtProjTargetDate,#IdTxtProjContractualDeliveryForAccordion,#txt_SOorHFMDate,#IdTxtICNDate,#IdTxtProjSiteClearanceForAccordion").keypress(function (event) {
        event.preventDefault();
    }).on('keydown', function (e) {
        if (e.keyCode == 8)
            event.preventDefault();
    });



    $("#IdTxtProjComissionedDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {
            AutomaticallyChangeCurrentStatus();
        }
    });
    $("#IdTxtProjClientDGReadinessDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjInComingCableReadinessDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#txtPOReceivedDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#txtSupplyCompletionDate").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {
            AutomaticallyChangeCurrentStatus();
        }
    });
    $("#IdTxtProjfouthMobilizationDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjforthDeMobilizationDateForAccordion").datepicker({
        dateFormat: "dd-M-yy",
        showOtherMonths: true,
        selectOtherMonths: true,
        changeMonth: true,
        changeYear: true,

        onSelect: function (date) {

        }
    });
    $("#IdTxtProjforthDeMobilizationDateForAccordion,#IdTxtProjfouthMobilizationDateForAccordion,#txtSupplyCompletionDate,#txtPOReceivedDate,#IdTxtProjInComingCableReadinessDateForAccordion,#IdTxtProjClientDGReadinessDateForAccordion").keypress(function (event) {
        event.preventDefault();
    }).on('keydown', function (e) {
        if (e.keyCode == 8)
            event.preventDefault();
    });



    $("#IdTxtSONumber").blur(function () {
        if ($("#IdTxtSONumber").val().trim() != "") {
            $("#ddl_ProjectCurrentStatus").val("2");
        } else {
            $("#ddl_ProjectCurrentStatus").val("1");
        }

    })
    //Validation for Current Status of the Project
    var d = new Date();

    //Function for Current Status Validation

    $("#IdTxtProjComissionedDateForAccordion,#IdTxtProjMobilizationDate,#txtSupplyCompletionDate").blur(function () {
        if ($("#IdTxtProjComissionedDateForAccordion").val().trim() != "" || $("#IdTxtProjMobilizationDate").val().trim() != "" || $("#txtSupplyCompletionDate").val().trim() != "") {
            AutomaticallyChangeCurrentStatus();
        }

    })
    function AutomaticallyChangeCurrentStatus() {
        var SupplyCompitionDate = $("#txtSupplyCompletionDate").val().trim();
        var MobilizationDate = $("#IdTxtProjMobilizationDate").val().trim();

        if ($("#IdTxtProjSiteClearanceForAccordion").val().trim() != "" && SupplyCompitionDate != "") {

            if ($("#IdTxtProjSiteClearanceForAccordion").val().trim() != "" && new Date(SupplyCompitionDate) > new Date() && MobilizationDate == "") {
                $("#ddl_ProjectCurrentStatus").val("3");
            } else if ($("#IdTxtProjSiteClearanceForAccordion").val().trim() != "" && new Date(SupplyCompitionDate) > new Date() && MobilizationDate != "") {
                $("#ddl_ProjectCurrentStatus").val("4");

            }
            else if (new Date(SupplyCompitionDate) < new Date() && MobilizationDate == "") {
                $("#ddl_ProjectCurrentStatus").val("5");
            }
            else if (new Date(SupplyCompitionDate) < new Date() && MobilizationDate != "") {
                $("#ddl_ProjectCurrentStatus").val("6");
            }

        } else {

            if ($("#IdTxtSONumber").val().trim() != "") {
                $("#ddl_ProjectCurrentStatus").val("2");
            } else {
                $("#ddl_ProjectCurrentStatus").val("1");
            }
        }
        if ($("#IdTxtProjComissionedDateForAccordion").val().trim() != "") {
            $("#ddl_ProjectCurrentStatus").val("7");
        }
    }

  
    $("#chk_GST_Shipping").click(function () {
        if ($("#chk_GST_Shipping").prop("checked") == true)
        {
           
            $("#IdTxt_GSTNumberBilling").val($("#IdTxt_GSTNumberShipping").val());
        }
        else
        {
            $("#IdTxt_GSTNumberBilling").val("");
        }
    })

    //Multiple Image

    var ArrayOfFiles = [];


    $("#FileAttchments").change(function (event) {
        // count++;
        var path = URL.createObjectURL(event.target.files[0]);

        var File = $(this).prop("files")[0];

        //ArrayOfFiles.push({
        //    c: count,
        //    Name: File.name,
        //    FileDetails: File
        //});
        ArrayOfFiles.push({

            IPHeaderIC: 7,
            IPDetailerIC: 6,
            IEStatusIC: 1,
            SEStatusIC: 1,
            IERemarks: "Ok done",
            SERemarks: "Ok done Se",
            Attachment: File
        });
        console.log(ArrayOfFiles)

        $(this).val("");

    });
    $(btn_test).click(function () {

        var IPDetailerlist_Array = []
        var IPElectricalHeaderlist_Array = [];
        var IPElectricalMotorDatalist_Array = [];
        var IPElecCommissioningDatalist_Array = [];
        var IPElecPreCommissioningDatalist_Array = [];
        var IPElecInstallationCheckupDatalist = [];


        var EquipmentDetailsHeader = [];
        var EquipmentDetailsDetailer = [];
        var Json_EquipmentDetailsHeader = {
            HeaderIC: "7",
            ProjectIC: 17,
            IP_SpecIC: 8,
            SpectPoint1Initialvalue: "A1 12,A2 34,B1 12,B2 56,C1 23,C2 45",
            SpecPoints2Intialvalue: "",
            SpecPoints3intialValue: "",
            SpectPoint1value: "A1 1,A2 2,B1 3,B2 4,C1 5,C2 6",
            SpecPoints2value: "",
            SpecPoints3Value: "",
            type: "0",
            Usertype: "SICRIE",
            ProtocolStatus: "1",
            Loggedin_EMPIC: 68,
            StageName: "Primary"
        }

        var Final_arry = [];
        //for (var i = 0; i < length; i++) {
        //var formdata
        //}




        var formdata = new FormData();

        for (var i = 0; i < ArrayOfFiles.length; i++) {
            formdata.append("File" + i, ArrayOfFiles[i].Attachment)
        }



        var fnal_json = {

            IPHeaderIC: 7,
            IPDetailerIC: 6,
            IEStatusIC: 1,
            SEStatusIC: 1,
            IERemarks: "Ok done",
            SERemarks: "Ok done Se",
            AttachementImgName: formdata

        }
        var Final_Array = {
            IPDetailerlist: fnal_json

        }

        EquipmentDetailsHeader.push(Json_EquipmentDetailsHeader);
        // EquipmentDetailsDetailer.push(Json_EquipmentDetailsDetailer)
        var FinalEquipmentDetailsHeaderList = {
            IPHeaderlist: EquipmentDetailsHeader,
            IPDetailerlist: IPDetailerlist_Array,
            IPElectricalHeaderlist: IPElectricalHeaderlist_Array,
            IPElectricalMotorDatalist: IPElectricalMotorDatalist_Array,
            IPElecCommissioningDatalist: IPElecCommissioningDatalist_Array,
            IPElecPreCommissioningDatalist: IPElecPreCommissioningDatalist_Array,
            IPElecInstallationCheckupDatalist: IPElecInstallationCheckupDatalist
        }
        console.log("FinalEquipmentDetailsHeaderList");
        console.log(FinalEquipmentDetailsHeaderList);
        console.log(fnal_json);
        // Loading.show();
        $.ajax({
            url: "http://localhost:55273/api/MicpApi/UpdateInstalltionProtocolDetails",
            // url: "http://************/Metso_MICP/api/MicpApi/UpdateInstalltionProtocolDetails",
            type: "POST",
            data: JSON.stringify(FinalEquipmentDetailsHeaderList),
            contentType: "application/json",
        }).then(function success(response) {
            console.log(response);
            if (response == "1") {
                //Toast.show("Saved Successfully");
                //if ($rootScope.DesignationID == "SICIE") {
                //    // $state.go("ProjectLanding");
                //}
                //else if ($rootScope.DesignationID == "43" && $rootScope.Company_ID == 1) {
                //    //$state.go("TechnitianHomePage");
                //}
                //Loading.show();
                $.ajax({
                    url: "http://localhost:55273/api/MicpApi/UpdateInstallationProtocolDetailerDetails",
                    // url: "http://************/Metso_MICP/api/MicpApi/UpdateInstallationProtocolDetailerDetails",
                    type: "POST",

                    data: JSON.stringify(Final_Array),
                    contentType: "application/json",
                }).then(function success(response) {

                }, function myError(response) {
                    console.log("Equipment Details Detailer Error");
                    console.log(response);
                    // Loading.hide();

                });//ajax

            }

            // Loading.hide();

        }, function myError(response) {
            console.log("Equipment Details header Error");
            console.log(response);
            //Loading.hide();

        });//ajax
     
    })



    //=========================================================================================//

</script>
