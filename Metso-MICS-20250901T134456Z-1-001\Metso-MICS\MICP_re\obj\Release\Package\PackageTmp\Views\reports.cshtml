﻿@{
    Layout = null;
}
<html>
<head>
	<title>Table V03</title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">

<!--===============================================================================================-->
    <style>
        
/* ------------------------------------ */
.text-white {color: white;}
.text-black {color: black;}
/*.m-b-110 {margin-bottom: 0px;}*/
.text-hov-white:hover {color: white;}

/* ------------------------------------ */
.text-up {text-transform: uppercase;}

/* ------------------------------------ */
.text-center {text-align: center;}
.text-left {text-align: left;}
.text-right {text-align: right;}
.text-middle {vertical-align: middle;}

/* ------------------------------------ */
.lh-1-0 {line-height: 1.0;}
.lh-1-1 {line-height: 1.1;}
.lh-1-2 {line-height: 1.2;}
.lh-1-3 {line-height: 1.3;}
.lh-1-4 {line-height: 1.4;}
.lh-1-5 {line-height: 1.5;}
.lh-1-6 {line-height: 1.6;}
.lh-1-7 {line-height: 1.7;}
.lh-1-8 {line-height: 1.8;}
.lh-1-9 {line-height: 1.9;}
.lh-2-0 {line-height: 2.0;}
.lh-2-1 {line-height: 2.1;}
.lh-2-2 {line-height: 2.2;}
.lh-2-3 {line-height: 2.3;}
.lh-2-4 {line-height: 2.4;}
.lh-2-5 {line-height: 2.5;}
.lh-2-6 {line-height: 2.6;}
.lh-2-7 {line-height: 2.7;}
.lh-2-8 {line-height: 2.8;}
.lh-2-9 {line-height: 2.9;}





/*[ SHAPE ]
///////////////////////////////////////////////////////////
*/

/*[ Display ]
-----------------------------------------------------------
*/
.dis-none {display: none;}
.dis-block {display: block;}
.dis-inline {display: inline;}
.dis-inline-block {display: inline-block;}
.dis-flex {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
}

/*[ Position ]
-----------------------------------------------------------
*/
.pos-relative {position: relative;}
.pos-absolute {position: absolute;}
.pos-fixed {position: fixed;}

/*[ float ]
-----------------------------------------------------------
*/
.float-l {float: left;}
.float-r {float: right;}


/*[ Width & Height ]
-----------------------------------------------------------
*/
.sizefull {
	width: 100%;
	height: 100%;
}
.w-full {width: 100%;}
.h-full {height: 100%;}
.max-w-full {max-width: 100%;}
.max-h-full {max-height: 100%;}
.min-w-full {min-width: 100%;}
.min-h-full {min-height: 100%;}

/*[ Top Bottom Left Right ]
-----------------------------------------------------------
*/
.top-0 {top: 0;}
.bottom-0 {bottom: 0;}
.left-0 {left: 0;}
.right-0 {right: 0;}

.top-auto {top: auto;}
.bottom-auto {bottom: auto;}
.left-auto {left: auto;}
.right-auto {right: auto;}


/*[ Opacity ]
-----------------------------------------------------------
*/
.op-0-0 {opacity: 0;}
.op-0-1 {opacity: 0.1;}
.op-0-2 {opacity: 0.2;}
.op-0-3 {opacity: 0.3;}
.op-0-4 {opacity: 0.4;}
.op-0-5 {opacity: 0.5;}
.op-0-6 {opacity: 0.6;}
.op-0-7 {opacity: 0.7;}
.op-0-8 {opacity: 0.8;}
.op-0-9 {opacity: 0.9;}
.op-1-0 {opacity: 1;}

/*[ Background ]
-----------------------------------------------------------
*/
.bgwhite {background-color: white;}
.bgblack {background-color: black;}



/*[ Wrap Picture ]
-----------------------------------------------------------
*/
.wrap-pic-w img {width: 100%;}
.wrap-pic-max-w img {max-width: 100%;}

/* ------------------------------------ */
.wrap-pic-h img {height: 100%;}
.wrap-pic-max-h img {max-height: 100%;}

/* ------------------------------------ */
.wrap-pic-cir {
	border-radius: 50%;
	overflow: hidden;
}
.wrap-pic-cir img {
	width: 100%;
}



/*[ Hover ]
-----------------------------------------------------------
*/
.hov-pointer:hover {cursor: pointer;}

/* ------------------------------------ */
.hov-img-zoom {
	display: block;
	overflow: hidden;
}
.hov-img-zoom img{
	width: 100%;
	-webkit-transition: all 0.6s;
    -o-transition: all 0.6s;
    -moz-transition: all 0.6s;
    transition: all 0.6s;
}
.hov-img-zoom:hover img {
	-webkit-transform: scale(1.1);
  	-moz-transform: scale(1.1);
  	-ms-transform: scale(1.1);
  	-o-transform: scale(1.1);
	transform: scale(1.1);
}



/*[  ]
-----------------------------------------------------------
*/
.bo-cir {border-radius: 50%;}

.of-hidden {overflow: hidden;}

.visible-false {visibility: hidden;}
.visible-true {visibility: visible;}




/*[ Transition ]
-----------------------------------------------------------
*/
.trans-0-1 {
	-webkit-transition: all 0.1s;
    -o-transition: all 0.1s;
    -moz-transition: all 0.1s;
    transition: all 0.1s;
}
.trans-0-2 {
	-webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;
}
.trans-0-3 {
	-webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    -moz-transition: all 0.3s;
    transition: all 0.3s;
}
.trans-0-4 {
	-webkit-transition: all 0.4s;
    -o-transition: all 0.4s;
    -moz-transition: all 0.4s;
    transition: all 0.4s;
}
.trans-0-5 {
	-webkit-transition: all 0.5s;
    -o-transition: all 0.5s;
    -moz-transition: all 0.5s;
    transition: all 0.5s;
}
.trans-0-6 {
	-webkit-transition: all 0.6s;
    -o-transition: all 0.6s;
    -moz-transition: all 0.6s;
    transition: all 0.6s;
}
.trans-0-9 {
	-webkit-transition: all 0.9s;
    -o-transition: all 0.9s;
    -moz-transition: all 0.9s;
    transition: all 0.9s;
}
.trans-1-0 {
	-webkit-transition: all 1s;
    -o-transition: all 1s;
    -moz-transition: all 1s;
    transition: all 1s;
}



/*[ Layout ]
///////////////////////////////////////////////////////////
*/

/*[ Flex ]
-----------------------------------------------------------
*/
/* ------------------------------------ */
.flex-w {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-wrap: wrap;
	-moz-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	-o-flex-wrap: wrap;
	flex-wrap: wrap;
}

/* ------------------------------------ */
.flex-l {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: flex-start;
}

.flex-r {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: flex-end;
}
caption {
    padding-top: 8px;
    padding-bottom: 8px;
    color: white;
    text-align: center;
    font-family: sans-serif;
    font-weight: bold;
   background-color: #327d78;
    border-radius: 5px;
    font-size: 18px;
}
.flex-c {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: center;
}

.flex-sa {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-around;
}

.flex-sb {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-between;
}

/* ------------------------------------ */
.flex-t {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-ms-align-items: flex-start;
	align-items: flex-start;
}

.flex-b {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-ms-align-items: flex-end;
	align-items: flex-end;
}

.flex-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-ms-align-items: center;
	align-items: center;
}

.flex-str {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-ms-align-items: stretch;
	align-items: stretch;
}

/* ------------------------------------ */
.flex-row {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: row;
	-moz-flex-direction: row;
	-ms-flex-direction: row;
	-o-flex-direction: row;
	flex-direction: row;
}

.flex-row-rev {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: row-reverse;
	-moz-flex-direction: row-reverse;
	-ms-flex-direction: row-reverse;
	-o-flex-direction: row-reverse;
	flex-direction: row-reverse;
}

.flex-col {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
}

.flex-col-rev {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column-reverse;
	-moz-flex-direction: column-reverse;
	-ms-flex-direction: column-reverse;
	-o-flex-direction: column-reverse;
	flex-direction: column-reverse;
}

/* ------------------------------------ */
.flex-c-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: center;
	-ms-align-items: center;
	align-items: center;
}

.flex-c-t {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: center;
	-ms-align-items: flex-start;
	align-items: flex-start;
}

.flex-c-b {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: center;
	-ms-align-items: flex-end;
	align-items: flex-end;
}

.flex-c-str {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: center;
	-ms-align-items: stretch;
	align-items: stretch;
}

.flex-l-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: flex-start;
	-ms-align-items: center;
	align-items: center;
}

.flex-r-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: flex-end;
	-ms-align-items: center;
	align-items: center;
}

.flex-sa-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-around;
	-ms-align-items: center;
	align-items: center;
}

.flex-sb-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-between;
	-ms-align-items: center;
	align-items: center;
}

/* ------------------------------------ */
.flex-col-l {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	-ms-align-items: flex-start;
	align-items: flex-start;
}

.flex-col-r {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	-ms-align-items: flex-end;
	align-items: flex-end;
}

.flex-col-c {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	-ms-align-items: center;
	align-items: center;
}

.flex-col-l-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	-ms-align-items: flex-start;
	align-items: flex-start;
	justify-content: center;
}

.flex-col-r-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	-ms-align-items: flex-end;
	align-items: flex-end;
	justify-content: center;
}

.flex-col-c-m {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	-ms-align-items: center;
	align-items: center;
	justify-content: center;
}

.flex-col-str {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	-ms-align-items: stretch;
	align-items: stretch;
}

.flex-col-sb {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column;
	-moz-flex-direction: column;
	-ms-flex-direction: column;
	-o-flex-direction: column;
	flex-direction: column;
	justify-content: space-between;
}

/* ------------------------------------ */
.flex-col-rev-l {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column-reverse;
	-moz-flex-direction: column-reverse;
	-ms-flex-direction: column-reverse;
	-o-flex-direction: column-reverse;
	flex-direction: column-reverse;
	-ms-align-items: flex-start;
	align-items: flex-start;
}

.flex-col-rev-r {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column-reverse;
	-moz-flex-direction: column-reverse;
	-ms-flex-direction: column-reverse;
	-o-flex-direction: column-reverse;
	flex-direction: column-reverse;
	-ms-align-items: flex-end;
	align-items: flex-end;
}

.flex-col-rev-c {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column-reverse;
	-moz-flex-direction: column-reverse;
	-ms-flex-direction: column-reverse;
	-o-flex-direction: column-reverse;
	flex-direction: column-reverse;
	-ms-align-items: center;
	align-items: center;
}

.flex-col-rev-str {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-flex-direction: column-reverse;
	-moz-flex-direction: column-reverse;
	-ms-flex-direction: column-reverse;
	-o-flex-direction: column-reverse;
	flex-direction: column-reverse;
	-ms-align-items: stretch;
	align-items: stretch;
}


/*[ Absolute ]
-----------------------------------------------------------
*/
.ab-c-m {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
  	-moz-transform: translate(-50%, -50%);
  	-ms-transform: translate(-50%, -50%);
  	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

.ab-c-t {
	position: absolute;
	top: 0px;
	left: 50%;
	-webkit-transform: translateX(-50%);
  	-moz-transform: translateX(-50%);
  	-ms-transform: translateX(-50%);
  	-o-transform: translateX(-50%);
	transform: translateX(-50%);
}

.ab-c-b {
	position: absolute;
	bottom: 0px;
	left: 50%;
	-webkit-transform: translateX(-50%);
  	-moz-transform: translateX(-50%);
  	-ms-transform: translateX(-50%);
  	-o-transform: translateX(-50%);
	transform: translateX(-50%);
}

.ab-l-m {
	position: absolute;
	left: 0px;
	top: 50%;
	-webkit-transform: translateY(-50%);
  	-moz-transform: translateY(-50%);
  	-ms-transform: translateY(-50%);
  	-o-transform: translateY(-50%);
	transform: translateY(-50%);
}

.ab-r-m {
	position: absolute;
	right: 0px;
	top: 50%;
	-webkit-transform: translateY(-50%);
  	-moz-transform: translateY(-50%);
  	-ms-transform: translateY(-50%);
  	-o-transform: translateY(-50%);
	transform: translateY(-50%);
}

.ab-t-l {
	position: absolute;
	left: 0px;
	top: 0px;
}

.ab-t-r {
	position: absolute;
	right: 0px;
	top: 0px;
}

.ab-b-l {
	position: absolute;
	left: 0px;
	bottom: 0px;
}

.ab-b-r {
	position: absolute;
	right: 0px;
	bottom: 0px;
}





@@font-face {
  font-family: Montserrat-Regular;
  src: url('../fonts/montserrat/Montserrat-Regular.ttf'); 
}

@@font-face {
  font-family: Montserrat-Medium;
  src: url('../fonts/montserrat/Montserrat-Medium.ttf'); 
}

/*//////////////////////////////////////////////////////////////////
[ RESTYLE TAG ]*/
* {
	/*margin: 0px;*/ 
	padding: 0px; 
	box-sizing: border-box;
}

body, html {
	height: 100%;
	font-family: sans-serif;
}

/* ------------------------------------ */
a {
	margin: 0px;
	transition: all 0.4s;
	-webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
}

a:focus {
	outline: none !important;
}

a:hover {
	text-decoration: none;
}

/* ------------------------------------ */
h1,h2,h3,h4,h5,h6 {margin: 0px;}

p {margin: 0px;}

ul, li {
	margin: 0px;
	list-style-type: none;
}


/* ------------------------------------ */
/*input {
  display: block;
	outline: none;
	border: none !important;
}*/

textarea {
  display: block;
  outline: none;
}

textarea:focus, input:focus {
  border-color: transparent !important;
}
        .total {
            background-color:#0e435a
        }

/* ------------------------------------ */
button {
	outline: none !important;
	border: none;
	/*background: transparent;*/
}

button:hover {
	cursor: pointer;
}

iframe {
	border: none !important;
}



/*//////////////////////////////////////////////////////////////////
[ Table ]*/

.limiter {
  width: 100%;
  margin: 0 auto;
}

.container-table100 {
  width: 100%;
  min-height: 100vh;
 /*background: #e2edec;*/

  /*
 
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  
  flex-wrap: wrap;*/

    display: -webkit-flex;
  /*align-items: center;
 
  justify-content: center;*/
  padding: 33px 30px;
}

.wrap-table100 {
  width: 1300px;
}

/*//////////////////////////////////////////////////////////////////
[ Table ]*/
table {
  width: 100%;
  background-color: #fff;
}

th, td {
  font-weight: unset;
  padding-right: 10px;
}

.column100 {
  width: 130px;
  padding-left: 25px;
}

.column100.column1 {
  width: 265px;
  padding-left: 42px;
}

.row100.head th {
  padding-top: 24px;
  padding-bottom: 20px;
}

.row100 td {
  padding-top: 18px;
  padding-bottom: 13px;
}


/*==================================================================
[ Ver1 ]*/
.table100.ver1 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: #808080;
  line-height: 1.4;
}

        .table100.ver1 th {
            font-family: Montserrat-Medium;
            font-size: 12px;
           color: #053e29;
            line-height: 1.4;
            text-transform: uppercase;
            background-color: white;
        }

.table100.ver1 .row100:hover {
  background-color: #f2f2f2;
   color: white
}

.table100.ver1 .hov-column-ver1 {
  background-color: #f2f2f2;
   color: black
}

.table100.ver1 .hov-column-head-ver1 {
  background-color: #484848 !important;
   color: white
}

.table100.ver1 .row100 td:hover {
  background-color: #6c7ae0;
  color: black;
}

/*==================================================================
[ Ver2 ]*/
.table100.ver2 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: #808080;
  line-height: 1.4;
}

.table100.ver2 th {
  font-family: Montserrat-Medium;
  font-size: 12px;
  color: #fff;
  line-height: 1.4;
  text-transform: uppercase;

  background-color: #333333;
}

.table100.ver2 .row100:hover td {
  background-color: #83d160;
  color: #fff;
}

.table100.ver2 .hov-column-ver2 {
  background-color: #83d160;
  color: #fff;
}

.table100.ver2 .hov-column-head-ver2 {
  background-color: #484848 !important;
}

.table100.ver2 .row100 td:hover {
  background-color: #57b846;
  color: #fff;
}
        .verb1otla {
             background-color:#f9ffbe;
        }

/*==================================================================
[ Ver2 ]*/
.table100.ver2 tbody tr:nth-child(even) {
  background-color: #eaf8e6;
}

.table100.ver2 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: #808080;
  line-height: 1.4;
}

.table100.ver2 th {
  font-family: Montserrat-Medium;
  font-size: 12px;
  color: #fff;
  line-height: 1.4;
  text-transform: uppercase;

  background-color: #333333;
}

.table100.ver2 .row100:hover td {
  background-color: #83d160;
  color: #fff;
}

.table100.ver2 .hov-column-ver2 {
  background-color: #83d160;
  color: #fff;
}

.table100.ver2 .hov-column-head-ver2 {
  background-color: #484848 !important;
}

.table100.ver2 .row100 td:hover {
  background-color: #57b846;
  color: #fff;
}

/*==================================================================
[ Ver3 ]*/

table {
    width: 100%;
    background-color: #fff;
    box-shadow: 2px 3px 4px 1px #66776c;
}
.table100.ver3 tbody tr {
  border-bottom: 2px solid #97d4a9;
}
.table100.ver3 tbody td {
     border-right: solid #97d4a9 2px;
    
}

.table100.ver3 tbody tr:nth-child(even) {
    background-color: #eaf8e6;
}
.table100.ver3 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: black;
  font-weight:bold;
  line-height: 1.4;
}

.table100.ver3 th {
  font-family: Montserrat-Medium;
 font-size: 13px;
  color: #fff;
  line-height: 1.4;
  font-weight:bold;
  text-transform: uppercase;
  /*background-color: #547171;*/
  /*background-color:#75adad;*/
  background-color:#419898
  
}

.table100.ver3 .row100:hover td {
  /*background-color: #fcebf5;*/
  color:black;
   
}

.table100.ver3 .hov-column-ver3 {
  /*background-color: #fcebf5;*/
  color:black;
  
}

.table100.ver3 .hov-column-head-ver3 {
  /*background-color: #7b88e3 !important;*/
  color:black;
   
}

.table100.ver3 .row100 td:hover {
  /*background-color: #e03e9c;*/
  color:black;
   
}
/*//-----------------*/
.table101.ver3 tbody tr {
  border-bottom: 2px solid #97d4a9;
}
.table101.ver3 tbody td {
     border-right: solid #97d4a9 2px;
}

.table101.ver3 tbody tr:nth-child(even) {
    background-color: #eaf8e6;
}
.table101.ver3 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: black;
  font-weight:bold;
  line-height: 1.4;
}

.table101.ver3 th {
  font-family: Montserrat-Medium;
  font-size: 13px;
  color: #fff;
  line-height: 1.4;
  font-weight:bold;
  text-transform: uppercase;
  /*background-color: #547171;*/
  /*background-color:#75adad;*/
  background-color:#419898
  
}

.table101.ver3 .row100:hover td {
  /*background-color: #fcebf5;*/
  color:black;
   
}

.table101.ver3 .hov-column-ver3 {
  /*background-color: #fcebf5;*/
  color:black;
  
}

.table101.ver3 .hov-column-head-ver3 {
  /*background-color: #7b88e3 !important;*/
  color:black;
   
}

.table101.ver3 .row100 td:hover {
  /*background-color: #e03e9c;*/
  color:black;
   
}

/*///--------------------*/

.table102.ver3 tbody tr {
  border-bottom: 2px solid #97d4a9;
}
.table102.ver3 tbody td {
     border-right: solid #97d4a9 2px;
}

.table102.ver3 tbody tr:nth-child(even) {
    background-color: #eaf8e6;
}
.table102.ver3 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
 color: black;
  font-weight:bold;
  line-height: 1.4;
}

.table102.ver3 th {
  font-family: Montserrat-Medium;
  font-size: 13px;
  color: #fff;
  line-height: 1.4;
  font-weight:bold;
  text-transform: uppercase;
  /*background-color: #547171;*/
  /*background-color:#75adad;*/
  background-color:#419898
  
}

.table102.ver3 .row100:hover td {
  /*background-color: #fcebf5;*/
  color:black;
   
}

.table102.ver3 .hov-column-ver3 {
  /*background-color: #fcebf5;*/
  color:black;
  
}

.table102.ver3 .hov-column-head-ver3 {
  /*background-color: #7b88e3 !important;*/
  color:black;
   
}

.table102.ver3 .row100 td:hover {
  /*background-color: #e03e9c;*/
  color:black;
   
}


/*==================================================================
[ Ver4 ]*/

.table100.ver4 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: #808080;
  line-height: 1.4;
}

.table100.ver4 th {
  font-family: Montserrat-Medium;
  font-size: 12px;
  color: #fff;
  line-height: 1.4;
  text-transform: uppercase;

  background-color: #fa4251;
}

.table100.ver4 .row100:hover td {
  color: #fa4251;
}

.table100.ver4 .hov-column-ver4 {
  background-color: #ffebed;
}

.table100.ver4 .hov-column-head-ver4 {
  background-color: #f95462 !important;
}

.table100.ver4 .row100 td:hover {
  background-color: #ffebed;
  color: #fa4251;
}

/*==================================================================
[ Ver5 ]*/
.table100.ver5 tbody tr:nth-child(even) {
  background-color: #e9faff;
}

.table100.ver5 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: #808080;
  line-height: 1.4;
  position: relative;
}

.table100.ver5 th {
  font-family: Montserrat-Medium;
  font-size: 12px;
  color: #fff;
  line-height: 1.4;
  text-transform: uppercase;

  background-color: #002933;
}

.table100.ver5 .row100:hover td {
  color: #fe3e64;
}

.table100.ver5 .hov-column-ver5 {color: #fe3e64;}
.table100.ver5 .hov-column-ver5::before {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  border-left: 1px solid #f2f2f2;
  border-right: 1px solid #f2f2f2;
}

.table100.ver5 .hov-column-head-ver5 {
  background-color: #1a3f48 !important;
  color: #fe3e64;
}

.table100.ver5 .row100 td:hover {
  color: #fe3e64;
}

.table100.ver5 .row100 td:hover:before {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  border: 1px solid #fe3e64;
}

/*==================================================================
[ Ver6 ]*/

.table100.ver6 {
  border-radius: 16px;
  overflow: hidden;
  background: #7918f2;
  background: -webkit-linear-gradient(-68deg, #327d78 , #317c77);
  background: -o-linear-gradient(-68deg, #ac32e4 , #4801ff);
  background: -moz-linear-gradient(-68deg, #ac32e4 , #4801ff);
  background: linear-gradient(-68deg, #327d78 , #317c77);
}

.table100.ver6 table {
  background-color: transparent;
}

.table100.ver6 td {
  font-family: Montserrat-Regular;
  font-size: 13px;
  color: #fff;
  line-height: 1.4;
}

.table100.ver6 th {
  font-family: Montserrat-Medium;
  font-size: 12px;
  color: #fff;
  line-height: 1.4;
  text-transform: uppercase;

 background-color: rgba(255,255,255,0.32);
}

.table100.ver6 .row100:hover td {
  background-color: rgba(255,255,255,0.1);
}

.table100.ver6 .hov-column-ver6 {
  /*background-color: rgba(255,255,255,0.1);*/
}


.table100.ver6 .row100 td:hover {
  /*background-color: rgba(255,255,255,0.2);*/
}
 .glyphicon-filter {
        position: relative;
    top: -15px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    color: white;
    left: 3px;
    z-index: 10;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
 .IDBtnReportCls {
        height: 30px;
        background-color: #e4562b;
        color: white;
        border-radius: 5px;
        padding-top: 3px;
        /*width: 126%;*/
    }

    </style>
</head>
<body>
	
	<div class="limiter">
          <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12" style="width: 240px;" id="Iddivfilter" hidden="hidden">
            <div class="panel panel-heading clickable" data-toggle="collapse" data-parent="#accordionstatus" data-target="#PanelQuarterZone" style="background: #327d78; border-radius: 5px; height: auto;">
                <h4 class="panel-title" style="color: white; margin-top: 0px; font-size: 12.3px; font-weight: bold;">State</h4>
                <span class="pull-right"><i class="glyphicon glyphicon-filter"></i></span>
            </div>
            <div id="PanelQuarterZone" class="panel-collapse collapse CustomPanelStyle" style="border: 1px solid rgb(226, 237, 236); margin-top: -20px;background-color: rgb(226, 237, 236);box-shadow: 1px 1px 7px -1px #0e0e10">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 100px; overflow-y: auto;font-size:12px;font-weight:bold;color:#327d78" id="IdDivStateForReports">
                       
                    </div>
                </div>
            </div>
        </div>
        
         <div class="row" style="color:#1f716c; width: 80%; margin-left: 13px; font-weight:bolder;" id="IdRadioFilter" hidden="hidden">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectCode" class="ClsLabelIdleReportTxtBox cslhide" style="font-size:14px">Contractor : </label>&nbsp;<input id="Contractor" checked type="radio" class=" dashboardradio cslhide" name="Dashboard" value="1">
                                        </div>
             &nbsp;&nbsp;
                                       @* <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12">                                           
                                               
                                        </div>*@

                                        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
                                            <label for="IdTxtProjectName" class="ClsLabelIdleReportTxtBox cslhide" style="font-size:14px">Installation Engineer : </label>&nbsp;<input id="Engineer" type="radio" class=" dashboardradio" name="Dashboard" value="2">
                                        </div>
                                       @* <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12">
                                            
                                        </div>*@

                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtClient" class="ClsLabelIdleReportTxtBox cslhide" style="font-size:14px">State : </label>&nbsp;<input id="IdTxtProjectCode" type="radio" class=" dashboardradio cslhide" name="Dashboard" value="3">
                                        </div>
                                       @* <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12">
                                          
                                        </div>*@
             
            @*  <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"> <button type="button" id="IDBtnReportInside" class="btn  IDBtnReportCls ">Generate Report</button></div>*@
                                    </div>
        <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"> <button type="button" id="IDBtnReport" class="btn  IDBtnReportCls ">Generate Report</button></div>
        </div>
            <br />
       @* <div class="row" style="height:850px;">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="overflow-x:scroll;">   *@        
        @*<div class="container-table100" >*@        	     
		@*<div class="wrap-table100">   *@           
				<div class="table100 ver3">
				</div>
				<div class="table101 ver3" hidden:"true" class="row">
                    <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">				
                      <br/>
                    </div>
                    </div>
        <br />
                <div class="table102 ver3" hidden:"true" class="row">
                  
				</div>
                <br />             
			@*</div>*@
		@*</div>*@     
        @*    </div>
        </div>*@

	</div>


	

<!--===============================================================================================-->	
	@*<script src="vendor/jquery/jquery-3.2.1.min.js"></script>
<!--===============================================================================================-->
	<script src="vendor/bootstrap/js/popper.js"></script>
	<script src="vendor/bootstrap/js/bootstrap.min.js"></script>
<!--===============================================================================================-->
	<script src="vendor/select2/select2.min.js"></script>
<!--===============================================================================================-->
	<script src="js/main.js"></script>*@
    <script>
        $(document).ready(function()
        {
            function AlertMessage1(msg, msgType) {
                swal({
                    title: msg,
                    type: msgType,
                    showCancelButton: false,
                    confirmButtonText: "OK",
                    closeOnConfirm: true,
                    confirmButtonColor: "#e6592e"

                }, function () {
                    window.onkeydown = null;
                    window.onfocus = null;
                });
            }
            
            $(".table100").hide()
            "use strict";
            $('.column100').on('mouseover', function () {
                var table1 = $(this).parent().parent().parent();
                var table2 = $(this).parent().parent();
                var verTable = $(table1).data('vertable') + "";
                var column = $(this).data('column') + "";

                $(table2).find("." + column).addClass('hov-column-' + verTable);
                $(table1).find(".row100.head ." + column).addClass('hov-column-head-' + verTable);
            });
            debugger
            $('.column100').on('mouseout', function () {
                var table1 = $(this).parent().parent().parent();
                var table2 = $(this).parent().parent();
                var verTable = $(table1).data('vertable') + "";
                var column = $(this).data('column') + "";

                $(table2).find("." + column).removeClass('hov-column-' + verTable);
                $(table1).find(".row100.head ." + column).removeClass('hov-column-head-' + verTable);
            });

            if ('@Session["Designation"]'.toString() != "SICRIE") {
                $("#Iddivfilter").show()
                $("#IdRadioFilter").hide()
                $("#IDBtnReport").show()
                $("#IDBtnReportInside").hide()
            }
            else {
                $("#Iddivfilter").hide()
                $("#IdRadioFilter").show()
                $("#IDBtnReport").hide()
                $("#IDBtnReportInside").show()
            }




            var palttypearr = []
            var ProjectCurrentStatusarr=[]
            $.ajax({
                url: AbsolutePath('/Reports/GetPlanttype'),
                type: 'GET',
                cache: false,
                async:false,
                success: function (resp) {
                   
                    palttypearr=resp
                }
            })

            $.ajax({
                url: AbsolutePath('/Reports/GetProjectStatus'),
                type: 'GET',
                cache: false,
                async: false,
                success: function (resp) {

                    ProjectCurrentStatusarr = resp
                }
            })
            console.log(palttypearr)
            $.ajax({
                url: AbsolutePath('/Home/StateDetails'),
                type: 'GET',
                cache: false,
                async: false,
                success: function (resp) {
                    console.log(resp)
                    $("#IdDivStateForReports").empty();
                    $("#IdDivStateForReports").append(resp);
                }
            })
            
            $(document).on('click', '.ClsZone', function () {
               
               
                if ($(this).val() == "SelectAll") {
                    if ($(this).prop('checked') == true) {
                        for (var i = 0; i < $(".ClsZone").length; i++) {
                            $($(".ClsZone")[i]).prop('checked', true)
                        }
                    }
                }
                if ($(this).val() == "SelectAll") {
                    if ($(this).prop('checked') != true) {
                        for (var i = 0; i < $(".ClsZone").length; i++) {
                            $($(".ClsZone")[i]).prop('checked', false)
                        }
                    }
                }
                if ($(this).val() != "SelectAll") {

                    $($(".ClsZone")[0]).prop('checked', false)
                }
                var checkboxlength = $(".ClsZone").length;
                var noofchekboxchecked = 0
                for (var i = 1; i < $(".ClsZone").length; i++) {

                    if ($($(".ClsZone")[i]).prop('checked') == true) {
                        noofchekboxchecked++
                    }
                }
                if (noofchekboxchecked == (checkboxlength - 1)) {
                    $($(".ClsZone")[0]).prop('checked', true)
                }
            })
          
            var selecteddasboard = 0
            $(".dashboardradio").click(function () {
                                       
                selecteddasboard = $(this).val()

                    @*  var id = '@Session["EmployeeID"]'*@
                if (selecteddasboard == 1) {

                    $.ajax({
                        url: AbsolutePath('/Reports/GetProjectStatusForRIE?Rieid=' + '@Session["EmployeeID"]'),
                        type: 'GET',
                        cache: false,
                        async: false,
                        success: function (resp) {
                               
                            console.log(resp)
                            var plattype = ""
                            var currentstatus = ""
                            var statename = ""
                            var count = ""


                            var duplicatearr = []
                            var duplicatearr1 = []
                            duplicatearr = resp
                            var isexiste = 0

                            $(".table101").html("")
                            $(".table102").html("")
                            var counter = 0
                            var testarr = []
                            if (resp.length > 0) {
                            for (var dupindex = 0; dupindex < resp.length; dupindex++) {
                                for (var i = 0; i < duplicatearr.length; i++) {
                                    if (resp[dupindex].ContractorIC == duplicatearr[i].ContractorIC || resp.length == 1) {

                                        if (counter == 0) {

                                            plattype = resp[i].Plant_typeName

                                            currentstatus = resp[i].Plant_typeName

                                            statename = resp[i].State_Name
                                            //  traversing

                                            //$("#table0 tbody").children().eq(0).css("border", "solid green 2px")
                                            $(".table101").show()
                                            $(".table101").append("<table data-vertable='ver3' id='tableContractor" + i + "'>")
                                            $("#tableContractor" + i).append("<caption>Contractors Allocation</caption>")
                                            $("#tableContractor" + i).append("<thead> <tr class='row100 head'><th class='column100 column1' data-column='column1' style='border-bottom:solid #97d4a9 2px;border-right: solid #97d4a9 2px;color:#ff914d'>Name</th>")
                                            for (var index = 0; index < palttypearr.length; index++) {
                                                var j = index + 2

                                                $("#tableContractor" + i + " tr").append("<th class='column100 column" + j + "' data-column='column" + j + "' style=' border-right: solid #97d4a9 2px;'>" + palttypearr[index].Plant_typeName + "</th>")
                                            }
                                            $("#tableContractor" + i).append("</tr></thead>")
                                            //debugger
                                            $("#tableContractor" + i).append("<tbody>")

                                            for (var k = 0; k <= resp.length; k++) {
                                                if (k != resp.length) {
                                                    var datacolm = k + 1
                                                    // testing


                                                    //

                                                    $("#tableContractor" + i + " tbody").append("<tr class='row100' id='Row" + k + 1 + "'> <th class='column100 column1' data-column='column1'>" + resp[k].ContractorName + "</th>")
                                                    testarr.push(resp[k].ContractorIC)
                                                    for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                        var seconddatacolum = noofcol + 2
                                                        $("#Row" + k + 1).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'></td>")
                                                    }


                                                    $("#tableContractor" + i + " tbody").append("</tr>")


                                                }
                                                else {

                                                    $("#tableContractor" + i + " tbody").append("<tr class='row100' id='Row" + k + 1 + "' style='background-color:#419898;'> <th class='column100 column1' data-column='column1'style='background-color:#419898;color:black'>Total</th>")
                                                    for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                        var seconddatacolum = noofcol + 2
                                                        $("#Row" + k + 1).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'style='background-color:#419898;border-right:solid #97d4a9 1px;border-left:solid #97d4a9 1px;'></td>")
                                                    }
                                                    $("#tableContractor" + i + " tbody").append("</tr>")
                                                }

                                            }

                                            var isexist = false
                                            var arr = []
                                               
                                            var testcic=0
                                            // apping values to all the columns
                                            $("#tableContractor" + i).append("</tbody></table>")
                                            for (var c = 0; c < duplicatearr.length; c++) {
                                                var ContractorIC = duplicatearr[c].ContractorIC
                                                var pdid = duplicatearr[c].ProjectDetailsId
                                                   
                                                    
                                                var ContractorName = duplicatearr[c].ContractorName
                                                var plantype = duplicatearr[c].Plant_typeName

                                                for (var a = 0; a < resp.length; a++) {
                                                    var ps = $("#tableContractor" + i + " tbody").children().eq(a).children().eq(0).text()

                                                    if (ContractorName == ps && pdid == resp[a].ProjectDetailsId) {
                                                        for (var b = 0; b < palttypearr.length; b++) {

                                                            var pt = $("#tableContractor" + i + " thead").children().eq(0).children().eq(b + 1).text()
                                                            // pd = duplicatearr[c].ProjectDetailsId
                                                            if (plantype == pt) {
                                                                $("#tableContractor" + i + " tbody").children().eq(a).children().eq(b + 1).text(duplicatearr[c].Number_of_Projects)

                                                                break;
                                                            }

                                                        }

                                                    }
                                                }
                                                //testcic = ContractorIC
                                            }

                                              

                                            //totaling  all the values

                                            var totalcount = 0
                                            for (var d = 0; d < palttypearr.length; d++) {
                                                var ps = $("#tableContractor" + i + " tbody").children().eq(d).children().eq(0).text()
                                                for (var e = 0; e < resp.length ; e++) {
                                                    var countofcell = $("#tableContractor" + i + " tbody").children().eq(e).children().eq(d + 1).text()
                                                    totalcount = (countofcell * 1) + (totalcount * 1)
                                                }
                                                $("#tableContractor" + i + " tbody").children().eq(resp.length).children().eq(d + 1).text(totalcount)
                                                totalcount = 0

                                            }

                                            $(".table101").append("<br/>")

                                               

                                            //Removing Duplicate rows

                                            $.AddAllSame = function (FromId, ToId) {
                                                debugger;
                                                for (var b1 = 0; b1 < palttypearr.length; b1++) {
                                                    var ToText = $("#tableContractor" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text() == "" ? 0 : $("#tableContractor" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text();
                                                    var FromText = $("#tableContractor" + 0 + " tr").eq(FromId).children().eq(b1 + 1).text() == "" ? 0 : $("#tableContractor" + 0 + " tr").eq(FromId).children().eq(b1 + 1).text();
                                                    $("#tableContractor" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text((parseInt(FromText) + parseInt(ToText)) == 0 ? "" : (parseInt(FromText) + parseInt(ToText)));
                                                }
                                            }
                                            var ArrayC = [];
                                            var count = $("#tableContractor" + 0 + " tr").length - 1;
                                            for (var l = 1; l < count; l++) {
                                                var IndexArray=ArrayC.indexOf($("#tableContractor" + 0 + " tr").eq(l).children().eq(0).text());
                                                if (IndexArray >= 0) {
                                                    $.AddAllSame(l, parseInt(IndexArray) + 1);
                                                    $("#tableContractor" + 0 + " tr").eq(l).remove();
                                                    count--;
                                                    l--;
                                                }
                                                else {
                                                    ArrayC.push($("#tableContractor" + 0 + " tr").eq(l).children().eq(0).text());
                                                }
                                                  

                                            }
                                            //-----------------------------------------
 
                                            counter++
                                        }
                                        else {
                                            //alert(" table allready Created")

                                        }

                                    }
                                }
                            }

                        }
                    else
                            {
                                AlertMessage1("No Records Exists", "warning")
                    }

                            }
                        })
                }

                else if (selecteddasboard == 2) {
                    $.ajax({
                        url: AbsolutePath('/Reports/GetProjectStatusForRIEforEngineer?Rieid=' + '@Session["EmployeeID"]'),
                        type: 'GET',
                        cache: false,
                        async: false,
                        success: function (resp) {
                            // debugger
                            console.log(resp)
                            var plattype = ""
                            var currentstatus = ""
                            var statename = ""
                            var count = ""


                            var duplicatearr = []
                            duplicatearr = resp
                            $(".table101").html("")
                            $(".table102").html("")

                            var counter = 0
                            var testarr = []
                            if (resp.length > 0) {
                            for (var dupindex = 0; dupindex < resp.length; dupindex++) {
                                for (var i = 0; i < duplicatearr.length; i++) {
                                    if (resp[dupindex].Assign_To_IE == duplicatearr[i].Assign_To_IE || resp.length == 1) {

                                        if (counter == 0) {

                                            plattype = resp[i].Plant_typeName

                                            currentstatus = resp[i].Plant_typeName

                                            statename = resp[i].State_Name
                                            //  traversing

                                            //$("#table0 tbody").children().eq(0).css("border", "solid green 2px")
                                            $(".table102").show()
                                            $(".table102").append("<table data-vertable='ver3' id='table" + 0 + "'>")
                                            $("#table" + 0).append("<caption>Engineer Allocation</caption>")
                                            $("#table" + 0).append("<thead> <tr class='row100 head'><th class='column100 column1' data-column='column1' style='border-bottom:solid #97d4a9 2px;border-right: solid #97d4a9 2px;color:#ff914d'>" + "Name" + " </th>")
                                            for (var index = 0; index < palttypearr.length; index++) {
                                                var j = index + 2

                                                $("#table" + 0 + " tr").append("<th class='column100 column" + j + "' data-column='column" + j + "' style=' border-right: solid #97d4a9 2px;'>" + palttypearr[index].Plant_typeName + "</th>")
                                            }
                                            $("#table" + 0).append("</tr></thead>")
                                            //debugger
                                            $("#table" + 0).append("<tbody>")

                                            for (var k = 0; k <= resp.length; k++) {
                                                if (k != resp.length) {
                                                    var datacolm = k + 1

                                                    $("#table" + 0 + " tbody").append("<tr class='row100' id='Row" + k + 0 + "'> <th class='column100 column1' data-column='column1'>" + resp[k].Company_Employee_Name + "</th>")
                                                    testarr.push(resp[k].Company_Employee_Name)
                                                    for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                        var seconddatacolum = noofcol + 2
                                                        $("#Row" + k + 0).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'></td>")
                                                    }
                                                    $("#table" + 0 + " tbody").append("</tr>")
                                                }
                                                else {

                                                    $("#table" + 0 + " tbody").append("<tr class='row100' id='Row" + k + 0 + "' style='background-color:#419898;'> <th class='column100 column1' data-column='column1'style='background-color:#419898;color:black'>Total</th>")
                                                    for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                        var seconddatacolum = noofcol + 2
                                                        $("#Row" + k + 0).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'style='background-color:#419898;border-right:solid #97d4a9 1px;border-left:solid #97d4a9 1px;'></td>")
                                                    }
                                                    $("#table" + 0 + " tbody").append("</tr>")
                                                }

                                            }
                                            debugger
                                            // applying values to all the columns
                                            $("#table" + 0).append("</tbody></table>")
                                            for (var c = 0; c < duplicatearr.length; c++) {
                                                var ContractorName = duplicatearr[c].Company_Employee_Name
                                                var plantype = duplicatearr[c].Plant_typeName
                                                var pdid = duplicatearr[c].ProjectDetailsId
                                                for (var a = 0; a < resp.length; a++) {
                                                    var ps = $("#table" + 0 + " tbody").children().eq(a).children().eq(0).text()
                                                    if (ContractorName == ps && pdid == resp[a].ProjectDetailsId) {
                                                        for (var b = 0; b < palttypearr.length; b++) {

                                                            var pt = $("#table" + 0 + " thead").children().eq(0).children().eq(b + 1).text()

                                                            if (plantype == pt) {
                                                                $("#table" + 0 + " tbody").children().eq(a).children().eq(b + 1).text(duplicatearr[c].Number_of_Projects)

                                                            }

                                                        }

                                                    }
                                                }
                                            }
                                            debugger
                                            //totaling  all the values
                                            var totalcount = 0
                                            for (var d = 0; d < palttypearr.length; d++) {
                                                var ps = $("#table" + 0 + " tbody").children().eq(d).children().eq(0).text()
                                                for (var e = 0; e < resp.length ; e++) {
                                                    var countofcell = $("#table" + 0 + " tbody").children().eq(e).children().eq(d + 1).text()
                                                    totalcount = (countofcell * 1) + (totalcount * 1)
                                                }
                                                $("#table" + 0 + " tbody").children().eq(resp.length).children().eq(d + 1).text(totalcount)
                                                totalcount = 0

                                            }

                                            $(".table102").append("<br/>")

                                            counter++
                                               

                                            $.AddAllSame = function (FromId, ToId) {
                                                debugger;
                                                for (var b1 = 0; b1 < palttypearr.length; b1++) {
                                                    var ToText = $("#table" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text() == "" ? 0 : $("#table" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text();
                                                    var FromText = $("#table" + 0 + " tr").eq(FromId).children().eq(b1 + 1).text() == "" ? 0 : $("#table" + 0 + " tr").eq(FromId).children().eq(b1 + 1).text();
                                                    $("#table" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text((parseInt(FromText) + parseInt(ToText)) == 0 ? "" : (parseInt(FromText) + parseInt(ToText)));
                                                }
                                            }
                                            var ArrayC = [];
                                            var count = $("#table" + 0 + " tr").length - 1;
                                            for (var l = 1; l < count; l++) {
                                                var IndexArray = ArrayC.indexOf($("#table" + 0 + " tr").eq(l).children().eq(0).text());
                                                if (IndexArray >= 0) {
                                                    $.AddAllSame(l, parseInt(IndexArray) + 1);
                                                    $("#table" + 0 + " tr").eq(l).remove();
                                                    count--;
                                                    l--;
                                                }
                                                else {
                                                    ArrayC.push($("#table" + 0 + " tr").eq(l).children().eq(0).text());
                                                }
                                                    

                                            }
                                            ////------





                                        }
                                        else {
                                            //alert(" table allready Created")

                                        }

                                    }
                                }
                            }

                        }
                    else
                            {
                                AlertMessage1("No Records Exists", "warning")
                    }

                            }
                        })
                    }
                    else {
                        $.ajax({
                            url: AbsolutePath('/Reports/GetProjectStatusForRIEforstate?Rieid=' + '@Session["EmployeeID"]'),
                             type: 'GET',
                             cache: false,
                             async: false,
                             success: function (resp) {
                                 debugger
                                 console.log(resp)
                                 var plattype = ""
                                 var currentstatus = ""
                                 var statename = ""
                                 var count = ""

                                 var testarr=[]
                                 var duplicatearr = []
                                 duplicatearr = resp
                                 $(".table101").html("")
                                 $(".table102").html("")
                                 if (resp.length > 0) {
                                     var counter = 0
                                     for (var dupindex = 0; dupindex < resp.length; dupindex++) {
                                         for (var i = 0; i < duplicatearr.length; i++) {
                                             if (resp[dupindex].StatenameIC == duplicatearr[i].StatenameIC || resp.length == 1) {

                                                 if (counter == 0) {

                                                     plattype = resp[i].Plant_typeName

                                                     currentstatus = resp[i].Plant_typeName

                                                     statename = resp[i].State_Name
                                                     //  traversing

                                                     //$("#table0 tbody").children().eq(0).css("border", "solid green 2px")
                                                     $(".table102").show()
                                                     $(".table102").append("<table data-vertable='ver3' id='tableState" + 0 + "'>")
                                                     $("#tableState" + 0).append("<caption>State Wise Allocation</caption>")
                                                     $("#tableState" + 0).append("<thead> <tr class='row100 head'><th class='column100 column1' data-column='column1' style='border-bottom:solid #97d4a9 2px;border-right: solid #97d4a9 2px;color:#ff914d'>" + "Name" + " </th>")
                                                     for (var index = 0; index < palttypearr.length; index++) {
                                                         var j = index + 2

                                                         $("#tableState" + 0 + " tr").append("<th class='column100 column" + j + "' data-column='column" + j + "' style=' border-right: solid #97d4a9 2px;'>" + palttypearr[index].Plant_typeName + "</th>")
                                                     }
                                                     $("#tableState" + 0).append("</tr></thead>")
                                                     //debugger
                                                     $("#tableState" + 0).append("<tbody>")

                                                     for (var k = 0; k <= resp.length; k++) {
                                                         if (k != resp.length) {
                                                             var datacolm = k + 1

                                                             $("#tableState" + 0 + " tbody").append("<tr class='row100' id='Row" + k + 0 + "'> <th class='column100 column1' data-column='column1'>" + resp[k].State_Name + "</th>")
                                                             testarr.push(resp[k].StatenameIC)
                                                             for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                                 var seconddatacolum = noofcol + 2
                                                                 $("#Row" + k + 0).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'></td>")
                                                             }
                                                             $("#tableState" + 0 + " tbody").append("</tr>")
                                                         }
                                                         else {

                                                             $("#tableState" + 0 + " tbody").append("<tr class='row100' id='Row" + k + 0 + "' style='background-color:#419898;'> <th class='column100 column1' data-column='column1'style='background-color:#419898;color:black'>Total</th>")
                                                             for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                                 var seconddatacolum = noofcol + 2
                                                                 $("#Row" + k + 0).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'style='background-color:#419898;border-right:solid #97d4a9 1px;border-left:solid #97d4a9 1px;'></td>")
                                                             }
                                                             $("#tableState" + 0 + " tbody").append("</tr>")
                                                         }

                                                     }
                                                     debugger
                                                     // apping values to all the columns
                                                     $("#tableState" + 0).append("</tbody></table>")
                                                     for (var c = 0; c < duplicatearr.length; c++) {
                                                         var ContractorName = duplicatearr[c].Company_Employee_Name
                                                         var plantype = duplicatearr[c].Plant_typeName
                                                         var pdid = duplicatearr[c].ProjectDetailsId
                                                         for (var a = 0; a < resp.length; a++) {
                                                             var ps = $("#tableState" + 0 + " tbody").children().eq(a).children().eq(0).text()
                                                             if (pdid == resp[a].ProjectDetailsId) {
                                                                 for (var b = 0; b < palttypearr.length; b++) {

                                                                     var pt = $("#tableState" + 0 + " thead").children().eq(0).children().eq(b + 1).text()

                                                                     if (plantype == pt) {
                                                                         $("#tableState" + 0 + " tbody").children().eq(a).children().eq(b + 1).text(duplicatearr[c].Number_of_Projects)

                                                                     }

                                                                 }

                                                             }
                                                         }
                                                     }
                                                     debugger
                                                     //totaling  all the values
                                                     var totalcount = 0
                                                     for (var d = 0; d < palttypearr.length; d++) {
                                                         var ps = $("#tableState" + 0 + " tbody").children().eq(d).children().eq(0).text()
                                                         for (var e = 0; e < resp.length ; e++) {
                                                             var countofcell = $("#tableState" + 0 + " tbody").children().eq(e).children().eq(d + 1).text()
                                                             totalcount = (countofcell * 1) + (totalcount * 1)
                                                         }
                                                         $("#tableState" + 0 + " tbody").children().eq(resp.length).children().eq(d + 1).text(totalcount)
                                                         totalcount = 0

                                                     }

                                                     $(".table102").append("<br/>")

                                                     counter++

                                                     console.log("state")
                                                     console.log(resp)

                                                     $.AddAllSame = function (FromId, ToId) {
                                                         debugger;
                                                         for (var b1 = 0; b1 < palttypearr.length; b1++) {
                                                             var ToText = $("#tableState" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text() == "" ? 0 : $("#tableState" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text();
                                                             var FromText = $("#tableState" + 0 + " tr").eq(FromId).children().eq(b1 + 1).text() == "" ? 0 : $("#tableState" + 0 + " tr").eq(FromId).children().eq(b1 + 1).text();
                                                             $("#tableState" + 0 + " tr").eq(ToId).children().eq(b1 + 1).text((parseInt(FromText) + parseInt(ToText)) == 0 ? "" : (parseInt(FromText) + parseInt(ToText)));
                                                         }
                                                     }
                                                     var ArrayC = [];
                                                     var count = $("#tableState" + 0 + " tr").length - 1;
                                                     for (var l = 1; l < count; l++) {
                                                         var IndexArray = ArrayC.indexOf($("#tableState" + 0 + " tr").eq(l).children().eq(0).text());
                                                         if (IndexArray >= 0) {
                                                             $.AddAllSame(l, parseInt(IndexArray) + 1);
                                                             $("#tableState" + 0 + " tr").eq(l).remove();
                                                             count--;
                                                             l--;
                                                         }
                                                         else {
                                                             ArrayC.push($("#tableState" + 0 + " tr").eq(l).children().eq(0).text());
                                                         }


                                                     }
                                                     ////------



                                                 }
                                                 else {
                                                     //alert(" table allready Created")

                                                 }

                                             }
                                         }
                                     }
                                 }
                                 else {
                                     AlertMessage1("No Records Exists", "warning")
                                 }

                             }
                         })
                    }




                //alert("Regional Installation Engineer")
            })
            if ('@Session["Designation"]'.toString() == "SICRIE") {
                $($(".dashboardradio")[0]).click()
            }
          
         
            

            $(document).on('click', '.IDBtnReportCls', function ()
            {
                var Zonearr = [];
                var Allselectcounter = 0
                $(".table100").html("")
                for (var i = 0; i < $(".ClsZone").length; i++) {
                    if ($($(".ClsZone")[i]).prop('checked') == true) {
                        Allselectcounter++
                    }
                }
                for (var i = 0; i < $(".ClsZone").length; i++) {
                    if ($($(".ClsZone")[i]).prop('checked') == true) {

                        Zonearr.push($($(".ClsZone")[i]).val())

                    }

                }
                
                
                if ('@Session["Designation"]'.toString() != "SICRIE") {


                    //if (Zonearr.length > 0) {
                    if ('@Session["Designation"]'.toString() != "SICRIE") {

                        console.log("rp")
                        console.log(Zonearr)
                    $.ajax({
                        url: AbsolutePath("/Reports/ReportsList"),
                        type: 'Get',
                        datatype: "json",
                        async:false,
                        data:
                        {
                            State: JSON.stringify(Zonearr),
                        },
                        cache: false,
                        success: function (resp) {
                            debugger
                            console.log("resp")
                            console.log(resp)
                            var plattype = ""
                            var currentstatus = ""
                            var statename=""
                            var count=""
                       
                           
                            var duplicatearr=[]
                            duplicatearr = resp
                       
                            $(".table100").html("")
                            var Tablecounter = 0
                            if (resp.length > 0) {
                            for (var dupindex = 0; dupindex < resp.length; dupindex++) {
                                for (var i = 0; i < duplicatearr.length; i++) {
                                    if (resp[dupindex].StatenameIC == duplicatearr[i].StatenameIC || resp.length == 1) {
                                        if (Tablecounter == 0) {

                                            if(dupindex==i)
                                            {
                                                plattype = resp[i].Plant_typeName

                                                currentstatus = resp[i].ProjectCurrentStatusName

                                                statename = resp[i].State_Name                                     
                                                //  traversing

                                                //$("#table0 tbody").children().eq(0).css("border", "solid green 2px")
                                                $(".table100").show()
                                                $(".table100").append("<table data-vertable='ver3' id='table" + i + "'>")
                                                $("#table" + i).append("<caption>YTD Project Status</caption>")
                                                $("#table" + i).append("<thead> <tr class='row100 head'><th class='column100 column1' data-column='column1' style='border-bottom:solid #97d4a9 2px;border-right: solid #97d4a9 2px;color:#ff914d'>" + statename + " </th>")
                                                for (var index = 0; index < palttypearr.length; index++) {
                                                    var j = index + 2
                                            
                                                    $("#table" + i + " tr").append("<th class='column100 column" + j + "' data-column='column" + j + "' style=' border-right: solid #97d4a9 2px;'>" + palttypearr[index].Plant_typeName + "</th>")
                                           
                                                }
                                                $("#table" + i).append("</tr></thead>")
                                                $("#table" + i).append("<tbody>")

                                                for (var k = 0; k <= ProjectCurrentStatusarr.length; k++) {
                                                    if (k != ProjectCurrentStatusarr.length) {
                                                        var datacolm = k + 1

                                                        $("#table" + i + " tbody").append("<tr class='row100' id='Row" + k + i + "'> <th class='column100 column1' data-column='column1'>" + ProjectCurrentStatusarr[k].ProjectCurrentStatusName + "</th>")
                                                        for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                            var seconddatacolum = noofcol + 2
                                                            $("#Row" + k + i).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'></td>")
                                                        }
                                                        $("#table" + i + " tbody").append("</tr>")
                                                    }
                                                    else {

                                                        $("#table" + i + " tbody").append("<tr class='row100' id='Row" + k + i + "' style='background-color:#419898;'> <th class='column100 column1' data-column='column1'style='background-color:#419898;color:black'>Total</th>")
                                                        for (var noofcol = 0; noofcol < palttypearr.length; noofcol++) {
                                                            var seconddatacolum = noofcol + 2
                                                            $("#Row" + k + i).append("<td class='column100 column" + seconddatacolum + "' data-column='column" + seconddatacolum + "'style='background-color:#419898;border-right:solid #97d4a9 1px;border-left:solid #97d4a9 1px;color:black'></td>")
                                                        }
                                                        $("#table" + i + " tbody").append("</tr>")
                                                    }

                                                }
                                                //------ appending values to all the columns
                                                $("#table" + i).append("</tbody></table>")
                                                for (var c = 0; c < duplicatearr.length; c++) {
                                                    var projectstatus = duplicatearr[c].ProjectCurrentStatusName
                                                    var plantype = duplicatearr[c].Plant_typeName
                                                    var sic = duplicatearr[c].StatenameIC
                                                    for (var a = 0; a < ProjectCurrentStatusarr.length; a++) {
                                                        var ps = $("#table" + i + " tbody").children().eq(a).children().eq(0).text()
                                                        if (projectstatus == ps && sic==resp[dupindex].StatenameIC) {
                                                            for (var b = 0; b < palttypearr.length; b++) {

                                                                var pt = $("#table" + i + " thead").children().eq(0).children().eq(b + 1).text()

                                                                if (plantype == pt) {
                                                                    $("#table" + i + " tbody").children().eq(a).children().eq(b + 1).text(duplicatearr[c].Number_of_Projects)

                                                                }

                                                            }

                                                        }
                                                    }
                                                }
                                                //--------------------------------------------------
                                                //totaling  all the values
                                                var totalcount=0
                                                for (var d = 0; d < palttypearr.length; d++) {
                                                    var ps = $("#table" + i + " tbody").children().eq(d).children().eq(0).text()
                                                    for (var e = 0; e < ProjectCurrentStatusarr.length ;e++) {                         
                                                        var countofcell = $("#table" + i + " tbody").children().eq(e).children().eq(d + 1).text()
                                                        totalcount = (countofcell * 1) + (totalcount * 1)
                                                    }
                                                    $("#table" + i + " tbody").children().eq(ProjectCurrentStatusarr.length).children().eq(d + 1).text(totalcount)
                                                    totalcount = 0

                                                }
                                                //============================

                                                $(".table100").append("<br/>")



                                            }
                                            //debugger
                                            Tablecounter++
                                        }
                                        else {
                                        }
                                    }
                                    else{
                                    }
                                }
                                Tablecounter = 0
                            }
                        }
                            else {
                               AlertMessage1("No Records Exists", "warning")

                            }

                           
                            Zonearr = ""
                         
                        }
                    })
                }
                else{
                    alert("select State" )
                }


            }
                else {


        }

            })
            $(".IDBtnReportCls").click()
        })
    </script>
</body>
   
</html>