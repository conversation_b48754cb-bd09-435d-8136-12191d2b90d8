AmCharts.translations[ "export" ][ "en" ] = {
	"fallback.save.text": "CTRL + C to copy the data into the clipboard.",
	"fallback.save.image": "Rightclick -> Save picture as... to save the image.",

	"capturing.delayed.menu.label": "{{duration}}",
	"capturing.delayed.menu.title": "Click to cancel",

	"menu.label.print": "Print",
	"menu.label.undo": "Undo",
	"menu.label.redo": "Redo",
	"menu.label.cancel": "Cancel",

	"menu.label.save.image": "Download as ...",
	"menu.label.save.data": "Save as ...",

	"menu.label.draw": "Annotate ...",
	"menu.label.draw.change": "Change ...",
	"menu.label.draw.add": "Add ...",
	"menu.label.draw.shapes": "Shape ...",
	"menu.label.draw.colors": "Color ...",
	"menu.label.draw.widths": "Size ...",
	"menu.label.draw.opacities": "Opacity ...",
	"menu.label.draw.text": "Text",

	"menu.label.draw.modes": "Mode ...",
	"menu.label.draw.modes.pencil": "Pencil",
	"menu.label.draw.modes.line": "Line",
	"menu.label.draw.modes.arrow": "Arrow",

	"label.saved.from": "Saved from: "
}