﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
  </configSections>
  <appSettings>
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="PreserveLoginUrl" value="true"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="ServerName" value="QITBLRQIPL165\SQL2014"/>
    <add key="DatabaseName" value="MICP_QA"/>
    <add key="Userid" value="sa"/>
    <add key="Password" value="*********"/>
    <add key="DICounter" value="50"/>
    <!--<add key="ServerName" value=" QITBLRPRDSERVER\Testing_SQL2012" />
    <add key="DatabaseName" value="Helpdesk_MetsoSIC" />
    <add key="Userid" value="Hosting" />
    <add key="Password" value="Hosting$123" />-->
    <!--Azure Cloud Hosted APp-->
    <!--<add key="ClientId" value="cbe4b83e-e36e-40d1-8142-239ec4340918" />
    <add key="RedirectUrl" value="https://mics.azurewebsites.net" />
    <add key="Tenant" value="Questinformatics.com" />
    <add key="Authority" value="https://login.microsoftonline.com/{0}" />-->
    <add key="ClientId" value="3d5cf9dc-53b1-42de-9a30-7f559f7108f7"/>
    <add key="RedirectUrl" value="https://metsomics-questinformaticsind.msappproxy.net/MICS/"/>
    <add key="Tenant" value="Questinformatics.com"/>
    <add key="Authority" value="https://login.microsoftonline.com/{0}"/>
    <add key="HostedProjectName" value="Mics"/>
    
    
    <!--<add key="ServerName" value="QITBLRQIPL194\SQL2014" />
    <add key="DatabaseName" value="MICP_QA" />
    <add key="Userid" value="sa" />
    <add key="Password" value="*********" />-->
    <add key="DICounter" value="50"/>
    <add key="FrmAddress" value="<EMAIL>"/>
    <add key="FrmName" value="Metso India pvt ltd"/>
    <add key="MailServer" value="smtp.office365.com"/>
    <add key="MailTimeOut" value="900000"/>
    <add key="SMTP_USER_ID" value="<EMAIL>"/>
    <add key="SMTP_PASSWORD" value="Quest$Atarw@2019"/>
    <add key="ProjectFolderName" value="https://metsomics-questinformaticsind.msappproxy.net/MICS"/>
    <add key="FincialStartDate" value="01-01-2019"/>
    <add key="FincialEndDate" value="01-01-2020"/>
  <add key="FactoryMailId" value="<EMAIL>,<EMAIL>,<EMAIL>" />

    <add key="FactoryCCMailId" value="<EMAIL>,<EMAIL>,<EMAIL>" />

  </appSettings>
 
  <system.web>
    <!--Session-->
    <!--<authentication mode="Forms">
      <forms name="MyLoginProfile" loginUrl="~/Home/Index" timeout="60" slidingExpiration="true" />
    </authentication>
    <authorization>
      <allow users="*" />
    </authorization>

    <sessionState mode="InProc" timeout="60" />-->
    <!--Session End-->
    <authentication mode="None"/>
    <httpRuntime targetFramework="4.5" maxRequestLength="20000"/>
    <compilation debug="true" targetFramework="4.5"/>
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers"/>
        <add namespace="System.Web.Mvc"/>
        <add namespace="System.Web.Mvc.Ajax"/>
        <add namespace="System.Web.Mvc.Html"/>
        <add namespace="System.Web.Routing"/>
        <add namespace="System.Web.WebPages"/>
      </namespaces>
    </pages>
    <customErrors mode="Off"/>
  <sessionState timeout="120"></sessionState>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="********"/>
      </requestFiltering>
    </security>
    <httpProtocol>
      <customHeaders>
        <add name="Access-Control-Allow-Origin" value="*"/>
        <add name="Access-Control-Allow-Headers" value="Origin, X-Requested-With, Content-Type, Accept,Authorization"/>
        <add name="Access-Control-Allow-Methods" value="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS"/>
      </customHeaders>
    </httpProtocol>
    <validation validateIntegratedModeConfiguration="false"/>
    <modules runAllManagedModulesForAllRequests="true"/>
    <handlers>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit"/>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit"/>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0"/>
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0"/>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0"/>
    </handlers>
  </system.webServer>
  <connectionStrings>
    <!--<add name="MetsoSICEntities" connectionString="metadata=res://*/Models.Metso_MICP.csdl|res://*/Models.Metso_MICP.ssdl|res://*/Models.Metso_MICP.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=QITBLRQIPL165\SQL2014;initial catalog=MICP_QA;user id=sa;password=*********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="MetsoSICEntities" connectionString="metadata=res://*/Models.Metso_MICP.csdl|res://*/Models.Metso_MICP.ssdl|res://*/Models.Metso_MICP.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=QITBLRCSSERVERT\SQL2012;initial catalog= MICP_QA;user id=sa;password=*****;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="MetsoSICEntities" connectionString="metadata=res://*/Models.Metso_MICP.csdl|res://*/Models.Metso_MICP.ssdl|res://*/Models.Metso_MICP.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=QITBLRQIPL194\SQL2014;initial catalog = MICP_QA;user id=sa;password=*********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <add name="MetsoSICEntities" connectionString="metadata=res://*/Models.Metso_MICP.csdl|res://*/Models.Metso_MICP.ssdl|res://*/Models.Metso_MICP.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=QITBLRQIPL165\SQL2014;initial catalog=MICP_QA;user id=sa;password=*********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient"/>
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb"/>
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.4.0.0" newVersion="5.4.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.4.0.0" newVersion="5.4.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.4.0.0" newVersion="5.4.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System" publicKeyToken="b77a5c561934e089" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Drawing" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Xml" publicKeyToken="b77a5c561934e089" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>