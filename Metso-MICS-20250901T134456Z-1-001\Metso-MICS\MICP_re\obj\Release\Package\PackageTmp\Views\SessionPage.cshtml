﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
  <head>
    <meta name="viewport" content="width=device-width" />
    <title>@ViewBag.Title</title>

    <link rel="icon" href="~/Assets/Images/Logo/favicon.ico" />
    <link href="~/Assets/Plugins/JQueryUI/JqueryUI-1.12.1/jquery-ui-1.12.1.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/Bootstrap/bootstrap-3.3.6-dist/css/bootstrap3.6.css" rel="stylesheet" />
    <link href="~/Assets/Themes/hot-sneaks/jquery-ui.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/ui.jqgrid.css" rel="stylesheet" />
    <link href="~/Assets/Fonts/font-awesome-4.7.0/css/font-awesome.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/FullCalender/fullcalendar.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/SweetAlerts/sweetalert.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/TimePicker/jquery.timepicker.css" rel="stylesheet" />
    <link href="~/Assets/CSS/_Layout.css" rel="stylesheet" />


    <script src="~/Assets/Plugins/JQuery/jquery-2.1.3.js"></script>
    <script src="~/Assets/Plugins/JQueryUI/jquery-ui-1.11.3.js"></script>
    <script src="~/Assets/Plugins/Bootstrap/bootstrap-3.3.6-dist/js/bootstrap.js"></script>
    <script src="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/grid.locale-en.js"></script>
    <script src="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/jquery.jqGrid.js"></script>
    <script src="~/Assets/Plugins/FullCalender/moment.min.js"></script>
    <script src="~/Assets/Plugins/FullCalender/fullcalendar.js"></script>
    <script src="~/Assets/Plugins/TimePicker/jquery.timepicker.js"></script>
    <script src="~/Assets/Plugins/SweetAlerts/sweetalert.js"></script>
    
    <style>
        body {
            @*color: #fff;*@
            font-family: 'Scania Sans';
            background-image: url('@Url.Content("~/Content/Image/MetsoBG.jpg")' );
            background-repeat: no-repeat;
        height: 100vh;
        width: 100%;
            -webkit-background-size: 100% 100%;
            -moz-background-size: 100% 100%;
            -o-background-size: 100% 100%;
            background-size: 100% 100%;
            /*width: 100%;
            height: 100%;*/
            margin: 0;
        }

        #loggit {
            @*width: 460px;*@
            @*margin: 12% 25%;*@
            padding: 10px 40px;
            background: #041e4278;
            -webkit-box-shadow: -2px -6px 1px -2px rgba(115,115,115,1);
            -moz-box-shadow: -2px -6px 1px -2px rgba(115,115,115,1);
            box-shadow: -2px -6px 1px -2px rgba(115,115,115,0);
            -moz-border-radius: 10px;
            -webkit-border-radius: 10px;
            -khtml-border-radius: 10px;
            border-radius: 10px;
            position: relative;
            z-index: 9999;
            box-shadow: 2px 2px 2px rgba(255,255,255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4);
        }

            #loggit h1 {
                text-align: center;
                font-weight: 700;
                margin: 5px 0 15px;
            }

            #loggit h3 {
                text-align: center;
                font-size: 18px;
                color: #bbb;
                margin: 0 0 20px;
            }

            #loggit .input-group-addon {
                border: 0 none;
            }

            #loggit .form-control {
                border: 0 none;
            }

                #loggit .form-control:focus {
                    box-shadow: none;
                }

@*            #loggit .formSubmit {
                margin-bottom: 25px;
            }*@

            #loggit .submitWrap {
                text-align: right;
            }

            #loggit .formNotice {
                margin: 0;
                font-size: 13px;
            }

                #loggit .formNotice span {
                    cursor: pointer;
                    color: #428BCA;
                }

                    #loggit .formNotice span:hover,
                    #loggit .formNotice span:focus {
                        color: #2A6496;
                        text-decoration: underline;
                    }

            #loggit #regForm {
                display: none;
            }


            @*#loggit .formSubmit {
                margin-bottom: 25px;
            }*@

            #loggit .submitWrap {
                text-align: right;
            }

        .btnlogin {
            background: #c8c9c7;
            background-image: -webkit-linear-gradient(top, #c8c9c7, #c8c9c7);
            background-image: -moz-linear-gradient(top, #c8c9c7, #c8c9c7);
            background-image: -ms-linear-gradient(top, #c8c9c7, #c8c9c7);
            background-image: -o-linear-gradient(top, #c8c9c7, #c8c9c7);
            background-image: linear-gradient(to bottom, #c8c9c7, #c8c9c7);
            -webkit-border-radius: 34px;
            -moz-border-radius: 34px;
            border-radius: 34px;
            font-family: 'Scania Sans';
            color: #041e42;
            font-size: 15px;
            padding: 7px 16px 7px 20px;
            text-decoration: none;
            box-shadow: 2px 2px 2px rgba(255,255,255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4);
        }

            .btnlogin:hover {
                background: #041e42;
                background-image: -webkit-linear-gradient(top, #041e42, #041e42);
                background-image: -moz-linear-gradient(top, #041e42, #041e42);
                background-image: -ms-linear-gradient(top, #041e42, #041e42);
                background-image: -o-linear-gradient(top, #041e42, #041e42);
                background-image: linear-gradient(to bottom, #041e42, #041e42);
                text-decoration: none;
                color: white;
                box-shadow: 2px 2px 2px rgba(255,255,255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4);
            }

        /* sweet alert */
        .sweet-alert {
            background-color: white;
            width: 350px;
            left: 60%;
            top: 50%;
            height: auto;
        }

            .sweet-alert p {
                color: #575757;
                font-size: 15px;
                text-align: center;
                font-weight: bold;
                position: relative;
                text-align: inherit;
                float: none;
                margin: 0;
                padding: 0;
                line-height: normal;
            }

            .sweet-alert h2 {
                color: #575757;
                font-size: 15px;
                text-align: center;
                font-weight: bold;
                text-transform: none;
                position: relative;
                margin: -15px 0;
                padding: 0;
                line-height: 30px;
                display: block;
            }
        .glyphicon-user, .glyphicon-lock{
            color:#041e42
        }
        #ForgotPassword{
        cursor:pointer;
        color:#fff
        }
       #ForgotPassword:hover {
            color: #FFC107;
            text-shadow: 0px 1px 2px white;
        }

        .sweet-alert input{
            font-size:13px;

            width: 100%;
    box-sizing: border-box;
    border-radius: 3px;
    border: 1px solid #d7d7d7;
    height: 30px;
    margin-top: 30px;
    margin-bottom: -30px;
    box-shadow: inset 0px 1px 1px rgba(0, 0, 0, 0.06);
    padding: 0 12px;
    transition: all 0.3s;


        }

        .sweet-alert button{
                @*background-color: #021228;*@
        font-size: 14px;
            padding: 8px 15px;
    margin: 20px 5px 0px 5px;
        width:70px
        }

        .sweet-alert .sa-input-error{
            top:37px;
        }

        .modal-header {
    font-size: 13px;
    background: #020f21;
    padding: 0.0em 1em;
    position: relative;
    box-shadow: 2px 2px 2px rgba(255, 255, 255,.4) inset, inset 0px 0px 6px 5px #041e42;
    border-radius: 9px;
    margin-left: 2px;
    margin-right: 2px;
    margin-top: 2px;
}

        .modal-title {
    color: #ffffff;
    font-size: 15px;
    padding: 7px;
}

        .close {
    float: right;
    font-size: 21px;
    font-weight: bold;
    line-height: 1;
    color: white;
    text-shadow: 0 1px 0 #fff;
    opacity: 1;
}

        #IdBtnResetPasswordCancel {
        background: #c00317;
        color: white;
    }

        #IdBtnResetPasswordOK {
            background: #020f21;
    color: white;
    }


    </style>
</head>
<body class="clsbody">
    <div>
        <div class="container-fluid">
            <div class="row" style="margin-top: 10px;">
                <div class="col-md-5">
                  @*  <img src="~/Assets/Images/Logo/Scania.png" style="width: 120px; margin-left: 300px" />*@
                </div>
                <div class="col-md-7">
                    <span style="font-size: 46px;
    font-family: 'Scania Sans';
    position: absolute;
    margin-left: -295px;
    color: white;
    font-weight: bold;
    top: 49px;">Installation & Commissioning System</span>
                </div>
            </div>
            <div class="row " style="margin-top:150px">
                <div class="text-center col-md-12"><span style="color: #f5f5f5;font-size: 38px;background: #132b4c;">Your Session Has Been Expired<br />Please Login Again <a href="@Url.Action("AppStartPage", "Home")" style="color:#e26b25">Click Here</a></span></div>
            </div>
        </div>
    </div>
</body>
</html>