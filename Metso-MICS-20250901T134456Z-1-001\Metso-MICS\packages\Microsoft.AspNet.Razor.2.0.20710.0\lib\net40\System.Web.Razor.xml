﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Razor</name>
  </assembly>
  <members>
    <member name="T:System.Web.Razor.CSharpRazorCodeLanguage">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.CSharpRazorCodeLanguage.CodeDomProviderType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.CSharpRazorCodeLanguage.CreateCodeParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.CSharpRazorCodeLanguage.LanguageName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.DocumentParseCompleteEventArgs">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.DocumentParseCompleteEventArgs.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.GeneratorResults">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.SourceChange">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.DocumentParseCompleteEventArgs.TreeStructureChanged">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.GeneratorResults">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Boolean,System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError},System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError},System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.GeneratorResults.#ctor(System.Web.Razor.ParserResults,System.CodeDom.CodeCompileUnit,System.Collections.Generic.IDictionary{System.Int32,System.Web.Razor.Generator.GeneratedCodeMapping})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.GeneratorResults.DesignTimeLineMappings">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.GeneratorResults.GeneratedCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.ParserResults">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.ParserResults.#ctor(System.Boolean,System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.ParserResults.#ctor(System.Web.Razor.Parser.SyntaxTree.Block,System.Collections.Generic.IList{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.ParserResults.Document">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.ParserResults.ParserErrors">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.ParserResults.Success">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.PartialParseResult">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Rejected">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Accepted">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.Provisional">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.SpanContextChanged">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.PartialParseResult.AutoCompleteBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.RazorCodeLanguage">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.CodeDomProviderType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.CreateCodeParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorCodeLanguage.GetLanguageByExtension(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.LanguageName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorCodeLanguage.Languages">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.RazorDirectiveAttribute">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.#ctor(System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorDirectiveAttribute.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.Name">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.TypeId">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorDirectiveAttribute.Value">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.RazorEditorParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.#ctor(System.Web.Razor.RazorEngineHost,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.CheckForStructureChanges(System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.CurrentParseTree">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.Dispose">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.Dispose(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="E:System.Web.Razor.RazorEditorParser.DocumentParseComplete">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.FileName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEditorParser.GetAutoCompleteString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.Host">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEditorParser.LastResultProvisional">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.RazorEngineHost">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor(System.Web.Razor.RazorCodeLanguage)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.#ctor(System.Web.Razor.RazorCodeLanguage,System.Func`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.CodeLanguage">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.CreateMarkupParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateCodeGenerator(System.Web.Razor.Generator.RazorCodeGenerator)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateCodeParser(System.Web.Razor.Parser.ParserBase)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.DecorateMarkupParser(System.Web.Razor.Parser.ParserBase)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultBaseClass">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultClassName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DefaultNamespace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.DesignTimeMode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.EnableInstrumentation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.GeneratedClassContext">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.InstrumentedSourceFilePath">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.NamespaceImports">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.PostProcessGeneratedCode(System.CodeDom.CodeCompileUnit,System.CodeDom.CodeNamespace,System.CodeDom.CodeTypeDeclaration,System.CodeDom.CodeMemberMethod)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorEngineHost.PostProcessGeneratedCode(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorEngineHost.StaticHelpers">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.RazorTemplateEngine">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.#ctor(System.Web.Razor.RazorEngineHost)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.CreateCodeGenerator(System.String,System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.CreateParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.RazorTemplateEngine.DefaultClassName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.RazorTemplateEngine.DefaultNamespace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.String,System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.IO.TextReader,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.String,System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCode(System.Web.Razor.Text.ITextBuffer,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.GenerateCodeCore(System.Web.Razor.Text.ITextDocument,System.String,System.String,System.String,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.RazorTemplateEngine.Host">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.IO.TextReader)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.IO.TextReader,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.Web.Razor.Text.ITextBuffer)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplate(System.Web.Razor.Text.ITextBuffer,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.RazorTemplateEngine.ParseTemplateCore(System.Web.Razor.Text.ITextDocument,System.Nullable{System.Threading.CancellationToken})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <typeparam name="TReturn"></typeparam>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.CurrentState">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StartState">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stay">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stay(`0)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Stop">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Transition(System.Web.Razor.StateMachine{`0}.State)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Transition(`0,System.Web.Razor.StateMachine{`0}.State)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.Turn">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1.State">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.StateMachine`1.StateResult">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.StateResult.#ctor(System.Web.Razor.StateMachine{`0}.State)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.StateMachine`1.StateResult.#ctor(`0,System.Web.Razor.StateMachine{`0}.State)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.HasOutput">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.Next">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.StateMachine`1.StateResult.Output">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.VBRazorCodeLanguage">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.VBRazorCodeLanguage.CodeDomProviderType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.CreateCodeGenerator(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.VBRazorCodeLanguage.CreateCodeParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.VBRazorCodeLanguage.LanguageName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Editor.EditorHints">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.None">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.VirtualPath">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Editor.EditorHints.LayoutPage">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Editor.EditResult">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.EditResult.#ctor(System.Web.Razor.PartialParseResult,System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Editor.EditResult.EditedSpan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Editor.EditResult.Result">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Editor.ImplicitExpressionEditHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.#ctor(System.Func`2,System.Boolean,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Editor.ImplicitExpressionEditHandler.AcceptTrailingDot">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Editor.ImplicitExpressionEditHandler.Keywords">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.ImplicitExpressionEditHandler.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Editor.SingleLineMarkupEditHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SingleLineMarkupEditHandler.#ctor(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SingleLineMarkupEditHandler.#ctor(System.Func`2,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Editor.SpanEditHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.#ctor(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.#ctor(System.Func`2,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.AcceptedCharacters">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CreateDefault">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.CreateDefault(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.EditorHints">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.GetOldText(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsAtEndOfFirstLine(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsAtEndOfSpan(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndDeletion(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndInsertion(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.IsEndReplace(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.OwnsChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Editor.SpanEditHandler.Tokenizer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Editor.SpanEditHandler.UpdateSpan(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.AddImportCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.#ctor(System.String,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.AddImportCodeGenerator.Namespace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.AddImportCodeGenerator.NamespaceKeywordLength">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AddImportCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.AttributeBlockCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.#ctor(System.String,System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.String})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Name">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Prefix">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.AttributeBlockCodeGenerator.Suffix">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.AttributeBlockCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.BlockCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.BlockCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.BlockCodeGenerator.Null">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.#ctor(System.String,System.String,System.CodeDom.CodeCompileUnit)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.GeneratedCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.PhysicalPath">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGenerationCompleteEventArgs.VirtualPath">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.CodeGeneratorBase">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorBase.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorBase.CalculatePadding(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorBase.CalculatePadding(System.Web.Razor.Parser.SyntaxTree.Span,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorBase.Pad(System.String,System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorBase.Pad(System.String,System.Web.Razor.Parser.SyntaxTree.Span,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.CodeGeneratorContext">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddCodeMapping(System.Web.Razor.Text.SourceLocation,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddContextCall(System.Web.Razor.Parser.SyntaxTree.Span,System.String,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddDesignTimeHelperStatement(System.CodeDom.CodeSnippetStatement)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddStatement(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.AddStatement(System.String,System.CodeDom.CodeLinePragma)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.String,System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.BufferStatementFragment(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.ChangeStatementCollector(System.Action`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CodeMappings">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CompileUnit">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.Create(System.Web.Razor.RazorEngineHost,System.String,System.String,System.String,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.CurrentBufferedStatement">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.EnsureExpressionHelperVariable">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.FlushBufferedStatement">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.GeneratedClass">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Parser.SyntaxTree.Span,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.GenerateLinePragma(System.Web.Razor.Text.SourceLocation,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.Host">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.MarkEndOfGeneratedCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CodeGeneratorContext.MarkStartOfGeneratedCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.Namespace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.SourceFile">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.TargetMethod">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.CodeGeneratorContext.TargetWriterName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.CSharpRazorCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CSharpRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.CSharpRazorCodeGenerator.Initialize(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Int32,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.Prefix">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.DynamicAttributeBlockCodeGenerator.ValueStart">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.ExpressionCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ExpressionCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.ExpressionRenderingMode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.ExpressionRenderingMode.WriteToOutput">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.ExpressionRenderingMode.InjectCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.GeneratedClassContext">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.AllowSections">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.AllowTemplates">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.BeginContextMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.Default">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultExecuteMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultLayoutPropertyName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteAttributeMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteAttributeToMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteLiteralMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.GeneratedClassContext.DefaultWriteMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.DefineSectionMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.EndContextMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.ExecuteMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.LayoutPropertyName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.op_Equality(System.Web.Razor.Generator.GeneratedClassContext,System.Web.Razor.Generator.GeneratedClassContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedClassContext.op_Inequality(System.Web.Razor.Generator.GeneratedClassContext,System.Web.Razor.Generator.GeneratedClassContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.ResolveUrlMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.SupportsInstrumentation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.TemplateTypeName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteAttributeMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteAttributeToMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteLiteralMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteLiteralToMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedClassContext.WriteToMethodName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.GeneratedCodeMapping">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.CodeLength">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.op_Equality(System.Web.Razor.Generator.GeneratedCodeMapping,System.Web.Razor.Generator.GeneratedCodeMapping)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.op_Inequality(System.Web.Razor.Generator.GeneratedCodeMapping,System.Web.Razor.Generator.GeneratedCodeMapping)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartColumn">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartGeneratedColumn">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartLine">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.GeneratedCodeMapping.StartOffset">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.GeneratedCodeMapping.ToString"></member>
    <member name="T:System.Web.Razor.Generator.HelperCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.Footer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.HeaderComplete">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.HelperCodeGenerator.Signature">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HelperCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.HybridCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.HybridCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.IBlockCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.IBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.IBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.ISpanCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ISpanCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.LiteralAttributeCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.String})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.#ctor(System.Web.Razor.Text.LocationTagged{System.String},System.Web.Razor.Text.LocationTagged{System.Web.Razor.Generator.SpanCodeGenerator})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Prefix">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.Value">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.LiteralAttributeCodeGenerator.ValueGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.MarkupCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.MarkupCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.ClassName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.Context">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.DesignTimeMode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.GenerateLinePragmas">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.Host">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.Initialize(System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.OnComplete">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.RootNamespaceName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorCodeGenerator.SourceFileName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCodeGenerator.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorCommentCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCommentCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorCommentCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.#ctor(System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Name">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.RazorDirectiveAttributeCodeGenerator.Value">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.ResolveUrlCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.ResolveUrlCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.SectionCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.#ctor(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.SectionCodeGenerator.SectionName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SectionCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.SetBaseTypeCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.#ctor(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.BaseType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.ResolveType(System.Web.Razor.Generator.CodeGeneratorContext,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetBaseTypeCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.SetLayoutCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.#ctor(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.SetLayoutCodeGenerator.LayoutPath">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetLayoutCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.SetVBOptionCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.#ctor(System.String,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Explicit(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.SetVBOptionCodeGenerator.ExplicitCodeDomOptionName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.SetVBOptionCodeGenerator.OptionName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Strict(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.SetVBOptionCodeGenerator.StrictCodeDomOptionName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SetVBOptionCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Generator.SetVBOptionCodeGenerator.Value">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.SpanCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.SpanCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Generator.SpanCodeGenerator.Null">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.StatementCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.StatementCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.TemplateBlockCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.GenerateEndBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TemplateBlockCodeGenerator.GenerateStartBlockCode(System.Web.Razor.Parser.SyntaxTree.Block,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.TypeMemberCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.GenerateCode(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Generator.CodeGeneratorContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.TypeMemberCodeGenerator.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Generator.VBRazorCodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Generator.VBRazorCodeGenerator.#ctor(System.String,System.String,System.String,System.Web.Razor.RazorEngineHost)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.BalancingModes">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.None">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.BacktrackOnFailure">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.NoErrorOnFailure">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.AllowCommentsAndTemplates">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.BalancingModes.AllowEmbeddedTransitions">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.CallbackVisitor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.#ctor(System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action{System.Web.Razor.Parser.SyntaxTree.BlockType},System.Action)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.OnComplete">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code. </summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CallbackVisitor.SynchronizationContext">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CallbackVisitor.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpCodeParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.AcceptIf(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.AssertDirective(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.At(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.BaseTypeDirective(System.String,System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.FunctionsDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.HandleEmbeddedTransition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.HelperDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.InheritsDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.InheritsDirectiveCore">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.IsNested">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.IsSpacingToken(System.Boolean,System.Web.Razor.Tokenizer.Symbols.CSharpSymbol)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Keywords">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Language">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.LayoutDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.MapDirectives(System.Action,System.String[])">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.OtherParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.OutputSpanBeforeRazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ParseBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ReservedDirective(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SectionDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateDirectiveCore">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.SessionStateTypeDirective(System.String,System.Func`3)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.TryGetDirectiveHandler(System.String,System.Action@)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.ValidSessionStateValue">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpCodeParser.Block">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.Block.#ctor(System.String,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpCodeParser.Block.#ctor(System.Web.Razor.Tokenizer.Symbols.CSharpSymbol)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Block.Name">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpCodeParser.Block.Start">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.CSharpLanguageCharacteristics">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetKeyword(System.Web.Razor.Tokenizer.Symbols.CSharpKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.CSharpLanguageCharacteristics.GetSymbolSample(System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.CSharpLanguageCharacteristics.Instance">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.HtmlLanguageCharacteristics">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlLanguageCharacteristics.Instance">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.HtmlMarkupParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.IsSpacingToken(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.Language">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.OtherParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.OutputSpanBeforeRazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseDocument">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.ParseSection(System.Tuple`2,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.SkipToAndParseCode(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.HtmlMarkupParser.SkipToAndParseCode(System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.HtmlMarkupParser.VoidElements">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.LanguageCharacteristics`3">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <typeparam name="TTokenizer"></typeparam>
      <typeparam name="TSymbol"></typeparam>
      <typeparam name="TSymbolType"></typeparam>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,`2,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.FlipBracket(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.GetSample(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentBody(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentStar(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsCommentStart(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsIdentifier(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsKeyword(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsKnownSymbolType(`1,System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsNewLine(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsTransition(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsUnknown(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.IsWhiteSpace(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.KnowsSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.SplitSymbol(System.Boolean,`1,`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.TokenizeString(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.LanguageCharacteristics`3.TokenizeString(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserBase">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.Context">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.IsMarkupParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserBase.OtherParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseDocument">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserBase.ParseSection(System.Tuple`2,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserContext">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.#ctor(System.Web.Razor.Text.ITextDocument,System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.ActiveParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.AddSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CodeParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.CompleteParse">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CurrentBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.CurrentCharacter">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.DesignTimeMode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.EndBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.EndOfFile">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.Errors">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.IsWithin(System.Web.Razor.Parser.SyntaxTree.BlockType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.LastAcceptedCharacters">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.LastSpan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.MarkupParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.OnError(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.OnError(System.Web.Razor.Text.SourceLocation,System.String,System.Object[])">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.Source">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.StartBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.StartBlock(System.Web.Razor.Parser.SyntaxTree.BlockType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserContext.SwitchActiveParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserContext.WhiteSpaceIsSignificantToAncestorBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserHelpers">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsCombining(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsConnecting(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsDecimalDigit(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsEmailPart(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsFormatting(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsHexDigit(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifier(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifier(System.String,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifierPart(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsIdentifierStart(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsLetter(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsLetterOrDecimalDigit(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsNewLine(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsNewLine(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsTerminatingCharToken(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsTerminatingQuotedStringToken(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsWhitespace(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.IsWhitespaceOrNewLine(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserHelpers.SanitizeClassName(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserVisitor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.ParserVisitor.CancelToken">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.OnComplete">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.ThrowIfCanceled">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitEndBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitError(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitSpan(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitor.VisitStartBlock(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.ParserVisitorExtensions">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.ParserVisitorExtensions.Visit(System.Web.Razor.Parser.ParserVisitor,System.Web.Razor.ParserResults)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.RazorParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.#ctor(System.Web.Razor.Parser.ParserBase,System.Web.Razor.Parser.ParserBase)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.CancellationToken)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.SynchronizationContext)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Action{System.Web.Razor.Parser.SyntaxTree.Span},System.Action{System.Web.Razor.Parser.SyntaxTree.RazorError},System.Threading.SynchronizationContext,System.Threading.CancellationToken)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.CreateParseTask(System.IO.TextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.RazorParser.DesignTimeMode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.IO.TextReader)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.IO.TextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.LookaheadTextReader)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.RazorParser.Parse(System.Web.Razor.Text.LookaheadTextReader,System.Web.Razor.Parser.ParserVisitor)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.EndCommentSequence">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.StartCommentSequence">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TextTagName">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TransitionCharacter">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.TransitionString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants.CSharp">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.ClassKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.ElseIfKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.FunctionsKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.HelperKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.InheritsKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.LayoutKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.NamespaceKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.SectionKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.SessionStateKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.CSharp.UsingKeywordLength">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxConstants.VB">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.CodeKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndCodeKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndFunctionsKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndHelperKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.EndSectionKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.ExplicitKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.FunctionsKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.HelperKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.ImportsKeywordLength">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.LayoutKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.OffKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SectionKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SelectCaseKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.SessionStateKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxConstants.VB.StrictKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.TokenizerBackedParser`3">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <typeparam name="TTokenizer"></typeparam>
      <typeparam name="TSymbol"></typeparam>
      <typeparam name="TSymbolType"></typeparam>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Accept(System.Collections.Generic.IEnumerable{`1})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Accept(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptAll(`2[])">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptAndMoveNext">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptSingleWhiteSpaceCharacter">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2,`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2,`2,`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptUntil(`2[])">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2,`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2,`2,`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhile(`2[])">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AcceptWhiteSpaceInLines">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AddMarkerSymbolIfNecessary">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AddMarkerSymbolIfNecessary(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.At(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.AtIdentifier(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Balance(System.Web.Razor.Parser.BalancingModes)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Balance(System.Web.Razor.Parser.BalancingModes,`2,`2,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.BuildSpan(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ConfigureSpan(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ConfigureSpan(System.Action`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.CurrentLocation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.CurrentSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.EndOfFile">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.EnsureCurrent">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Expected(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Expected(`2[])">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.HandleEmbeddedTransition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Initialize(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Language">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextIs(`2[])">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.NextToken">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Optional(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Optional(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.SpanKind)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Output(System.Web.Razor.Parser.SyntaxTree.SpanKind,System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.OutputSpanBeforeRazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.PreviousSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PushSpanConfig(System.Action`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutBack(System.Collections.Generic.IEnumerable{`1})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutBack(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.PutCurrentBack">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.RazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.ReadWhile(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Required(`2,System.Boolean,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Span">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.SpanConfig">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.TokenizerBackedParser`3.Tokenizer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.TokenizerBackedParser`3.Was(`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.VBCodeParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.AcceptVBSpaces">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Assert(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.AssertDirective(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.At(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.At(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedDirective(System.Void,System.Boolean,System.String,System.Web.Razor.Parser.SyntaxTree.BlockType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedDirectiveBody(System.String,System.Web.Razor.Text.SourceLocation,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedStatement(System.Void,System.Boolean,System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.EndTerminatedStatement(System.Void,System.Boolean,System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleEmbeddedTransition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleEmbeddedTransition(System.Web.Razor.Tokenizer.Symbols.VBSymbol)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleExitOrContinue(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HandleTransition(System.Web.Razor.Tokenizer.Symbols.VBSymbol)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.HelperDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ImportsStatement">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.InheritsStatement">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.IsAtEmbeddedTransition(System.Boolean,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.IsDirectiveDefined(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.Keywords">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.KeywordTerminatedStatement(System.Void,System.Boolean,System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.Language">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.LayoutDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.MapDirective(System.String,System.Func`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.MapKeyword(System.Web.Razor.Tokenizer.Symbols.VBKeyword,System.Func`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.NestedBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Optional(System.Web.Razor.Tokenizer.Symbols.VBKeyword)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OptionStatement">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.VBCodeParser.OtherParser">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OtherParserBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OtherParserBlock(System.String,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.OutputSpanBeforeRazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ParseBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ReadVBSpaces">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.Required(System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.ReservedWord">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.SectionDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBCodeParser.SessionStateDirective">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.VBLanguageCharacteristics">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateMarkerSymbol(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.CreateTokenizer(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.FlipBracket(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.GetKnownSymbolType(System.Web.Razor.Tokenizer.Symbols.KnownSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.VBLanguageCharacteristics.GetSample(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.VBLanguageCharacteristics.Instance">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.None">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.NewLine">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.WhiteSpace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.NonWhiteSpace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.AllWhiteSpace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.Any">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.AcceptedCharacters.AnyExceptNewline">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.#ctor(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.#ctor(System.Func`2,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.AutoCompleteAtEndOfSpan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.AutoCompleteString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.CanAcceptChange(System.Web.Razor.Parser.SyntaxTree.Span,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.AutoCompleteEditHandler.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.Block">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.#ctor(System.Web.Razor.Parser.SyntaxTree.BlockBuilder)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Children">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.CodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.FindFirstDescendentSpan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.FindLastDescendentSpan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.Flatten">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.IsBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Length">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.LocateOwner(System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Name">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Start">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Block.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Block.Type">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.BlockBuilder">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.#ctor(System.Web.Razor.Parser.SyntaxTree.Block)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Build">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Children">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.CodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Name">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Reset">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.BlockBuilder.Type">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.BlockType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Statement">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Directive">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Functions">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Expression">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Helper">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Markup">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Section">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Template">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.BlockType.Comment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.RazorError">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Int32,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.#ctor(System.String,System.Web.Razor.Text.SourceLocation,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.Equals(System.Web.Razor.Parser.SyntaxTree.RazorError)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Length">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Location">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.RazorError.Message">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.RazorError.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.Span">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.#ctor(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Change(System.Action{System.Web.Razor.Parser.SyntaxTree.SpanBuilder})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.CodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Content">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.EditHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.IsBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Kind">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Length">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Next">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Previous">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ReplaceWith(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Start">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.Span.Symbols">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.Span.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SpanBuilder">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.#ctor(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Accept(System.Web.Razor.Tokenizer.Symbols.ISymbol)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Build">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.ClearSymbols">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.CodeGenerator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.EditHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Kind">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Reset">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Start">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SpanBuilder.Symbols">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SpanKind">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Transition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.MetaCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Comment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Code">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Parser.SyntaxTree.SpanKind.Markup">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Accept(System.Web.Razor.Parser.ParserVisitor)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.EquivalentTo(System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <returns>.</returns>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.IsBlock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Length">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Parent">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Parser.SyntaxTree.SyntaxTreeNode.Start">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.BufferingTextReader">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.#ctor(System.IO.TextReader)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.BeginLookahead">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.CancelBacktrack">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.BufferingTextReader.CurrentCharacter">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.BufferingTextReader.CurrentLocation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Dispose(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.ExpandBuffer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.NextCharacter">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Peek">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.BufferingTextReader.Read">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.ITextBuffer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextBuffer.Length">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.ITextBuffer.Peek">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextBuffer.Position">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.ITextBuffer.Read">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.ITextDocument">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.ITextDocument.Location">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.LocationTagged`1">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <typeparam name="T"></typeparam>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.#ctor(`0,System.Int32,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.#ctor(`0,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.LocationTagged`1.Location">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Equality(System.Web.Razor.Text.LocationTagged{`0},System.Web.Razor.Text.LocationTagged{`0})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Implicit(System.Web.Razor.Text.LocationTagged{`0})~`0">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.op_Inequality(System.Web.Razor.Text.LocationTagged{`0},System.Web.Razor.Text.LocationTagged{`0})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LocationTagged`1.ToString(System.String,System.IFormatProvider)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.LocationTagged`1.Value">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.LookaheadTextReader">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.BeginLookahead">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadTextReader.CancelBacktrack">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.LookaheadTextReader.CurrentLocation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.LookaheadToken">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.#ctor(System.Action)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Accept">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Dispose">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.LookaheadToken.Dispose(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.SeekableTextReader">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.IO.TextReader)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.#ctor(System.Web.Razor.Text.ITextBuffer)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Length">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Location">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.Peek">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.SeekableTextReader.Position">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SeekableTextReader.Read">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.SourceLocation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.#ctor(System.Int32,System.Int32,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.AbsoluteIndex">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Add(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Advance(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.CharacterIndex">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.CompareTo(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Equals(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocation.LineIndex">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Addition(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Equality(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_GreaterThan(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Inequality(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_LessThan(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.op_Subtraction(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.Subtract(System.Web.Razor.Text.SourceLocation,System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocation.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Text.SourceLocation.Undefined">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Text.SourceLocation.Zero">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.SourceLocationTracker">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.#ctor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.#ctor(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.CalculateNewLocation(System.Web.Razor.Text.SourceLocation,System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.SourceLocationTracker.CurrentLocation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.UpdateLocation(System.Char,System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.SourceLocationTracker.UpdateLocation(System.String)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.TextBufferReader">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.#ctor(System.Web.Razor.Text.ITextBuffer)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.BeginLookahead">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.CancelBacktrack">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextBufferReader.CurrentLocation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Dispose(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Peek">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextBufferReader.Read">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.TextChange">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.#ctor(System.Int32,System.Int32,System.Web.Razor.Text.ITextBuffer,System.Int32,System.Int32,System.Web.Razor.Text.ITextBuffer)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ApplyChange(System.String,System.Int32)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ApplyChange(System.Web.Razor.Parser.SyntaxTree.Span)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsDelete">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsInsert">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.IsReplace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewBuffer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewLength">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewPosition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.NewText">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.Normalize">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldBuffer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldLength">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldPosition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextChange.OldText">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.op_Equality(System.Web.Razor.Text.TextChange,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.op_Inequality(System.Web.Razor.Text.TextChange,System.Web.Razor.Text.TextChange)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextChange.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.TextChangeType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Text.TextChangeType.Insert">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Text.TextChangeType.Remove">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Text.TextDocumentReader">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Length">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Location">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.Peek">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Text.TextDocumentReader.Position">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Text.TextDocumentReader.Read">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.CSharpHelpers">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsIdentifierPart(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsIdentifierStart(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpHelpers.IsRealLiteralSuffix(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.CSharpTokenizer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.CSharpTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentStarType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentTransitionType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.RazorCommentType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.CSharpTokenizer.StartState">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.HtmlTokenizer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.HtmlTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.HtmlTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentStarType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentTransitionType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.RazorCommentType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.HtmlTokenizer.StartState">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.ITokenizer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.ITokenizer.NextSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Tokenizer`2">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <typeparam name="TSymbol"></typeparam>
      <typeparam name="TSymbolType"></typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.AfterRazorCommentTransition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.At(System.String,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.Buffer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.CharOrWhiteSpace(System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,`1,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentCharacter">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentErrors">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentLocation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.CurrentStart">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.EndOfFile">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.EndSymbol(System.Web.Razor.Text.SourceLocation,`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.EndSymbol(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.HaveContent">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.MoveNext">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.NextSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Peek">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentBody">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentStarType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentTransitionType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.RazorCommentType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Reset">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.ResumeSymbol(`0)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.Single(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Tokenizer`2.Source">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.StartSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.System#Web#Razor#Tokenizer#ITokenizer#NextSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeAll(System.String,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeCurrent">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeString(System.String,System.Boolean)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Tokenizer`2.TakeUntil(System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.TokenizerView`3">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <typeparam name="TTokenizer"></typeparam>
      <typeparam name="TSymbol"></typeparam>
      <typeparam name="TSymbolType"></typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.#ctor(`0)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Current">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.EndOfFile">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.Next">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.TokenizerView`3.PutBack(`1)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Source">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.TokenizerView`3.Tokenizer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.VBHelpers">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsDoubleQuote(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsOctalDigit(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBHelpers.IsSingleQuote(System.Char)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.VBTokenizer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBTokenizer.#ctor(System.Web.Razor.Text.ITextDocument)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.VBTokenizer.CreateSymbol(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentStarType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentTransitionType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.RazorCommentType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.VBTokenizer.StartState">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Abstract">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Byte">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Class">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Delegate">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Event">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Fixed">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.If">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Internal">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.New">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Override">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Readonly">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Short">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Struct">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Try">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Unsafe">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Volatile">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.As">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Do">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Is">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Params">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ref">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Switch">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ushort">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.While">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Case">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Const">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Explicit">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Float">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Null">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sizeof">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Typeof">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Implicit">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Private">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.This">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Using">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Extern">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Return">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Stackalloc">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Uint">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Base">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Catch">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Continue">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Double">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.For">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.In">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Lock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Object">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Protected">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Static">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.False">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Public">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sbyte">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Throw">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Virtual">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Decimal">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Else">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Operator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.String">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Ulong">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Bool">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Char">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Default">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Foreach">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Long">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Void">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Enum">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Finally">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Int">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Out">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Sealed">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.True">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Goto">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Unchecked">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Interface">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Break">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Checked">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpKeyword.Namespace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.EscapedIdentifier">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.CSharpSymbol.Keyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Unknown">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Identifier">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Keyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.IntegerLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NewLine">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.WhiteSpace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Comment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RealLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.CharacterLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.StringLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Arrow">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Minus">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Decrement">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.MinusAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NotEqual">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Not">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Modulo">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.ModuloAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.AndAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.And">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleAnd">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftParenthesis">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightParenthesis">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Star">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.MultiplyAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Comma">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Dot">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Slash">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DivideAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleColon">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Colon">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Semicolon">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.QuestionMark">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.NullCoalesce">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightBracket">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftBracket">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.XorAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Xor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftBrace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.OrAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.DoubleOr">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Or">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightBrace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Tilde">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Plus">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.PlusAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Increment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LessThan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LessThanEqual">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftShift">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.LeftShiftAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Assign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Equals">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.GreaterThan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.GreaterThanEqual">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightShift">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RightShiftAssign">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Hash">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.Transition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorCommentTransition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorCommentStar">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.CSharpSymbolType.RazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.HtmlSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Unknown">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Text">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.WhiteSpace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.NewLine">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.OpenAngle">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Bang">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Solidus">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.QuestionMark">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.DoubleHyphen">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.LeftBracket">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.CloseAngle">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RightBracket">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Equals">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.DoubleQuote">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.SingleQuote">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Transition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.Colon">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorCommentStar">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.HtmlSymbolType.RazorCommentTransition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.ISymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.ISymbol.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.ISymbol.Content">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.ISymbol.OffsetStart(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.ISymbol.Start">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.WhiteSpace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.NewLine">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Identifier">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Keyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Transition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.Unknown">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentStart">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentStar">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.KnownSymbolType.CommentBody">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
      <typeparam name="TType"></typeparam>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.#ctor(System.Web.Razor.Text.SourceLocation,System.String,`0,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.ChangeStart(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Content">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Errors">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.OffsetStart(System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Start">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.ToString">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.SymbolBase`1.Type">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Collections.Generic.IEnumerable{System.Web.Razor.Tokenizer.Symbols.ISymbol},System.Web.Razor.Text.SourceLocation)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Parser.SyntaxTree.SpanBuilder)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Parser.SyntaxTree.SpanBuilder,System.Func`2)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.SymbolExtensions.GetContent(System.Web.Razor.Tokenizer.Symbols.ISymbol)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBKeyword">
      <summary>Enumerates the list of Visual Basic keywords.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AddHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AndAlso">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Byte">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Catch">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDate">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CInt">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Const">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CSng">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CULng">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Declare">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.DirectCast">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Else">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Enum">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Exit">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Friend">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GetXmlNamespace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Handles">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.In">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Is">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Like">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Mod">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MyBase">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.New">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.AddressOf">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.As">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ByVal">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CBool">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDbl">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Class">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Continue">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CStr">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CUShort">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Default">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Do">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ElseIf">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Erase">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.False">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Function">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Global">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.If">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Inherits">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.IsNot">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Long">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Module">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MyClass">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Next">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Alias">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Boolean">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Call">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CByte">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CDec">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CLng">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CSByte">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Date">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Delegate">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Double">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.End">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Error">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Finally">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Get">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GoSub">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Implements">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Integer">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Let">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Loop">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MustInherit">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Namespace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Not">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.And">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ByRef">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Case">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CChar">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Char">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CObj">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CShort">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.CUInt">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Decimal">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Dim">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Each">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.EndIf">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Event">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.For">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GetType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.GoTo">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Imports">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Interface">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Lib">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Me">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.MustOverride">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Narrowing">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Nothing">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.NotInheritable">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.On">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Or">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overrides">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Property">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ReadOnly">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Resume">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Set">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Single">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.String">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Then">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Try">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ULong">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Wend">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.With">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.NotOverridable">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Operator">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.OrElse">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ParamArray">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Protected">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.ReDim">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Return">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Shadows">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Static">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Structure">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Throw">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.TryCast">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.UShort">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.When">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.WithEvents">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Object">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Option">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overloads">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Partial">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Public">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Rem">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.SByte">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Shared">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Step">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Sub">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.To">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.TypeOf">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Using">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.While">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.WriteOnly">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Of">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Optional">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Overridable">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Private">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.RaiseEvent">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.RemoveHandler">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Select">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Short">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Stop">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.SyncLock">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.True">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.UInteger">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Variant">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Widening">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBKeyword.Xor">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBSymbol">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Int32,System.Int32,System.Int32,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.#ctor(System.Web.Razor.Text.SourceLocation,System.String,System.Web.Razor.Tokenizer.Symbols.VBSymbolType,System.Collections.Generic.IEnumerable{System.Web.Razor.Parser.SyntaxTree.RazorError})">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.Equals(System.Object)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.GetHashCode">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="M:System.Web.Razor.Tokenizer.Symbols.VBSymbol.GetSample(System.Web.Razor.Tokenizer.Symbols.VBSymbolType)">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="P:System.Web.Razor.Tokenizer.Symbols.VBSymbol.Keyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="T:System.Web.Razor.Tokenizer.Symbols.VBSymbolType">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Unknown">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.WhiteSpace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.NewLine">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LineContinuation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Comment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Identifier">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Keyword">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.IntegerLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.FloatingPointLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.StringLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.CharacterLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.DateLiteral">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftParenthesis">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightBrace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftBrace">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightParenthesis">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Hash">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Bang">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Comma">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Dot">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Colon">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Concatenation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.QuestionMark">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Subtract">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Multiply">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Add">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Divide">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.IntegerDivide">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Exponentiation">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LessThan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.GreaterThan">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Equal">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RightBracket">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.LeftBracket">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Dollar">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.Transition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorCommentTransition">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorCommentStar">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
    <member name="F:System.Web.Razor.Tokenizer.Symbols.VBSymbolType.RazorComment">
      <summary>This type/member supports the .NET Framework infrastructure and is not intended to be used directly from your code.</summary>
    </member>
  </members>
</doc>