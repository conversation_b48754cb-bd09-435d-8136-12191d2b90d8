﻿@{
    
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>LayoutPage</title>
    <link rel="icon" href="~/Assets/Images/Logo/favicon.ico" />
    <link href="~/Assets/Plugins/JQueryUI/JqueryUI-1.12.1/jquery-ui-1.12.1.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/Bootstrap/bootstrap-3.3.6-dist/css/bootstrap3.6.css" rel="stylesheet" />
    <link href="~/Assets/Themes/hot-sneaks/jquery-ui.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/ui.jqgrid.css" rel="stylesheet" />
    <link href="~/Assets/Fonts/font-awesome-4.7.0/css/font-awesome.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/FullCalender/fullcalendar.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/SweetAlerts/sweetalert.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/TimePicker/jquery.timepicker.css" rel="stylesheet" />
    <link href="~/Assets/CSS/_Layout.css" rel="stylesheet" />
     <link href="~/Content/CSS/ButtonStyle.css" rel="stylesheet" />
    <link href="~/Content/CSS/Login.css" rel="stylesheet" />

    <script src="~/Assets/Plugins/JQuery/jquery-2.1.3.js"></script>
    <script src="~/Assets/Plugins/JQueryUI/jquery-ui-1.11.3.js"></script>
    <script src="~/Assets/Plugins/Bootstrap/bootstrap-3.3.6-dist/js/bootstrap.js"></script>
    <script src="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/grid.locale-en.js"></script>
    <script src="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/jquery.jqGrid.js"></script>
    <script src="~/Assets/Plugins/FullCalender/moment.min.js"></script>
    <script src="~/Assets/Plugins/FullCalender/fullcalendar.js"></script>
    <script src="~/Assets/Plugins/TimePicker/jquery.timepicker.js"></script>
    <script src="~/Assets/Plugins/SweetAlerts/sweetalert.js"></script>
    <style>
        .sweet-alert {
            background-color: white;
            width: 350px;
            left: 60%;
            top: 50%;
            height: auto;
        }

            .sweet-alert p {
                color: #575757;
                font-size: 15px;
                text-align: center;
                font-weight: bold;
                position: relative;
                text-align: inherit;
                float: none;
                margin: 0;
                padding: 0;
                line-height: normal;
            }

            .sweet-alert h2 {
                color: #575757;
                font-size: 15px;
                text-align: center;
                font-weight: bold;
                text-transform: none;
                position: relative;
                margin: -15px 0;
                padding: 0;
                line-height: 30px;
                display: block;
            }
        .container-fluid {
            position: absolute;
            padding: 0px;
            left: 0px;
            right: 0px;
            top: 31%;
            width:80%;
        }

        .input-group {
            width: 90%;
        }

        #SpnLognHeading {
            font-size: 26px;
            font-weight: bold;
            color: white;
        }

        .panel {
            border-radius: 6px;
        }

        #DivLoginBody {
            /*margin-top:381px;*/
        }

        #HeaderBG {
            /*background-image: url(Content/Image/mesto1.png);*/
            /*border: solid 1px red;*/
            height: auto;
            background-repeat: no-repeat;
            background-size: 101% 101%;
            -webkit-background-size: cover;
            -moz-background-size: cover;
            -o-background-size: cover;
            background-size: cover;
            border-bottom: 1px solid #006666;
            width:103%;

        }
        .bg {
             background-size:100%;
            background-repeat: no-repeat;
            -webkit-background-size: cover;
            -moz-background-size: cover;
            -o-background-size: cover;
            background-size: cover;
            width:98%;
        }
    </style>
</head>
<body class="bg">
     <div class="row " id="HeaderBG">
        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-11">
            <div class="row">
                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1">
                    <img src="~/Content/Image/Logo.png" style="height: 30px;  margin-top: 10px;" />
                </div>
                <div class="col-lg-11 col-md-11 col-sm-11 col-xs-11">
                    <div class="text-md-center" style="font-size: 24px; text-align: center; color: #006363; font-weight: bold; margin-top: 10px;">Installation & Commissioning System</div>
                </div>
            </div>
        </div>
        <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1" style="display: none;">
            <img src="~/Content/Image/logout.png" id="ImgLogout" data-tooltip="title" title="Logout" style="cursor: pointer; height: 30px;  margin-top: 10px;" />
        </div>

    </div>


 <div class="row " style="margin-top:150px">
                <div class="text-center col-md-12"><span style="color: #f5f5f5;font-size: 38px;background: #132b4c;">Access Denied<br />Please Login with valid User <a href="~/Home/Index" style="color:#e26b25">Click Here</a></span></div>
            </div>

</body>
</html>

<script>
    

    $("#HeaderBG").css('background-image', 'url("' + AbsolutePath("/Content/Image/mesto1.png") + '")');
    $("#TxtUsername").focus();
    //$(document).ready(function () {
    //    $(".clickable").click(function () {
    //        if ($("#TxtUsername").val() == "admin" && $("#TxtPassword").val() == "12345") {
    //            window.location.href = AbsolutePath("/Admin/Projections");
    //        }
    //        else if ($("#TxtUsername").val() == "user" && $("#TxtPassword").val() == "12345") {
    //            window.location.href = AbsolutePath("/User/Projections");
    //        } else {
    //            swal({
    //                title: "Error Message",
    //                text: "Wrong User ID or Password",
    //                type: "error",
    //                confirmButtonText: "OK",
    //            });
    //            $("#TxtUsername").val("");
    //            $("#TxtPassword").val("");
    //        }

    //    });

    //    $(window).keyup(
    //       function (e) {
    //           if (e.keyCode == 13) {
    //               if ($("#TxtUsername").val() == "admin" && $("#TxtPassword").val() == "12345") {

    //                   window.location.href = AbsolutePath("/Admin/Projections");

    //               }
    //               else if ($("#TxtUsername").val() == "user" && $("#TxtPassword").val() == "12345") {

    //                   window.location.href = AbsolutePath("/User/Projections");

    //               } else {
    //                   swal({
    //                       title: "Error Message",
    //                       text: "Wrong User ID or Password",
    //                       type: "error",
    //                       confirmButtonText: "OK",
    //                   });
    //                   $("#TxtUsername").val("");
    //                   $("#TxtPassword").val("");
    //               }
    //           }

    //       });

    //});


    $("html").css("height", "100%");
    $(".bg").css({ "background-image": "url(" + AbsolutePath('/Content/Image/MICP_Img.jpg') + ")", "background-repeat": "no-repeat", "background-size": "100% 100%", "height": "100%", "margin-top": "0px" });


    //--------------------------------------------------TO MAKE ABSOLUTE PATH--------------------------------------------
    function AbsolutePath(url) {
        var path = '@Request.ApplicationPath';
        if (path == '/')
            return url;
        else
            return path + url;
    }

    //Common Alert Dialog Func
    var previousWindowKeyDown;
    function AlertMessage(title, type) {
        if (previousWindowKeyDown !== undefined && window.onkeydown !== previousWindowKeyDown) {
            window.onkeydown = previousWindowKeyDown;
        }
        swal({
            title: title,
            type: type,
            closeOnConfirm: true,
            confirmButtonText: "OK",
            width: '850px',
            allowEscapeKey: false
        }, function () {
            if (window.onkeydown != null && window.onfocus != null) {
                window.onkeydown = null;
                window.onfocus = null;
            }
            var previousWindowKeyDown = window.onkeydown;
        })
        $(".sweet-alert ").children().eq(7).focus();
    }
    /*-------------Login functionality development --Coded by:Raheem 29-Jan-2018-----------------*/

  
    
  





</script>