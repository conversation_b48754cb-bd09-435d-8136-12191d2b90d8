﻿<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>@ViewBag.Title</title>

    <link rel="icon" href="~/Assets/Images/Logo/favicon.ico" />
    <link href="~/Assets/Plugins/JQueryUI/JqueryUI-1.12.1/jquery-ui-1.12.1.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/Bootstrap/bootstrap-3.3.6-dist/css/bootstrap3.6.css" rel="stylesheet" />
    <link href="~/Assets/Themes/hot-sneaks/jquery-ui.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/ui.jqgrid.css" rel="stylesheet" />
    <link href="~/Assets/Fonts/font-awesome-4.7.0/css/font-awesome.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/FullCalender/fullcalendar.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/SweetAlerts/sweetalert.css" rel="stylesheet" />
    <link href="~/Assets/Plugins/TimePicker/jquery.timepicker.css" rel="stylesheet" />
    <link href="~/Assets/CSS/_Layout.css" rel="stylesheet" />


    <script src="~/Assets/Plugins/JQuery/jquery-2.1.3.js"></script>
    <script src="~/Assets/Plugins/JQueryUI/jquery-ui-1.11.3.js"></script>
    <script src="~/Assets/Plugins/Bootstrap/bootstrap-3.3.6-dist/js/bootstrap.js"></script>
    <script src="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/grid.locale-en.js"></script>
    <script src="~/Assets/Plugins/JQGrid/JQGrid-4.7.0/jquery.jqGrid.js"></script>
    <script src="~/Assets/Plugins/FullCalender/moment.min.js"></script>
    <script src="~/Assets/Plugins/FullCalender/fullcalendar.js"></script>
    <script src="~/Assets/Plugins/TimePicker/jquery.timepicker.js"></script>
    <script src="~/Assets/Plugins/SweetAlerts/sweetalert.js"></script>

    <style>
        * {
            font-family: Helvetica Neue,Helvetica,Arial,sans-serif;
        }

        .form-control {
            font-family: Helvetica Neue,Helvetica,Arial,sans-serif;
        }

        .ClsTxtQuotaionHeader {
            font-size: 12px !important;
            font-weight: bold !important;
        }

        body {
            margin: 0px 0px;
            padding: 0px 0px;
        }

        .form-control {
            height: 30px;
        }

        .navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
            border-color: #2c5dad;
            background-color: #2c5dad;
        }

        .navbar-inverse {
            background-color: #0858dc;
            border-color: #0858dc;
        }

        ul > li {
            /*background-color: #3266bb;
            border-left: solid white 2px;*/
            color: white;
            font-family: sans-serif;
            text-align: center;
            /*width: 130px;*/
            /*font-size: 16px;*/
            font-weight: bold;
        }

        a {
            color: white;
            font-family: sans-serif;
        }

        ul, ol {
            width: 100%;
        }

        textarea {
            resize: none;
        }

        label {
            font-family: sans-serif;
            font-size: 12px;
            font-weight: bold;
        }

     
         #IDHeaderLogo {
            height: auto;
            background:url(@Url.Content("~/Assets/Images/MestoBackgroundImage.png"));
            color: black;
            width: auto;
        }

        #SpanHeader {
            color: #3266bb;
            font-weight: bold;
            font-size: 27px;
        }

        #IdDivision {
            /*margin-top: -25px;*/
            height: 720px;
            background: #e2edec;
            border-top: solid #317c77 1px;
            box-shadow: 0px 0px 15px 0px rgb(50, 125, 120);
        }
      .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
          box-shadow: 2px 2px 2px rgba(255, 255, 255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4)
        background:rgba(221, 249, 249, 0.69);
      @*  color:#327d78;*@
          color:black;
          border: 1px solid rgba(221, 249, 249, 0.69)
        }
        .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active{
    
    box-shadow: 2px 2px 2px rgba(255, 255, 255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4);
        }
      
        #IdPHeaderQuote {
            color: #3266bb;
            font-weight: bold;
            font-size: 20px;
            font-family: sans-serif;
        }

        .ui-jqgrid .ui-jqgrid-pager .ui-pg-button {
    cursor: pointer;
         box-shadow: 2px 2px 2px rgba(255, 255, 255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4);
}
        #IdLogo {
            height: 50px;
        }

        /*--------------------------------------------------- cursor: pointer -----------------------------*/

        #IdImgRightArrow, #IdImgLeftArrow, #logout, #IdSaveTermsAndConditions {
            cursor: pointer;
        }

        /*-------------------------------------------- Horizontal Menu----------------------------------*/
        .navbar-inverse .navbar-brand {
            color: #ffffff;
            font-weight: bold;
            font-size: 13px;
        }

        .navbar-inverse .navbar-nav > li > a {
            color: #ffffff;
            font-weight: bold;
            font-size: 13px;
            font-family: sans-serif;
        }

        #myNavbar {
            margin-left: -45px;
        }



        .navbar-nav > li > a {
            padding-top: 5px;
            padding-bottom: 5px;
        }

        .MainContainerBody {
            height: 750px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .navbar-default {
            background-color: white;
            border-color: white;
        }

            .navbar-default .navbar-nav > li > a {
                color: #f5f5f5;
            }

        #IDDivNavbarHeader a {
            display: none;
        }

        .menuActive {
            background: #e4562b !important;
            color: white !important;
            font-weight: bold;
            font-size: 12px;
            font-family: sans-serif;
        }

        @@media only screen and (max-width: 1200px) {
            #myNavbar ul li {
                border-radius: 10px;
                width: 76px;
                font-size: 7px;
                height: 10px;
                font-weight: bold;
                border: solid white 2px;
                border-bottom: solid white 1px;
            }

            .menuActive {
                background: #e4562b !important;
                color: white !important;
                font-weight: bold;
                font-size: 7px;
                font-family: sans-serif;
            }
        }


        @@media only screen and (max-width: 765px) {
            #IDDivNavbarHeader a {
                display: block;
            }

            .navbar-default {
                background-color: #f8f8f8;
                border-color: #e7e7e7;
            }

            #myNavbar ul li {
                border-radius: 10px;
                width: 100%;
                font-size: 12px;
                border: solid white 2px;
                border-bottom: solid white 1px;
            }

            #myNavbar {
                margin-left: 1px;
            }

            .ui-widget-header {
                font-size: 6px;
            }
        }

        /*------------------------------------------ Modal Header ----------------------------------------------*/
        .modal {
            margin-top: 0px;
            margin-left: 0px;
            padding-left: 1px;
            margin-top: -30px;
        }

        .modal-header {
            height: 45px;
            /* background-color: #8ac3fb; */
            color: #1f716c;
            line-height: 0px;
            padding-top: 10px;
            text-align: center;
          @*  border: solid 1px #006666;*@
           background: #8bcaca;
          box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4);
        }

            .modal-header span {
                color: black;
                font-size: 16px;
                font-weight: bold;
            }

            .modal-header .close {
                margin-top: 0px;
                color: #ffffff;
                opacity: 1;
            }
        .modal-title {
   
    font-weight: bold;
}
        .modal-footer {
            padding: 15px;
            text-align: right;
            border-top: 1px solid #006666;
        }
        /*----------------------------------------------------- Select ----------------------------------------------*/

        .SelectStyle {
            background: linear-gradient(to bottom,white,#c6dff9);
            background: -moz-linear-gradient(to bottom,white,#c6dff9);
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#e07034',GradientType=1 );
            background: -webkit-linear-gradient(to bottom,white,#c6dff9);
        }

        /*----------------------------------------------------- Accordian -----------------------------------------*/

        .ClsliTodayActivity {
            margin-top: 2px;
            padding: 5px;
            width: 100%;
            color: #000;
            font-family: sans-serif;
            font-weight: bold;
            text-align: left;
            border-bottom: solid #91c5ff 1px;
            border-top: solid #91c5ff 1px;
            list-style-type: none;
            background-color: white;
            font-size: 11px;
        }

        /*----------------------------------------------------- Button -----------------------------------------*/

        .ButtonStyle {
            color: #fff;
            background-color: #327d78;
            border-color: #327d78;
            box-shadow: 2px 2px 6px 2px #a7a7a7;
            outline: 0;
        }

        .btn:hover {
            background: #e4562b;
            border-color: #e4562b;
            color: #fff;
        }

        .AddNew {
    width: 90%;
    height: 22px;
    background-color: #e4562b;
    color: white;
    font-family: sans-serif;
    font-weight: bold;
    margin-left: 10px;
    margin-bottom: 20px;
    outline: 0;
    border: 0px;
}

        /*----------------------------------------------------- Lef tMenu -----------------------------------------*/

        #IdLeftMenu li {
       width: 100%;
    height: 30px;
    border-bottom: solid 2px white;
    list-style-type: none;
    padding-top: 5px;
    padding-left: 5px;
    text-align: left;
    cursor: pointer;
    background-color: #327d78;   
    font-size: 12px;
}

            #IdLeftMenu li:hover 
              {
                background-color: #e4562b;
                font-size: 12px;
            }
        /*=================================group-addon==================================*/
        .input-group-addon {
            padding: 0px 2px;
            font-size: 10px;
            font-weight: normal;
            line-height: 1;
            color: white;
            text-align: center;
            background-color: #327d78;
            border: 1px solid #327d78;
            border-radius: 4px;
        }
        /*============================= Grid Themeing ============================*/
        .ui-th-column, .ui-jqgrid .ui-jqgrid-htable th.ui-th-column {
            /*border: 1px solid white;*/
            overflow: hidden;
            text-align: center;
            white-space: nowrap;
           // background: rgba(0, 102, 102, 0.32);
        background: rgb(214, 236, 234);
        box-shadow: 2px 2px 2px rgba(255, 255, 255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4)
            /*#327d78;*/
        }

        .ui-jqgrid .ui-jqgrid-caption 
        {
            font-size: 16px;
            color: red;
            background:rgba(221, 249, 249, 0.69); 
        }

        .ui-jqgrid .ui-pg-table td {
            //color: white;
        color:#327d78;
        }

        .ui-jqgrid .ui-pg-selbox {
            font-size: .8em;
            line-height: 18px;
            display: block;
            height: 18px;
            margin: 0;
            color: #327d78;
        }

        .ui-search-input, .ui-jqgrid .ui-pg-input {
            color: #327d78;
        }

        .ui-jqgrid-title {
            font-family: sans-serif;
            font-size: 13px;
            color: white;
        }

        .ui-widget.ui-widget-content {
            border: 1px solid #75adad;
        }

        .ui-row-ltr {
            height: 25px;
        }
         
        .ui-jqgrid-title {
    font-family: sans-serif;
    font-size: 15px;
    color: #327d78;
}
      ui-jqgrid .ui-jqgrid-caption {
   font-size: 14px;
    color: red;
    background: rgba(221, 249, 249, 0.69);
    padding: 2px 4px 2px 6px;
} 

        .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
            /*border: 1px solid white;*/
            /* background: #d7ebf9 url(images/ui-bg_glass_80_d7ebf9_1x400.png) 50% 50% repeat-x; */
            font-weight: bold;
           // color: #ffffff;
        //color:#0b7369;
           // background: rgba(0, 102, 102, 0.69);

        background:rgba(221, 249, 249, 0.69);
        color:#327d78;
        }

        .ui-pager-control {
           // background: #75adad;
           // background: #d5ecec;
        background:rgba(221, 249, 249, 0.69);
    box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4);
          
        }

        .ui-jqgrid .ui-jqgrid-pager .ui-pg-div {
   @* padding: 1px 0;
    float: left;
    position: relative;*@
    box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4);
    border-radius: 50%;
}
        .glyphicon-pencil {
            color: #327d78;
            cursor: pointer;
            font-size: 15px;
        }

        .glyphicon-file {
            color: #327d78;
            cursor: pointer;
            font-size: 15px;
        }


        .ui-jqgrid-titlebar, .ui-jqgrid-titlebar-close {
@*            display: none;*@
        }

        .clearsearchclass {
            display: none !important;
        }
        .ui-jqgrid .ui-jqgrid-titlebar-close span 
        {
          display: none;
          margin: 1px;
         }
      

        #IdDivTodayActivity {
            height: 300px;
            width: auto;
        }

        .ui-widget-header {
            border: 1px solid #75adad;
            /* background: #35414f url(images/ui-bg_dots-small_35_35414f_2x2.png) 50% 50% repeat; */
            color: #006666;
            font-weight: bold;
            font-size: 14px;
        }

        .ui-datepicker table {
            width: 100%;
            font-size: .9em;
            border-collapse: collapse;
            margin: 0 0 .4em;
        }

        .ui-state-disabled, .ui-widget-content .ui-state-disabled, .ui-widget-header .ui-state-disabled {
            opacity: 0.7;
            background-image: none;
        }
        li.ui-timepicker-selected, .ui-timepicker-list li:hover, .ui-timepicker-list .ui-timepicker-selected:hover {
            background: #006666;
            color: #fff;
        }
                .ui-timepicker-list li {
            padding: 3px 0 3px 5px;
            cursor: pointer;
            white-space: nowrap;
            color: #e4562b;
            list-style: none;
            margin: 0;
            font-size: 13px;
            font-weight: 500;
        }
        .ui-timepicker-wrapper {
            width: 7em;
        }
        /*===========================================================================*/


        .nav-tabs > li > a {
            margin-right: 2px;
            line-height: 1.42857143;
            border: 1px solid transparent;
            border-radius: 4px 4px 0 0;
            background-color: #327d78;
            color: #041e42;
            font-weight: bold;
        }

        #myNavbar ul > li {
          #myNavbar ul > li {
    color: white;
    font-family: sans-serif;
    text-align: center;
    /* width: 130px; */
    font-size: 12px;
    font-weight: bold;
    width: 126px;
    margin-left: 0px;
}
        }

        .navbar-default .navbar-nav > li > a:hover {
            color: #fff;
            background-color: #e05314;
        }

        @*//=============================================//*@

        .sweet-alert h2 {
    color: #575757;
    font-size: 12px;
    text-align: center;
    font-weight: 600;
    text-transform: none;
    position: relative;
    margin: 3px 0;
    padding: 0;
    line-height: 40px;
    display: block;
}

        .sweet-alert {

    background-color: #FDFFFF;
    font-family: 'Open Sans','Helvetica Neue',Helvetica,Arial,sans-serif;
    font-family: Cambria;
    width: 425px;
    padding: 17px;
    border-radius: 5px;
    text-align: center;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -256px;
    margin-top: -200px;
    overflow: hidden;
    display: none;
    z-index: 99999;
    height: 210px;
    font-size: 10px !important;
}
          .sweet-alert button.cancel {
            background-color: #42aba4 !important;
            width: 80px;
        
        }

        .sweet-alert button.confirm {
            background-color: #e4562b !important;
            width: 80px;
        }

        .sweet-alert .sa-icon {

    width: 69px;
    height: 70px;
    border: 4px solid gray;
        border-top-color: gray;
        border-right-color: gray;
        border-bottom-color: gray;
        border-left-color: gray;
    -webkit-border-radius: 40px;
    border-radius: 50%;
    margin: -1px auto;
    padding: 6px;
    position: relative;
    box-sizing: content-box;

}

        .sweet-alert .sa-icon.sa-error .sa-line {
    position: absolute;
    height: 5px;
    width: 36px;
    background-color: #F27474;
    display: block;
    top: 32px;
    border-radius: 2px;
}

        .sweet-alert button 
        {   
    font-size: 17px;
    font-weight: 500;   
    padding: 4px 21px;
    margin: 0px 5px 0;
    cursor: pointer;
   @* width: 64px;*@
    height: 32px;
}
        .ui-jqgrid .ui-jqgrid-view input, .ui-jqgrid .ui-jqgrid-view select, .ui-jqgrid .ui-jqgrid-view textarea, .ui-jqgrid .ui-jqgrid-view button {
    font-size: 11px;
    @*border: solid green 1px;*@
}

       .ui-jqgrid-view input:focus{ border: solid green 2px;}
         .ui-jqgrid-view select:focus{border: solid green 2px;}
        .ui-jqgrid-view textarea:focus{border: solid green 1px;}
    @*    input:focus { 
    background-color: yellow;
}*@
.panel-heading[data-toggle="collapse"]:after {
    font-family: 'Glyphicons Halflings';
    content: "\e113";
    position: absolute;
    color: #229877;
    font-size: 12px;
    /* line-height: 0px; */
    right: 30px;
    /* top: calc(52% - 11px); */
    margin-top: -16px;
}
.ui-jqgrid .ui-jqgrid-view input, .ui-jqgrid .ui-jqgrid-view select, .ui-jqgrid .ui-jqgrid-view textarea, .ui-jqgrid .ui-jqgrid-view button {
    font-size: 11px;
    /* border: solid green 1px; */
}
        .modal-header .close {
    margin-top: 0px;
    color: #416965;
    opacity: 1;
}
=====================================================

    </style>
</head>
<body class="container-fluid" style="font-family: Helvetica Neue,Helvetica,Arial,sans-serif">


    <header id="IDHeaderLogo" class="row container-fluid">
        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
            <div class="row">
                @*<br />*@
                @*<br />*@
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12" style="margin-top: 10px;">
                    <img src="~/Assets/Images/Logo/Metso_logo.jpg" id="IdLogo" />
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
            <div class="text-md-center" style="font-size: 24px; text-align: center; color: #006363; font-weight: bold; margin-top: 10px;">Installation & Commissioning System</div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12">
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <p class="pull-right">Welcome <span id="spn_name" style="font-weight: bold">@ViewBag.name</span>&nbsp; | &nbsp;<span id="logout" title="Logout" style="font-weight: bolder; color: #e4562b;">Logout</span></p>
            @*   <p class="pull-right">Welcome <span id="spn_name" style="font-weight: bold">@ViewBag.name</span>&nbsp;</p>*@
                     </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 ">
                    @*<p id="IdPHeaderQuote" class="pull-right" style="font-weight: bolder; font-size: 15px;">Change Password </p>*@
                    @*<img class="pull-right" style="height: 25px; width: 15%; cursor: pointer" title="Change Password " src="~/Assets/Images/PasswordPicture1.png" id="IdImgChangePassword" />*@
                </div>
            </div>
        </div>
        @* <div class="row">
                  <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                      Change Password
                  </div>
            </div>*@
    </header>
    <div class="row">
        <div class="col-sm-12 col-md-12 col-lg-12 col-xs-12">
            <nav class="navbar navbar-default">
                <div class="container-fluid" style="border-bottom: solid #327d78 1px;">
                    <div class="navbar-header" id="IDDivNavbarHeader">
                        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        <a class="navbar-brand" href="#myPage">Menu</a>
                    </div>
                    <div class="collapse navbar-collapse" id="myNavbar" style="margin-bottom: 5px;">
                        <ul class="nav navbar-nav navbar-Left nav-tabs clsMainMenu">
                            <li><a href="#" id="IdaProjects">Projects</a></li>
                           @* <li><a href="#" id="IdaCheckSheets">Check Sheets</a></li>*@
                            <li><a href="#" id="IdaCheckSheets">Progress Sheet</a></li>
                            <li style="width: 196px;"><a href="#" id="IdaInstallationProtocalls">Installation Protocols</a></li>
                            <li><a href="#" id="IdaHydraAvailability">Hydra Availability</a></li>
                            <li><a href="#" id="IdaIdleReport">Idle Report</a></li>
                            <li><a href="#" id="IdaCommissioning">Commissioning</a></li>
                            @* <li style="width: 166px;"><a href="#" id="IdaShortSupplies">Short Supplies</a></li>*@
                            <li><a href="#" id="IdaSettings">Settings</a></li>
                            <li><a href="#" id="IdaReports">DashBoard</a></li>
                            <li><a href="#" id="IDArchive">Archive</a></li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12" id="IdDivision" style="padding: 0px;">
            <img src="~/Assets/Images/LeftArrow.png" id="IdImgLeftArrow" class="pull-right" style="height: 40px; width: 40px" />
            <br />
            <br />
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" id="IdToAppend"></div>
            <br />
            <br />
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div id="IdDivAccordian" style="font-size: 12px;">
                </div>
            </div>
        </div>
        <div id="IdDivContent" class="col-lg-10 col-md-10 col-sm-10 col-xs-12">
            <img src="~/Assets/Images/RightArrow.png" id="IdImgRightArrow" style="height: 40px; width: 40px; margin-top: -40px; display: none" />
            <div class="MainContainerBody">
            </div>
        </div>
    </div>

    <div class="modal fade ClsDivModelView" id="IdDivModelChangePassword" role="dialog" style="position: absolute !important;">
        <div class="modal-dialog modal-sm" style="margin-top: 160px;">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close closeProject" id="IdbtnCloseArrow">&times;</button>
                    <button type="button" class="Colsemodal" style="display: none" data-dismiss="modal"></button>
                    <h4 class="modal-title">Change Password</h4>
                </div>
                <div id="IDDivSheetModalBody" class="modal-body">
                    <div id="IdDivCheckSheetDetailForm">
                        <br />
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <label for="IdTxtContractorName" class="ClsLabelCustTxtBox ">Old Password</label><sup style="color:red">*</sup>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="input-group">
                                    <input id="IdTxtOldPassword" type="Password" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" autofocus="autofocus" />
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <label for="IdTxtContractorName" class="ClsLabelCustTxtBox ">New Password</label><sup style="color:red">*</sup>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="input-group">
                                    <input id="IdTxtNewPassword" type="Password" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" />
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <label for="IdTxtContractorName" class="ClsLabelCustTxtBox ">Confirm Password</label><sup style="color:red">*</sup>
                            </div>
                            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                                <div class="input-group">
                                    <input id="IdTxtConfirmPassword" type="text" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <br />
                    <div class="modal-footer">
                        <button type="button" id="IdBtnSaveChangePassword" class="btn ButtonStyle ClsEnquiryFrombtn">Save</button>
                        <button type="button" id="btnCLoseEnqForm" class="btn ButtonStyle ClsEnquiryFrombtn closeCheckSheet">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>




</body>
</html>

<script>

    function AbsolutePath(url) {
        var path = '@Request.ApplicationPath';
        if (path == '/')
            return url;
        else
            return path + url;
    }

    function AlertMessage(msg, msgType) {
        swal({
            title: msg,
            type: msgType,
            showCancelButton: false,
            confirmButtonText: "OK",
            closeOnConfirm: true,
            confirmButtonColor: "#e6592e"

        },
        function () {
            window.onkeydown = null;
            window.onfocus = null;
        });
    };

    var newbuttonValue;

    $(document).ready(function () {


        $.ajax({
            url: AbsolutePath('/Home/GetUpdatingArchive'),
            type: 'GET',
            cache: false,
            async: false,
            success: function (resp) {
                console.log(resp)
                //palttypearr = resp
            }
        })

        $("#IdImgLeftArrow").click(function () {

            $("#IdDivision").css({ "-webkit-animation": "ChatHide 0.5s backwards", "position": "fixed" });
            $("#IdDivision").css("-moz-animation", "ChatHide 0.5s backwards");
            $("#IdDivision").css("-animation", "ChatHide 0.5s backwards");
            $("#IdDivision").css("opacity", "0");

            $("#IdDivContent").removeClass("col-lg-10");
            $("#IdDivContent").removeClass("col-md-10");
            $("#IdDivContent").removeClass("col-sm-10");

            $("#IdDivContent").addClass("col-lg-12 pull-left");
            $("#IdDivContent").addClass("col-md-12 pull-left");
            $("#IdDivContent").addClass("col-sm-12 pull-left");
            $("#IdImgRightArrow").show();
        });

        $("#IdImgRightArrow").click(function () {
            $("#IdDivision").css({ "-webkit-animation": "ChatAnimation1 0.5s forwards", "position": "relative" });
            $("#IdDivision").css("-moz-animation", "ChatAnimation1 0.5s forwards");
            $("#IdDivision").css("-animation", "ChatAnimation1 0.5s forwards");
            $("#IdDivision").css("opacity", "1");

            $("#IdDivContent").removeClass("col-lg-12");
            $("#IdDivContent").removeClass("col-md-12");
            $("#IdDivContent").removeClass("col-sm-12");

            $("#IdDivContent").addClass("col-lg-10 pull-left");
            $("#IdDivContent").addClass("col-md-10 pull-left");
            $("#IdDivContent").addClass("col-sm-10 pull-left");
            $("#IdDivContent").css("margin-left", "0px");
            $(this).hide();
            $(this).css("display", "none");
        });
        //==========================================================================================//

        function menuInActive() {
            for (var i = 0; i < $("#myNavbar ul li a").length; i++) {
                $($("#myNavbar ul li a")[i]).removeClass("menuActive");
            }
        }

        $("#myNavbar ul li").click(function () {
            $("#myNavbar ul li").css("border-top-right-radius", "0px").css("border-top-left-radius", "0px").css("background-color", "gray");;
            $("#myNavbar ul li").css("border-top", "solid white 2px").css("border-left", "solid white 2px").css("border-right", "solid white 2px").css("border-bottom", "solid silver 0px").css("padding-right", "0px");
            $(this).css("border-bottom-left-radius", "0px").css("border-bottom-right-radius", "0px")/*.css("margin-left","2px");*/
            $(this).css("border-top", "solid silver 5px").css("border-left", "solid silver 2px").css("border-right", "solid silver 2px").css("padding-right", "2px").css("padding-left", "2px").css("border-bottom", "solid silver 0px").css("padding-right", "0px");
            $(this).css("border-top-right-radius", "10px").css("border-top-left-radius", "10px").css("border-bottom", "solid white 3px").css("background-color", "white");
        });

        $("a").click(function () {
            menuInActive();
            $(this).addClass("menuActive");
        })

        //==========================================================================================//

        if ('@Session["Designation"].ToString()' == "SICRIE" || '@Session["Designation"].ToString()' == "43")
        {

            $("#IdaSettings").hide();
            $("#IDArchive").hide()

        }
        else
        {
            $("#IdaSettings").show();
            $("#IDArchive").show()

        }

        if ('@Session["Designation"].ToString()' == "67" )
        {
            $("#IdaIdleReport").hide();
            $("#IdaSettings").hide();
            $("#IdaCheckSheets").hide();
            $("#IdaHydraAvailability").hide();
            $("#IdaReports").hide();            
        }
        else
        {
            $("#IdaIdleReport").show();
            $("#IdaSettings").show();
            $("#IdaCheckSheets").show();
            $("#IdaHydraAvailability").show();
            $("#IdaReports").show();          
        }




        $(document).on('click', '#IdaProjects', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/Projects"));
        })

        $(document).on('click', '#IDArchive', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/Archive"));
        })

        $(document).on('click', '#IdaCheckSheets', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/CheckSheets"));
        })

        $(document).on('click', '#IdaHydraAvailability', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/HydraAvailability"));
        })


        $(document).on('click', '#IdaIdleReport', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/IdleReport"));
        })

        $(document).on('click', '#IdaCommissioning', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning"));
        })

        $(document).on('click', '#IdaInstallationProtocalls', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/InstallationProtocalls"));
        })

        $(document).on('click', '#IdaSettings', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/Settings"));
        })

        $(document).on('click', '#IdaReports', function () {
            $(this).addClass("menuActive");
            $(".MainContainerBody").load(AbsolutePath("/Home/Reports"));
        })

        $("#IdaProjects").trigger("click")

        //================================================================================================================//

        $(document).on('click', ".closeProject,.closeIdleRepor,.closeHydraAvailability,.closeCommissionSheet,.closeCheckSheet", function () {
            swal({
                buttons:
                {
                    confirm: true,
                    cancel: true,
                },
                title: "Are you sure, you want to close?",
                type: "warning",
                confirmButtonText: "No",
                confirmButtonColor: "#DD6B55",
                showCancelButton: true,
                cancelButtonText: "Yes", closeOnConfirm: true, closeOnCancel: true
            },
                function (isConfirm) {
                    window.onkeydown = null;
                    window.onfocus = null;
                    if (!isConfirm) {
                        $(".Colsemodal").trigger("click");
                        if (newbuttonValue == 1) {
                            NewButton();
                        }
                    }
                    else {
                    }
                });
        });

        //==================================================================================================================//

        var sessiontime = '@Session.Timeout';
        setTimeout(MSGBox, (sessiontime * 60000) - 1)

        function MSGBox() {
            window.location.href = AbsolutePath("/Home/SessionPageLogin");
        }

        $("#logout").click(function ()
        {

            swal({
                buttons:
                {
                    confirm: true,
                    cancel: true,
                },
                title: "Are you sure, you want to logout?",
                type: "warning",
                confirmButtonText: "No",
                confirmButtonColor: "#DD6B55",
                showCancelButton: true,
                cancelButtonText: "Yes", closeOnConfirm: true, closeOnCancel: true
            },
                function (isConfirm)
                {              
                    window.onkeydown = null;
                    window.onfocus = null;
                    if (!isConfirm) {
                        window.location.href = AbsolutePath("/Home/SignOut");

                    }
                    else {
                        //Ashwini
                    }
                });
        })

        $(document).on("click", "#IdImgChangePassword", function ()
        {
            $("#IdTxtOldPassword").val("");
            $("#IdTxtNewPassword").val("");
            $("#IdTxtConfirmPassword").val("");

            $("#IdDivModelChangePassword").modal({ backdrop: false });

        })

        $(document).on("click", "#IdBtnSaveChangePassword", function ()
        {           
            if ($("#IdTxtOldPassword").val() != "" && $("#IdTxtNewPassword").val() != "" && $("#IdTxtConfirmPassword") != "")
            {
                if ('@Session["Password"].ToString()' == $("#IdTxtOldPassword").val())
                {

                    if ($("#IdTxtNewPassword").val() == $("#IdTxtConfirmPassword").val())
                    {
                        var PasswordInfo = [];
                        var PasswordInfolist =
                            {
                                EmployeeID: '@Session["EmployeeID"].ToString()',
                                OldPassword: $("#IdTxtOldPassword").val(),
                                NewPassword: $("#IdTxtNewPassword").val(),
                                ConfirmPassword: $("#IdTxtConfirmPassword").val()
                            }
                        PasswordInfo.push(PasswordInfolist);
                        console.log(PasswordInfo);
                        $.ajax({
                            url: AbsolutePath("/Home/ChangePassowerd"),
                            type: 'POST',
                            datatype: "JSON",
                            data:
                                {
                                    "PasswordInfo": JSON.stringify(PasswordInfo),
                                },
                            success: function (resp)
                            {
                                AlertMessage("Records Saved Successfully", "success")
                                $("#IdDivModelChangePassword").modal("hide");
                            },
                            error: function (resp)
                            {
                                AlertMessage("File Not Saved", "warning")

                            },
                        });

                    }
                    else
                    {
                        AlertMessage("Miss match in New & Confirm Password", "warning")
                    }

                }
                else
                {
                    AlertMessage("Old Password is Incorrect", "warning")
                }
            }
            else
            {
                AlertMessage("Please fill all required fields", "warning")
              
            }
        })
    });
</script>

