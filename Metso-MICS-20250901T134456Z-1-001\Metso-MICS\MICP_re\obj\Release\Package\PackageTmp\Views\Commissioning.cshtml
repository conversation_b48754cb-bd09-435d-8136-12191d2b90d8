﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Commissioning</title>


    <style>    
        @@media only screen and (min-width: 1280px) 
              {
            #IdDivModelCommissionSheet {
                height:500px;
            }
        } 
              
              @@media only screen and (max-width: 1275px) {
            #IdDivModelCommissionSheet {
                height:500px;
            }
        } 
    </style>

</head>
<body>
    <div style="width: inherit; overflow: auto">
    <table id="projectTblGrid"></table>
    <div id="projectDivPager"></div>
</div>

    @*//==============================================================================================//*@

     <div class="modal fade ClsDivModelView" id="IdDivModelCommissionSheet" role="dialog" style="position: absolute !important; height: 800px;overflow-y: scroll;">
                    <div class="modal-dialog modal-lg" style="width: 97%">
                        <!-- Modal content-->
                        <div class="modal-content">

                            <div class="modal-header">
                                <button type="button" class="close closeCommissionSheet" id="btnCLoseX" >&times;</button>
                                 <button type="button" class="Colsemodal" style="display:none" data-dismiss="modal"></button>
                                <h4 class="modal-title">Commissioning Details</h4>


                            </div>
                            <div id="IDDivCustModalBody" class="modal-body">
                                <div id="IdDivCheckSheetForm">
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdProjectcode" class="ClsLabelCustTxtBox ">Project Code</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">                                       
                                                <input id="IdProjectcode" type="text" class="form-control input-sm ClsTxtQuotaionHeader titlevalue" readonly="readonly" autofocus />                                             
                                        </div>
                                          <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdProjectNameNew" class="ClsLabelCustTxtBox ">Project Name</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">                                       
                                                <input id="IdProjectNameNew" type="text" class="form-control input-sm ClsTxtQuotaionHeader titlevalue" readonly="readonly" autofocus />                                             
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdProjectName" readonly="readonly" class="ClsLabelCustTxtBox ">Plant type</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdProjectName" readonly="readonly" type="text" class="form-control input-sm ClsCheckSheetDetails titlevalue"  readonly="readonly"/>
                                        </div>
                                       
                                    </div>
                                    <br />
                                    <div class="row">
                                         <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="TxtClientName" class="ClsLabelCustTxtBox ">Client</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="TxtClientName" type="text" readonly="readonly" value="" class="form-control input-sm ClsCheckSheetDetails titlevalue" readonly="readonly" />

                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtState" class="ClsLabelCustTxtBox ">State</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtState" readonly="readonly" type="text" class="form-control input-sm ClsCheckSheetDetails titlevalue" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtLoation" class="ClsLabelCustTxtBox ">Location</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtLoation" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader titlevalue" />
                                        </div>                                      
                                    </div>
                                    <br />
                                    <div class="row">
                                          <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtStatus" class="ClsLabelCustTxtBox ">Status</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtStatus" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader titlevalue" />
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <div class="row">
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="background-color: #8bcaca; color:#327d78; width: 97%; margin-left: 13px; font-weight:bolder">
                                        Commissioning Details
                                    </div>
                                </div>
                                <br />

                                <div class="row">
                                    @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>*@
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                        <div style="width: inherit; overflow: auto;" id="IdDivGridCheckSheet">
                                            <table id='IdTblCommissioningGrid'></table>
                                            <div id='CheckCommissioningPager'></div>
                                        </div>
                                    </div>
                                </div>

                                @* ---Main Cheak Sheet--- *@
                                @* <div id="IDCheakSheetAccordian">

                    
                    <div class="panel-primary" id="div_ProjectAccoridn">
                        <div class="panel panel-heading clickable" data-toggle="collapse" style='height: 30px; background-color: #006666;' data-parent="#accordion" data-target="#IDProjectDetails">
                            <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: 500; color: white;    margin-top: -4px;">Cheak Sheet Details</h4>
                            <span class="pull-right"><i class="glyphicon glyphicon-chevron-down"></i></span>
                        </div>

                        <div style="width: inherit; overflow: auto;" id="IdDivGridCheckSheet">
                            <table id='IdTblCheckSheetGrid'></table>
                            <div id='CheckSheetDetailsPager'></div>
                        </div>
                    </div>
                </div>*@
                            </div>
                            <div class="modal-footer">
                                @*<button type="button" id="IdBtnCommissioningSave" class="btn ButtonStyle ClsEnquiryFrombtn">Save</button>
                                <button type="button" id="IdBtnCommissioningReset" class="btn ButtonStyle ClsEnquiryFrombtn">Reset</button>
                                <button type="button" id="IdBtnCommissioningCancel" class="btn ButtonStyle ClsEnquiryFrombtn closeCommissionSheet" >Cancel</button>*@
                                <button type="button" id="IdBtnCommissioningExport" class="btn ButtonStyle ClsEnquiryFrombtn">Export</button>
                                <button type="button" id="IdBtnCommissioningReset" class="btn ButtonStyle ClsEnquiryFrombtn closeCommissionSheet">Close</button>
                               
                            </div>
                        </div>
                    </div>
                </div>

    @*//==============================================================================================//*@

     <div class="modal fade ClsDivModelView" id="IdDivModelCommissioningAddDetail" role="dialog" style="position: absolute !important;">
                    <div class="modal-dialog modal-lg" style="width: 97%">
                        <!-- Modal content-->
                        <div class="modal-content">

                            <div class="modal-header">
                                <button type="button" class="close" id="IdbtnCloseArrow">&times;</button>
                                <h4 class="modal-title">Cheak Sheet Details</h4>


                            </div>
                            <div id="IDDivSheetModalBody" class="modal-body">
                                <div id="IdDivCheckSheetDetailForm">
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtEnquiryNum" class="ClsLabelCustTxtBox ">Description</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <div class="input-group">
                                                <input id="IdTxtDescription" type="text" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" autofocus="autofocus" />

                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtEnquiryDate" class="ClsLabelCustTxtBox ">Civil Provided</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtCivilProvided" type="text" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="TxtCreatedby" class="ClsLabelCustTxtBox ">Supply Status</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectSupplyStatus" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>

                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtCreatedDate" class="ClsLabelCustTxtBox ">Ground Work</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectGroundWork" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>

                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtQuotationNo" class="ClsLabelCustTxtBox ">Erection</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectErection" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtOfferNo" class="ClsLabelCustTxtBox ">Electrical Work</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectElectricalWork" class="form-control" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtCreatedDate" class="ClsLabelCustTxtBox ">Constraint</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectConstraint" class="form-control titlevalue" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>

                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtCreatedDate" class="ClsLabelCustTxtBox ">Status</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <select id="IdDdlSelectStatus" class="form-control titlevalue" style="margin-right: 10px">
                                                <option value="0">--Select--</option>
                                                <option value="1">Complete</option>
                                                <option value="2">In-Complete</option>
                                                <option value="3">Not Received</option>
                                                <option value="4">Not Applicable</option>

                                            </select>

                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtQuotationNo" class="ClsLabelCustTxtBox ">Remarks</label>
                                        </div>
                                        <div class="col-sm-10">
                                            <textarea class="form-control titlevalue" id="IdTxtEnqRemarks"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <br />
                                <br />


                            </div>
                            <div class="modal-footer">
                                <button type="button" id="txtSaveEnqDetails" class="btn ButtonStyle ClsEnquiryFrombtn">Save</button>
                                <button type="button" id="btnGenerateQtnDetails" class="btn ButtonStyle ClsEnquiryFrombtn" data-dismiss="modal">Reset</button>
                                <button type="button" id="btnCLoseEnqForm" class="btn ButtonStyle ClsEnquiryFrombtn">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>

</body>
</html>
<script>    
    $("#IdTxtCivilProvided").datepicker();

    if ('@ViewBag.CommissioningProjectCode' == '0')
    {
        $("#IdToAppend").html("");

        //function NewButton() {
        //    $("#IdToAppend").html("");
        //    $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        //    // $("#IdToAppend").append("<Button id='IdBtnNewIdleReport' class='AddNew'>New</Button>")
        //}
        //NewButton();

        $(document).on('click', '#IdBtnNewIdleReport', function () {
            $("#IdDivModelCommissionSheet").modal({ backdrop: false });
        });
        newbuttonValue = 1;
    }
  
    if ('@ViewBag.CommissioningProjectCode' != '0') {
        newbuttonValue = 0;
    }

    function LeftMenuInactive() {
        for (var i = 0; i < $("#IdLeftMenu li").length; i++) {
            $($("#IdLeftMenu li ")[i]).removeClass("menuActive");
        }
    }

    $(".titlevalue ").mouseenter(function () {
        var word = $(this).val();

        $(".titlevalue ").attr('title', word);
    });
    //============================================= Commissioning ==========================================//

    function CommissioningLeftMenu() {
        $("#IdToAppend").html("");
        $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        $("#IdToAppend").append("<ul id='IdLeftMenu'></ul>")
        $("#IdLeftMenu").css("margin-left", "-30px");
        $("#IdLeftMenu").append("<li id='IdCommissionCommissioning'>Commissioning</li>")
        $("#IdLeftMenu").append("<li id='IdCommissionProject'>Project</li>")
        $("#IdLeftMenu").append("<li id='IdCommissionCheckSheet'>Progress Sheet</li>")
        $("#IdLeftMenu").append("<li id='IdCommissionInstallationProtocalls'>Installation Protocols</li>")
        $("#IdLeftMenu").append("<li id='IdCommissionHydraAvailability'>Hydra Availability</li>")
        $("#IdLeftMenu").append("<li id='IdCommissionIdleReport'>Idle Report</li>")
       // $("#IdLeftMenu").append("<li id='IdCommissionShortSupplies'>Short Supplies</li>")

    }

    $(document).on('click', '#IdCommissionCommissioning', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + 0));
        newbuttonValue = 1;
    })

    $(document).on('click', '#IdCommissionCheckSheet', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/CheckSheets?ProjectId=" +  ProjectID));
    })

    $(document).on('click', '#IdCommissionProject', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Projects?ProjectId=" +  ProjectID));
    })

    $(document).on('click', '#IdCommissionHydraAvailability', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/HydraAvailability?ProjectId=" +  ProjectID));
    })

    $(document).on('click', '#IdCommissionIdleReport', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/IdleReport?ProjectId=" +  ProjectID));
    })

    $(document).on('click', '#IdCommissionInstallationProtocalls', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/InstallationProtocalls?ProjectId=" +  ProjectID));
    })

    //$(document).on('click', '#IdCommissionShortSupplies', function () {
    //    $("#IdDivModelViewCust").modal("hide");
    //    LeftMenuInactive()
    //    $(this).addClass("menuActive")
    //    $(".MainContainerBody").html("");
    //    $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + 1));
    //})




    //========================================================================================================//
    function EditCommissioningDetails(Id) {
        if ('@ViewBag.CommissioningProjectCode' == '0') {
            CommissioningLeftMenu();
        }
        //$("#IdDivModelViewHydraAvailability").modal({ backdrop: false })
        //var PDID = $("#projectTblGrid tr").eq(Id).children().eq(2).text()
        //ProjectID = PDID
        //var ProjectCode = $("#projectTblGrid tr").eq(Id).children().eq(3).text()
        //var Client = $("#projectTblGrid tr").eq(Id).children().eq(4).text()
        //var State = $("#projectTblGrid tr").eq(Id).children().eq(5).text()
        //var Location = $("#projectTblGrid tr").eq(Id).children().eq(6).text()
        //var ProjectType = $("#projectTblGrid tr").eq(Id).children().eq(7).text()

        //var Status = $("#projectTblGrid tr").eq(Id).children().eq(8).text()
        //var Created = $("#projectTblGrid tr").eq(Id).children().eq(9).text()
        //var Completed = $("#projectTblGrid tr").eq(Id).children().eq(10).text()
        //$("#IdProjectcode").val(ProjectCode)
        //$("#IdProjectName").val(ProjectType)
        //$("#TxtClientName").val(Client)
        //$("#IdTxtState").val(State)
        //$("#IdTxtLoation").val(Location)
        //$("#IdTxtStatus").val(Status)
        ////$("#IdTxtStatus").val(5)
        ////$("#IdTxtNotAvailable").val(5)
        ////MainLandingGridForHydraAvialabilityHistoryNew(PDID)
        //$.CheckSheetLandingGrid(ProjectID);
        //$("#IdDivModelCommissionSheet").modal({ backdrop: false });
    };

    $(document).on('click', ".editclassCom", function () {
        debugger
        // $(this).parent().next().next().css("border","solid red 2px")

        var PDID = $(this).parent().next().text()
        ProjectID = PDID
        //$("#IdDivModelViewIdleReport").modal({ backdrop: false });
        //$("#IdDivModelViewHydraAvailability").modal({ backdrop: false });

        var ProjectCode = $(this).parent().next().next().text()
        var ProjectName = $(this).parent().next().next().next().next().text()
        var Client = $(this).parent().next().next().next().text()
        var State = $(this).parent().next().next().next().next().text()
        var Location = $(this).parent().next().next().next().next().next().text()
        var ProjectType = $(this).parent().next().next().next().next().next().next().text()

        var Status = $(this).parent().next().next().next().next().next().next().next().text()
        var Created = $(this).parent().next().next().next().next().next().next().next().next().text()
        var Completed = $(this).parent().next().next().next().next().next().next().next().next().next().text()
        var Pname = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().text()
        //var Employename = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().text()
        //var Avaiable = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().text()
        //var notAvailiable = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().next().text()
        $("#IdProjectcode").val(ProjectCode)
        $("#IdProjectName").val(ProjectType)
        $("#TxtClientName").val(Client)
        $("#IdTxtState").val(State)
        $("#IdTxtLoation").val(Location)
        $("#IdTxtStatus").val(Status)
        $("#IdProjectNameNew").val(Pname)
        
        //$("#IdTxtAvailable").val(Avaiable)
        //$("#IdTxtNotAvailable").val(notAvailiable)
        $.CheckSheetLandingGrid(ProjectID);
        $("#IdDivModelCommissionSheet").modal({ backdrop: false });
    })


    $(document).on('click','#IdBtnCommissioningExport',function ()
    {
        window.location.href = AbsolutePath("/Commissionning/ExportToexcelCommissionDetails?ProjectId=" + ProjectID)
    })
    function ViewCommissioningDetails(Id) {
        if ('@ViewBag.CommissioningProjectCode' == '0')
        {
            CommissioningLeftMenu();
        }
        $("#IdDivModelCommissionSheet").modal({ backdrop: false });
    }


    //===================================================================================================//
    $.ProjectLandingGrid = function (ProjectID) {

        //var LoadStaticDataMainGrid = [
        //    { "ActivityHeaderIC": "1", "ProjectCode": "C.005867 & C.005869", "Client": "Mr. K K Singh", "State": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "NW-Rapid", "Status": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
        //    { "ActivityHeaderIC": "2", "ProjectCode": "C.005868 & C.005900", "Client": "Mr. Praveen", "State": "Karnataka", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "Modular", "Status": "InProgress", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
        //    { "ActivityHeaderIC": "3", "ProjectCode": "C.005869 & C.005901", "Client": "Mr. Suresh", "State": "Tamilnadu", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "Modular", "Status": "Completed", "CreatedDate": "11-09-2018", "CompletedDate": "18-09-2018" },
        //    { "ActivityHeaderIC": "4", "ProjectCode": "C.005870 & C.005902", "Client": "Mr. Mahesh", "State": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "ProjectType": "NW-Rapid", "Status": "InProgress", "Created Date": "11-09-2018", "CompletedDate": "18-09-2018" }];

        var LoadStaticDataMainGrid = [
{ "ProjectDetailsId": "1", "Project_code": "C.005867 & C.005869", "Site_Client_Name": "Mr. K K Singh", "State_Name": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "NW-Rapid", "ProjectStatusName": "Completed", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
{ "ProjectDetailsId": "2", "Project_code": "C.005868 & C.005900", "Site_Client_Name": "Mr. Praveen", "State_Name": "Karnataka", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "Modular", "ProjectStatusName": "InProgress", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
{ "ProjectDetailsId": "3", "Project_code": "C.005869 & C.005901", "Site_Client_Name": "Mr. Suresh", "State_Name": "Tamilnadu", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "Modular", "ProjectStatusName": "Completed", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
{ "ProjectDetailsId": "4", "Project_code": "C.005870 & C.005902", "Site_Client_Name": "Mr. Mahesh", "State_Name": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "NW-Rapid", "ProjectStatusName": "InProgress", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" }];

        $("#projectTblGrid").GridUnload();

        $("#projectTblGrid").jqGrid({

            //datatype: "local",
            //data: LoadStaticDataMainGrid,

            //colModel: [
            //        //{
            //        //    name: "Edit", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
            //        //        return "<i class='glyphicon glyphicon-pencil'  title='Click Here For Edit'  onclick='EditCommissioningDetails(" + b.rowId + ")' id='IdProject_EditImg" + b.rowId + "'></span>";
            //        //    }
            //        //},
            //        {
            //            name: "View", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
            //                return "<i class='glyphicon glyphicon-file' title='Click Here For View' data-toggle='modal' data-target='#IdEnquiryModal' onClick='ViewCommissioningDetails(" + b.rowId + ")' ></i>"
            //            }
            //        },

            //         { name: "ActivityHeaderIC", index: "ActivityHeaderIC", label: "Activity HeaderIC", editable: true, align: "left", hidden: true },

            //              { name: "ProjectCode", index: "ProjectCode", label: "Project Code", editable: true, align: "left", hidden: false },
            //              {
            //                  name: "Client", index: "Client", label: "Client", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true,
            //              },
            //              { name: "State", index: "State", label: "State", editable: true, align: "left", width: 110, resizable: true, sortable: true, search: true, searchable: true },
            //              { name: "Location", index: "Location", label: "Location", editable: true, align: "left", width: 130, resizable: true, sortable: true, search: true, searchable: true, },
            //              { name: "ProjectType", index: "ProjectType", label: "Project Type", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true },
            //              { name: "Status", index: "Status", label: "Status", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true, hidden: false },
            //              { name: "CreatedDate", index: "CreatedDate", label: "Created Date", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true },
            //              { name: "CompletedDate", index: "CompletedDate", label: "Completed Date", editable: true, align: "left", width: 120, resizable: true, sortable: true, search: true, searchable: true },

            //],
            colModel: [
              //{
              //    name: 'Edit', width: 60, align: 'center', formatter: function view(a, b) {
              //        return "<span class='glyphicon glyphicon-pencil ClsViewEnquiryDetails' id='editImg_" + b.rowId + "'></span>";
              //    }, search: false,
              //},

              {
                  name: "View", align: "center", width: 80, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                      return "<i class='glyphicon glyphicon-file editclassCom'  title='Click Here For View' onClick='EditCommissioningDetails(" + b.rowId + ")' ></i>"
                  }
              },
              { name: "ProjectDetailsId", index: "ProjectDetailsId", label: "ProjectDetailsId", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
              { name: "Project_code", index: "Project_code", label: "Project Code", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
              { name: "Site_Client_Name", index: "Site_Client_Name", label: "Client", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
              { name: "State_Name", index: "State_Name", label: "State", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
              { name: "Location", index: "Location", label: "Location", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
              { name: "Plant_typeName", index: "Plant_typeName", label: "Project Type", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
              { name: "ProjectStatusName", index: "ProjectStatusName", label: "Status", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false },
              { name: "Created_Date", index: "Created_Date", label: "Created Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
              { name: "ComissioingSubmittedDate", index: "CompletedDate", label: "Completed Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
                     { name: "CommissioningStatusName", index: "CommissioningStatusName", label: "CommissioningStatusName", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                  { name: "Company_Employee_Name", index: "Company_Employee_Name", label: "Company_Employee_Name", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
            { name: "ProjectName", index: "PN", label: "Project Name", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true }
            ],
            height: 'auto',
            width: 960,
            caption: "Commissioning Details",
            url: AbsolutePath("/Commissionning/ProjectInjfoForCommissioning?ProjectID=" + ProjectID),
            datatype: "json",
            mtype: "GET",
            sortname: "ProjectDetailsId",
            sortorder: "asc",
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
           // loadonce: false,
            rowNum: 20,
            rownumbers:true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager:"#projectDivPager",
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
        })
        $("#projectTblGrid").navGrid("#projectDivPager", { add: false, edit: false, del: false, refresh: false, search: false })
        $("#projectTblGrid").filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
        $("#projectTblGrid").navGrid("#projectDivPager", {}, {}, {}, {}, {
            multipleSearch: true,
            multipleGroup: true,
            showQuery: true
        });       
        $("#projectTblGrid").navButtonAdd("#projectDivPager",
           {
               title: "Refresh",
               buttonicon: "ui-icon-refresh",
               caption: "",
               position: "last",
               onClickButton: function () {
                   debugger
                   //$("#projectTblGrid").jqGrid("GridUnload");
                   $.ProjectLandingGrid(ProjectID);
               }
           });
        $("#projectTblGrid").navButtonAdd("#projectDivPager", {
            title: 'Export',
            caption: "",
            buttonicon: 'ui-icon-bookmark',
            onClickButton: function () {

                window.location.href = AbsolutePath("/Commissionning/ExportToexcel");
            }
        });

    }
    $.ProjectLandingGrid('@ViewBag.CommissioningProjectCode');
   
    //===================================================================================================//
    $.CheckSheetLandingGrid = function (Pdid) {

        var CheakSheetStaticData = [

            //--1
            { "ComissioningEngineer": "Vijay", "Date": "30/08/2018", "From": "7:00 AM", "To": "9:00 AM", "Hours": "2 hrs", "Activity": "Idle", "Equipment": " ", "Details": "Pulley Fitment is balanced due to incomplete supply", "Remarks": "" },

            { "ComissioningEngineer": "Raj", "Date": "30/08/2018", "From": "5:00 PM", "To": "6:00 PM", "Hours": "1 hrs", "Activity": "Trouble Shooting", "Equipment": "GP10745", "Details": "Pulley Fitment is balanced due to incomplete supply", "Remarks": "" },

            { "ComissioningEngineer": "Suraj ", "Date": "30/08/2018", "From": "12:00pm", "To": "1:00 Pm", "Hours": "1 hrs", "Activity": "Comissioning ", "Equipment": "CZ100", "Details": "Pulley Fitment is balanced due to incomplete supply", "Remarks": "" },


        ];

        //$("#IdDivGridCheckSheet").html("");

        //$("#IdDivGridCheckSheet").append("<table id='IdTblCommissioningGrid'></table>")
        //$("#IdDivGridCheckSheet").append("<div id='CheckCommissioningPager'></div >")

        $("#IdTblCommissioningGrid").GridUnload();
        $("#IdTblCommissioningGrid").jqGrid({
           // datatype: "local",
            // mtype: "GET",
            //  data: CheakSheetStaticData,
            url: AbsolutePath("/Commissionning/CommissioningDetails?ProjectID=" + Pdid),
            datatype: "json",
            mtype: "GET",
            height: 240,
            width: 1050,
            //caption: "Commissioning Details",
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            rownumbers: true,
            loadonce: true,
            rowNum: 20,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: "#CheckCommissioningPager",
            sortname: "Company_Employee_Name",
            sortorder: "asc",
            colModel: [
                    //{
                    //    name: "Edit", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                    //        return "<i class='glyphicon glyphicon-pencil'  title='Click Here For Edit'  onclick='$.EdditEnquiry(" + b.rowId + ")'>"
                    //    }
                    //},
                    //{
                    //    name: "View", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                    //        return "<i class='glyphicon glyphicon-file' title='Click Here For View' data-toggle='modal' data-target='#IdEnquiryModal' onclick='$.EnquiryView(" + b.rowId + ")'>"
                    //    }
                    //},
                          { name: "Company_Employee_Name",  label: "Engineer", editable: true, align: "left", hidden: false },
                          {
                              name: "Commissioning_Date", label: "Date", editable: true, align: "center", width: 130, resizable: true, sortable: true, search: true, searchable: true, formatter: "date", formatoptions: { srcformat: 'm/d/Y', newformat: 'd-M-Y' }
                          },
                          { name: "Commissioning_From",  label: "From", editable: true, align: "right", width: 110, resizable: true, sortable: true, search: true, searchable: true },
                          { name: "Commissioning_TO", label: "To", editable: true, align: "right", width: 130, resizable: true, sortable: true, search: true, searchable: true, },
                          { name: "Commissioning_Hours",  label: "Hours", editable: true, align: "right", width: 100, resizable: true, sortable: true, search: true, searchable: true },
                          { name: "Commission_ActivityName",  label: "Activity", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true, hidden: false },
                          { name: "EquipmentIC",  label: "Equipment", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true },
                            { name: "Commission_ErrorBaseName",  label: "Day Log", editable: true, align: "left", width: 120, resizable: true, sortable: true, search: true, searchable: true },
                          { name: "Details",  label: "Details", editable: true, align: "left", width: 100, resizable: true, sortable: true, search: true, searchable: true },
                        

            ],
            
            //loadcomplete: function (data) {
            //    $(".ui-jqgrid .ui-jqgrid-bdiv").css("overflow-x", "scroll")
            //    //$(".ui-jqgrid .ui-jqgrid-bdiv").css("overflow-y", "auto")
            //    //$(".ui-jqgrid .ui-jqgrid-bdiv").css("overflow", "hidden")
            //    console.log("grid Load Completed"); console.log(data);
            //},
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                console.log(data)
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
        })
        $("#IdTblCommissioningGrid").navGrid("#CheckCommissioningPager", { add: false, edit: false, del: false, refresh: false, search: false })
        $("#IdTblCommissioningGrid").filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
        $("#IdTblCommissioningGrid").navGrid("#CheckCommissioningPager", {}, {}, {}, {}, {
            multipleSearch: true,
            multipleGroup: true,
            showQuery: true
        });
        //$("#IdTblCommissioningGrid").navButtonAdd("#CheckCommissioningPager",
        //   {
        //       title: "add",
        //       buttonicon: "ui-icon-plus",
        //       caption: "",
        //       position: "first",
        //       onClickButton: function () {

        //           $("#IdDivModelCommissioningAddDetail").modal("show");

        //       }
        //   });

        $("#IdTblCommissioningGrid").navButtonAdd("#CheckCommissioningPager",
           {
               title: "Refresh",
               buttonicon: "ui-icon-refresh",
               caption: "",
               position: "last",
               onClickButton: function () {
                   $("#IdTblCommissioningGrid").jqGrid("GridUnload");
                   $.CheckSheetLandingGrid(Pdid);
               }
           });
        $("#IdTblCommissioningGrid").jqGrid('setGroupHeaders', {
            useColSpanStyle: false,
            groupHeaders: [
              { startColumnName: 'Commissioning_Date', numberOfColumns: 4, titleText: '<center>Commissioning Time</center>' },
              { startColumnName: 'Commission_ActivityName', numberOfColumns: 3, titleText: '<center>Time Activity Details</center>' },

            ]
        });  
    }

   
    
</script>
