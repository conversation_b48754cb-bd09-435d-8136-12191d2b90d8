﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>InstallationProtocol</title>
    <style>
        #DivImg1 {
            height: 300px;
            width: 100%;
        }

        #DivIm2 {
            height: 200px;
            width: 100%;
        }

        #List1 li {
            width: 175px;
            height: 35px;
            color: black;
            font-weight: bold;
            font-size: 11px;
            background: #d9efef;
            /*border: solid 1px #006666;*/
            margin-left: 3px;
            margin-top: 0px;
            padding: 4px -2px;
            /*border-radius: 45px 0px 0px 0px;*/
            box-shadow: 2px 2px 2px rgba(255, 255, 255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4);
        }

            #List1 li > a 
            {
                color: #327d78;
                width: 175px;
                height: 35px;
                /*border-radius: 45px 0px 0px 0px;*/
                font-size: 10px;
                font-weight: bold;
                padding: 4px -2px;
                box-shadow: 2px 2px 2px rgba(255, 255, 255, .4) inset, inset -2px -2px 2px rgba(0, 0, 0, .4);
            }

        
       

        a, a:active, a:focus 
        {
            outline: none;
        }

        #List1 li:first-child 
        {
            margin-left: 10px;
        }

        #List1 li:hover, #List1 li:active 
        {
            outline: medium none;
        }



        .nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
            color: white !important;
            background-color: #327d78;
            font-weight: bold;
            font-size: 13px;
        }

        #List1 li > a:hover {
            color: white !important;
            background-color: #327d78;
        }

        .Spanclass {
            top: 9px;
            position: absolute;
            left: 66px;
        }

        .SpanclassEI {
            top: 9px;
            position: absolute;
            left: 11px;
        }

        .SpanclassEC {
            top: 4px;
            position: absolute;
            left: -1px;
        }

        .SpanclassEPC {
            top: 3px;
            position: absolute;
            left: 0px;
        }

          /*@@media only screen and (min-width: 1280px) 
              {
            #IdDivModelCheckSheet {
                height:500px;
            }
        } 
              
              @@media only screen and (max-width: 1275px) {
            #IdDivModelCheckSheet {
                height:500px;
            }
        }*/ 

    </style>
</head>
<body>
    <div style="width: inherit; overflow: auto">
        <table id="projectTblGrid"></table>
        <div id="projectDivPager"></div>
    </div>
    <div class="modal fade ClsDivModelView" id="IdDivModelCheckSheet" role="dialog" style="position: absolute !important; overflow-y: scroll;">
        <div class="modal-dialog modal-lg" style="width: 97%">
            <!-- Modal content-->
            <div class="modal-content">

                <div class="modal-header">
                    <button type="button" class="close closeCheckSheet" id="btnCLoseX">&times;</button>
                    <button type="button" class="Colsemodal" style="display: none" data-dismiss="modal"></button>
                    <h4 class="modal-title">Installation Protocol Details</h4>
                </div>
                <div id="IDDivCustModalBody" class="modal-body">
                    <div class="panel-content" >
                        <div id="List">
                            <ul class="nav nav-pills" style="text-align: center;" id="List1">
                                <li class="ClsP active"><a data-toggle="pill" href="#Protocol" class="ClsTest"><span class="Spanclass">Protocol</span></a></li>
                                <li class="ClsEM"><a data-toggle="pill" href="#ConsCSR"><span class="SpanclassEM">Electrical Motor Data</span></a></li>
                                <li class="ClsEI"><a data-toggle="pill" href="#AMC"><span class="SpanclassEI">Electrical Installation Checking</span></a></li>
                                <li class="ClsEC"><a data-toggle="pill" href="#AFR"><span class="SpanclassEC">Electrical Commissionning Check-up</span></a></li>
                                <li class="ClsEPC"><a data-toggle="pill" href="#Others"><span class="SpanclassEPC">Electrical Pre-Commissionning Check-up</span></a></li>
                            </ul>
                        </div>
                        <div class="tab-content" id="PillBody">
                            <br />
                            <div id="Protocol" class="tab-pane fade in active">

                                <div id="IdDivCheckSheetForm">
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectCode" class="ClsLabelCustTxtBox ">Project Code</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtProjectCode" type="text" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly" disabled="disabled" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectName" class="ClsLabelCustTxtBox ">Project Name</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtProjectName" readonly="readonly" type="text" class="form-control input-sm ClsCheckSheetDetails" disabled="disabled" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtClient" class="ClsLabelCustTxtBox ">Client</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtClient" type="text" readonly="readonly" value="" class="form-control input-sm ClsCheckSheetDetails" disabled="disabled" />

                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtState" class="ClsLabelCustTxtBox ">State</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtState" disabled="disabled" type="text" class="form-control input-sm ClsCheckSheetDetails" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtLocation" class="ClsLabelCustTxtBox ">Location</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtLocation" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader" />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label  class="ClsLabelCustTxtBox ">Project Status</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input id="IdTxtProjectStatus" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader" />
                                        </div>
                                    </div>

                                </div>
                                <br />
                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProtocalStatus" class="ClsLabelCustTxtBox ">Protocal Status</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdTxtProtocalStatus" disabled="disabled" type="text" class="form-control input-sm ClsCheckSheetDetails" />
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProtocalSubmittedDate" class="ClsLabelCustTxtBox ">Protocal Submitted Date</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdTxtProtocalSubmittedDate" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader" />
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdTxtProtocalApprovalDate" class="ClsLabelCustTxtBox ">Protocal Approval Date</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdTxtProtocalApprovalDate" type="text" readonly="readonly" class="form-control input-sm ClsTxtCustHeader" />
                                    </div>
                                </div>
                                <br />
                                <br />

                                @*/==============  Acordian is appended  =============/*@
                                <div id="DivAcoordian">
                                </div>
                            </div>

                            @*//========================================================  EMD  ===================================================//*@

                            <div id="ConsCSR" class="tab-pane fade">

                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdCusName" class="ClsLabelCustTxtBox ">Customer :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdEMD_Customer" type="text" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly" disabled="disabled" />
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="TxtDate" class="ClsLabelCustTxtBox ">Date :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdEMD_Date" type="text" readonly="readonly" value="" class="form-control input-sm ClsCheckSheetDetails" disabled="disabled" />

                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdSiteaddress" class="ClsLabelCustTxtBox ">Site Address :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <textarea id="IdEMD_Siteaddress" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly"></textarea>
                                    </div>
                                </div>
                                <br />

                                <div id="DivAcoordianForEMD">
                                </div>

                            </div>

                            @*//========================================================  EIC  ===================================================//*@


                            <div id="AMC" class="tab-pane fade">
                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdCusName" class="ClsLabelCustTxtBox ">Customer :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdEIC_Customer" type="text" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly" disabled="disabled" />
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="TxtDate" class="ClsLabelCustTxtBox ">Date :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdEIC_Date" type="text" readonly="readonly" value="" class="form-control input-sm ClsCheckSheetDetails" disabled="disabled" />

                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdSiteaddress" class="ClsLabelCustTxtBox ">Site Address :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <textarea id="IdEIC_Siteaddress" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly"></textarea>
                                    </div>
                                </div>
                                <br />
                                <div id="DivAcoordianForEIC">
                                </div>
                            </div>

                            @*//========================================================  ECC  ===================================================//*@

                            <div id="AFR" class="tab-pane fade">

                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdCusName" class="ClsLabelCustTxtBox ">Customer :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdECC_Customer" type="text" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly" disabled="disabled" />
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="TxtDate" class="ClsLabelCustTxtBox ">Date :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdECC_Date" type="text" readonly="readonly" value="" class="form-control input-sm ClsCheckSheetDetails" disabled="disabled" />

                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdSiteaddress" class="ClsLabelCustTxtBox ">Site Address :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <textarea id="IdECC_Siteaddress" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly"></textarea>
                                    </div>
                                </div>
                                <br />
                                <div id="DivAcoordianForECC">
                                </div>
                            </div>

                            @*//========================================================  EPCC  ===================================================//*@

                            <div id="Others" class="tab-pane fade">
                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdCusName" class="ClsLabelCustTxtBox ">Customer :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdEPCC_Customer" type="text" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly" disabled="disabled" />
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="TxtDate" class="ClsLabelCustTxtBox ">Date :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input id="IdEPCC_Date" type="text" readonly="readonly" value="" class="form-control input-sm ClsCheckSheetDetails" disabled="disabled" />

                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <label for="IdSiteaddress" class="ClsLabelCustTxtBox ">Site Address :</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <textarea id="IdEPCC_Siteaddress" class="form-control input-sm ClsTxtQuotaionHeader" readonly="readonly"></textarea>
                                         </div>
                                </div>
                                <br />

                                <div id="DivAcoordianForEPCC">
                                </div>
                            </div>
                        </div>
                    </div>
                    <br />
                    <div class="modal-footer">
                        <button type="button" id="IdBtnCheckSheetCancel" class="btn ButtonStyle ClsEnquiryFrombtn closeCheckSheet">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @*//  $("#DivAcoordian").append("<div class='panel panel-default' id='div_PlantAccoridn" + (resp[0].ItemNames[i]).toString().trim() + "'><div id= 'IdDiv" + resp[0].ItemNames[i] + "' class='panel-heading accordion-toggle collapsed' style='box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer;' data-toggle='collapse' data-parent='#accordion' data-target='#IDPlant" + (resp[0].ItemNames[i]).toString().trim() + "'><h4 class='panel-title text-center' style='font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;'>" + resp[0].ItemNames[i] + "</h4></div><div id='IDPlant" + (resp[0].ItemNames[i]).toString().trim() + "'style='font-family: Arial; font-size: 13px; color: black;' class='panel-collapse collapse'><div class='panel-body'><div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'></div><div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #1f716c; width: 97%; margin-left: 13px; font-weight: bolder'>Supporting Structure Level (top of insert plate)</div></div><br /><br /><div class='row'><div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'></div><div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'><div id='DivImg1" + resp[0].ItemNames[i] + "'><img style='height: 100%; width: 100%' id='Img1" + (resp[0].ItemNames[i]).toString().trim() + "'/></div></div></div><br /><br /><div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>Level Values</div></div><br /><div class='row'><div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'></div><div class=1col-lg-8 col-md-8 col-sm-8 col-xs-12'><table id= 'LevelTblGrid" + (resp[0].ItemNames[i]).toString().trim() + "'></table><div id='LevelTblPager" + (resp[0].ItemNames[i]).toString().trim() + "'></div></div></div><br /><div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>Check points during start-up</div></div><br /><div class='row'><div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'></div><div class='col-lg-11 col-md-11 col-sm-11 col-xs-12'><div style='width: inherit; overflow: auto;'><table id='StatusGrid " + (resp[0].ItemNames[i]).toString().trim() + "'></table><div id='StatusPager" + (resp[0].ItemNames[i]).toString().trim() + "'></div></div></div></div></div></div></div>");*@
    @*<div class='panel panel-default' id='div_PlantAccoridn" + (resp[0].ItemNames[i]).toString().trim() + "'><div id= 'IdDiv" + resp[0].ItemNames[i].toString().trim()  + "' class='panel-heading accordion-toggle collapsed' style='box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer;' data-toggle='collapse' data-parent='#accordion' data-target='#IDPlant" + (resp[0].ItemNames[i]).toString().trim() + "'><h4 class='panel-title text-center' style='font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;'>" + resp[0].ItemNames[i] + "</h4></div><div id='IDPlant" + (resp[0].ItemNames[i]).toString().trim() + "'style='font-family: Arial; font-size: 13px; color: black;' class='panel-collapse collapse'><div class='panel-body'><div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'></div><div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #1f716c; width: 97%; margin-left: 13px; font-weight: bolder'>Supporting Structure Level (top of insert plate)</div></div><br /><br /><div  id= 'IdDivAttachment" + resp[0].ItemNames[i].toString().trim()  + "'></div><br /><div  id= 'IdDivCheckPoints" + resp[0].ItemNames[i].toString().trim()  + "'></div></div></div></div>*@
    @*<div class='row'><div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'></div><div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'><div id='DivImg1" + resp[0].ItemNames[i] + "'><img style='height: 100%; width: 100%' id='Img1" + (resp[0].ItemNames[i]).toString().trim() + "'/></div></div></div><br /><br /><div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>Level Values</div></div><br /><div class='row'><div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'></div><div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'><table id= 'LevelTblGrid" + (resp[0].ItemNames[i]).toString().trim() + "'></table><div id='LevelTblPager" + (resp[0].ItemNames[i]).toString().trim() + "'></div></div></div>*@
    @*<div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>Check points during start-up</div></div><br /><div class='row'><div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'></div><div class='col-lg-11 col-md-11 col-sm-11 col-xs-12'><div style='width: inherit; overflow: auto;'><table id='StatusGrid " + (resp[0].ItemNames[i]).toString().trim() + "'></table><div id='StatusPager" + (resp[0].ItemNames[i]).toString().trim() + "'></div></div></div></div>*@


</body>
</html>
<script>

   
    var ProjectIC = 0;
    if ('@ViewBag.InstallationProtocallsProjectCode' == '0')
    {

        $("#IdToAppend").html("");
        //function NewButton() {
        //    $("#IdToAppend").html("");
        //    $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        //    // $("#IdToAppend").append("<Button id='IdBtnNewIdleReport' class='AddNew'>New</Button>")
        //}
        //NewButton();

        $(document).on('click', '#IdBtnNewIdleReport', function ()
        {
            $("#IdDivModelViewIdleReport").modal({ backdrop: false });
        })
        newbuttonValue = 1;         
    }

    if ('@ViewBag.InstallationProtocallsProjectCode' != '0')
    {
        ProjectIC = '@ViewBag.InstallationProtocallsProjectCode';
        newbuttonValue = 0;
    }

    function LeftMenuInactive() {
        for (var i = 0; i < $("#IdLeftMenu li").length; i++) {
            $($("#IdLeftMenu li ")[i]).removeClass("menuActive");
        }
    }


    //============================================= Check Sheet ==========================================//
    function CheckSheetLeftMenu() {
        $("#IdToAppend").html("");
        $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        $("#IdToAppend").append("<ul id='IdLeftMenu'></ul>")
        $("#IdLeftMenu").css("margin-left", "-30px");
        $("#IdLeftMenu").append("<li id='IdIPInstallationProtocalls'>Installation Protocols</li>")
        $("#IdLeftMenu").append("<li id='IdIPProject'>Project</li>")
        $("#IdLeftMenu").append("<li id='IdIPCheckSheet'>Progress Sheet</li>")
        $("#IdLeftMenu").append("<li id='IdIPHydraAvailability'>Hydra Availability</li>")
        $("#IdLeftMenu").append("<li id='IdIPIdleReport'>Idle Report</li>")
        $("#IdLeftMenu").append("<li id='IdIPCommissioning'>Commissioning</li>")
      //  $("#IdLeftMenu").append("<li id='IdIPShortSupplies'>Short Supplies</li>")

    }

    $(document).on('click', '#IdIPInstallationProtocalls', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/InstallationProtocalls?ProjectId=" + 0));
        newbuttonValue = 1;
    })

    $(document).on('click', '#IdIPHydraAvailability', function ()
    {
       
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/HydraAvailability?ProjectId=" + ProjectIC));
    })

    $(document).on('click', '#IdIPProject', function () {
      
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Projects?ProjectId=" + ProjectIC));
    })

    $(document).on('click', '#IdIPCommissioning', function () {
     
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + ProjectIC));
    })

    $(document).on('click', '#IdIPCheckSheet', function () {
       
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/CheckSheets?ProjectId=" + ProjectIC));
    })

    $(document).on('click', '#IdIPIdleReport', function () {
      
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/IdleReport?ProjectId=" + ProjectIC));
    })

    //=====================================================================================================//

    $.ProjectLandingGrid = function ()
    {
        $("#projectTblGrid").jqGrid
            ({
                url: AbsolutePath("/InstalltionProtocol/GetInstallationProtocolHeader?ProjectIC=" + ProjectIC+""),
                caption: "Installtion Protocol",
                datatype: "Json",
                mtype: "GET",
                height: 'auto',
                viewrecords: true,
                rowList: [15, 20, 50, 100],
                rowNum: 20,
                rownumbers: true,
                sortname: "Project_ID",
                sortorder: "desc",
                pager: "#projectDivPager",
                width: 930,
                selrow: true,
                loadonce: true,
                colModel:
                    [

           {
               name: "View", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                   return "<i class='glyphicon glyphicon-file' title='Click Here For View' data-toggle='modal' data-target='#IdEnquiryModal' onClick='ViewinstallationProtocolDetails(" + b.rowId + ")' ></i>"
               }
           },
         { name: "InstallationProtocolID", label: "InstallationProtocolID", editable: true, align: "left", hidden: true },
         { name: "InstallationProtocolSpecIC", label: "InstallationProtocolSpecIC", editable: true, align: "left", hidden: true },
         { name: "Project_ID", label: "Project_ID", width: 100, editable: true, align: "left", hidden: true },
         { name: "Project_Code", label: "Project Code", width: 150, editable: true, align: "left", hidden: false },
         { name: "SpecPoints1_ChangeValues", label: "SpecPoints1_ChangeValues", editable: true, align: "left", hidden: true },
         { name: "SpecPoints2_ChangeValues", label: "SpecPoints2_ChangeValues", editable: true, align: "left", hidden: true },
         { name: "SpecPoints3_ChangeValues", label: "SpecPoints3_ChangeValues", editable: true, align: "left", hidden: true },
         { name: "Site_Client_Name", label: "Client Name", editable: true, align: "left", width: 150, hidden: false },
         { name: "Site_Client_Mobile", label: "Client Mobile", editable: true, align: "left", width: 100, hidden: false },
         { name: "Location", label: "Location", editable: true, align: "left", width: 150, hidden: false },
         { name: "Model_ID", label: "Model_ID", editable: true, align: "left", hidden: true },
         { name: "Attachment1", label: "Attachment1", editable: true, align: "left", hidden: true },
         { name: "Attachment2", label: "Attachment2", editable: true, align: "left", hidden: true },
         { name: "Attachment3", label: "Attachment3", editable: true, align: "left", hidden: true },
         { name: "SpecPoints1", label: "SpecPoints1", editable: true, align: "left", hidden: true },
         { name: "SpecPoints2", label: "SpecPoints2", editable: true, align: "left", hidden: true },
         { name: "SpecPoints3", label: "SpecPoints3", editable: true, align: "left", hidden: true },
         { name: "SIC_Models_Name", label: "Models Name", editable: true, align: "left", width: 100, hidden: true },
         { name: "SIC_ItemsTypesIC", label: "SIC_ItemsTypesIC", editable: true, align: "left", hidden: true },
         { name: "ItemTypeName", label: "Item Type", editable: true, align: "left", width: 100, hidden: true },
         { name: "Attachement1Desc", label: "Attachement1Desc", editable: true, align: "left", hidden: true },
         { name: "Attachment2Desc", label: "Attachment2Desc", editable: true, align: "left", hidden: true },
         { name: "Attachment3Desc", label: "Attachment3Desc", editable: true, align: "left", hidden: true },
         { name: "InstallationProtocol_StatusIC", label: "InstallationProtocol_StatusIC", editable: true, align: "left", hidden: true },
         { name: "InstallationProtocol_Submitted_date", label: "Submitted date", editable: true, align: "left", width: 100, hidden: false },
         { name: "InstallationProtocol_Approved_date", label: "Approved date", editable: true, align: "left", width: 100, hidden: false },
         { name: "InstallationProtocolApprovaStatusName", label: "Status", editable: true, align: "left", width: 100, hidden: false },
         { name: "InstallationProtocolStatusMachineWiseIC", label: "InstallationProtocolStatusMachineWiseIC", editable: true, align: "left", hidden: true }],

                loadComplete: function () {
                    $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                },
                resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
                multiselect: false,
                jsonReader: {
                    root: "root",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            })
        $("#projectTblGrid").navGrid("#projectDivPager", { add: false, edit: false, del: false, refresh: false, search: false })
        $("#projectTblGrid").filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
        $("#projectTblGrid").navGrid("#projectDivPager", {}, {}, {}, {}, {
            multipleSearch: true,
            multipleGroup: true,
            showQuery: true
        });
        $("#projectTblGrid").navButtonAdd("#projectDivPager",
           {
               title: "Refresh",
               buttonicon: "ui-icon-refresh",
               caption: "",
               position: "last",
               onClickButton: function () {
                   $("#projectTblGrid").jqGrid("GridUnload");
                   $.ProjectLandingGrid();
               }
           });

    }
    $.ProjectLandingGrid();

    function AppendStageValue(length, Value, DivId, List, PanelDesclist)
    {
        $(DivId).html("");
        //var PanelDescription=null;
        //var EarthingTypeName = null;
        //var EartingValues = null;

        for (var i = 0; i < length ; i++)
        {
            var LevelValuesList = [];

            for (j = 0; j < List.length; j++)
            {
                if (List[j].StageIC == i + 1)
                {
                    LevelValuesList.push(List[j])
                }
            }

            var stagename = null;
            var PanelDescription = null;

            if (i+1 == 1)
            {
                stagename = "Primary";
                PanelDescription = PanelDesclist[0];
            }
            else if (i + 1 == 2)
            {
                stagename = "Secondary";
                PanelDescription = PanelDesclist[1];
            }
            else if (i + 1 == 3) {
                stagename = "Tertiary";
                PanelDescription = PanelDesclist[2];
            }
            else
            {
                stagename = "Other";
                PanelDescription = PanelDesclist[3];
            }
            $(DivId).append
                (
              "<div class='panel panel-default' id=''>" +
              "<div id='Header" + Value + i + "' class='panel-heading accordion-toggle collapsed' style='box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer;' data-toggle='collapse' data-parent='#accordion' data-target='#PanalBody" + Value + i + "'>" +
                  "<h4 class='panel-title text-center' style='font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;'>" + stagename + "</h4>" +
              "</div>" +
              "<div id='PanalBody" + Value + i + "'style='font-family: Arial; font-size: 13px; color: black;' class='panel-collapse collapse'>" +
              "<div class='panel-body'>" +
              " <div class='row'>" +
              "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>" +
                  "<label for='IdPanelDesc' class='ClsLabelCustTxtBox'>Panel Description :</label>" +
              "</div>" +
              "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>" +
                  "<textarea id='IdPanelDesc" + Value + i + "' class='form-control input-sm ClsTxtQuotaionHeader'  disabled='disabled' >" + PanelDescription + "</textarea>" +
              "</div>" +
             " <div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>" +
                  "<label for='IdSubmitedDate' class='ClsLabelCustTxtBox'>Submited Date :</label>" +
              "</div>" +
              "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>" +
                  "<input id='IdTxtSubmitedDate" + Value + i + "' type='text' readonly='readonly' value='' class='form-control input-sm ClsCheckSheetDetails' disabled='disabled' />" +
              "</div>" +
              "</div>" +
               "<br/>" +
              "<div  class='row EartingValueInfo'>" +
              "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>" +
                 " <label for='IdPanelDesc' class='ClsLabelCustTxtBox'>Earting Type</label>" +
              "</div>" +
              "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>" +
                  "<input id='IdTxtEartingType" + Value + i + "'  type='text' readonly='readonly' value='' class='form-control input-sm ClsCheckSheetDetails' disabled='disabled' />" +
              "</div>" +
              "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>" +
                  "<label for='IdPanelDesc' class='ClsLabelCustTxtBox'>Earting Values</label>" +
              "</div>" +
              "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>" +
                  "<input id='IdTxtEartingValues" + Value + i + "'  type='text' readonly='readonly' value='' class='form-control input-sm ClsCheckSheetDetails' disabled='disabled' />" +
              "</div>" +
            "</div>" +
            "<br />" +
             "<div class='row'>" +
              "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>" +
                  "Electrical Motor Data" +
              " </div>" +
                "</div>" +
                 "<br />" +
                "<br />" +
                "<div class='row'>" +
                    "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>" +
                  "<div style='width: inherit; overflow: auto;' id='IdDivGridEM'>" +
                      "<table id='IdTblGrid" + Value + i + "'></table>" +
                      "<div id='IdPager" + Value + i + "'></div>" +
                  "</div>" +
                        "</div>" +
                "</div>" +
                "<br />" +
                 "</div>" +
                "</div>" +
        "</div>");




            switch (Value)
            {
                case "EMD":
                    $.EMotor("#IdTblGrid" + Value + i, "#IdPager" + Value + i, LevelValuesList);
                    $("#IdTxtSubmitedDate" + Value + i).val(LevelValuesList[0].ElectricalMotorData_SubmittedDate_SE);
                    $(".EartingValueInfo").hide();
                    break;

                case "EIC":
                    $.EICGrid("#IdTblGrid" + Value + i, "#IdPager" + Value + i, LevelValuesList);
                    $("#IdTxtSubmitedDate" + Value + i).val(LevelValuesList[0].Electrical_IC_SubimttedDate);
                    $("#IdTxtEartingType" + Value + i).val(LevelValuesList[0].EarthingTypeName);
                    $("#IdTxtEartingValues" + Value + i).val(LevelValuesList[0].EartingValues);
                    break;

                case "ECC":
                    $.ECCGrid("#IdTblGrid" + Value + i, "#IdPager" + Value + i, LevelValuesList);
                    $("#IdTxtSubmitedDate" + Value + i).val(LevelValuesList[0].Electrical_CC_SubmittedDate);
                    $(".EartingValueInfo").hide();
                    break;

                case "EPCC":
                    $.EPCCGrid("#IdTblGrid" + Value + i, "#IdPager" + Value + i, LevelValuesList);
                    $("#IdTxtSubmitedDate" + Value + i).val(LevelValuesList[0].Electrical_PC_SubmittedDate_SE);
                    $(".EartingValueInfo").hide();
                    break;
            }

        }
    }

    function Creardata() {
        $("#IdTxtProjectCode").val("");
        $("#IdTxtProjectName").val("");
        $("#IdTxtClient") .val("");
        $("#IdTxtState").val("");
        $("#IdTxtLocation").val("");
        $("#IdTxtProjectStatus").val("");
        $("#IdTxtProtocalStatus").val("");
        $("#IdTxtProtocalStatus").val("");
        $("#IdTxtProtocalSubmittedDate").val("");
        $("#IdTxtProtocalApprovalDate") .val("");
        $("#IdEMD_Customer").val("");
        $("#IdEMD_Date").val("");
        $("#IdEMD_Siteaddress").val("");
        $("#IdEIC_Customer").val("");
        $("#IdEIC_Date").val("");
        $("#IdEIC_Siteaddress").val("");
        $("#IdECC_Customer").val("");
        $("#IdECC_Date").val("");
        $("#IdEIC_Siteaddress").val("");
        $("#IdECC_Customer").val("");
        $("#IdECC_Date").val("");
        $("#IdECC_Siteaddress").val("");
        $("#IdEPCC_Customer").val("");
        $("#IdEPCC_Date").val("");
        $("#IdEPCC_Siteaddress").val("");
        $("#DivAcoordian").html("");

    };

    function ViewinstallationProtocolDetails(Id)
    {      
        $('#List1 a[href="#Protocol"]').tab('show')

        if ('@ViewBag.InstallationProtocallsProjectCode' == '0')
        {
            CheckSheetLeftMenu()
        }
        $("#IdDivModelCheckSheet").modal({ backdrop: false });

        var rowdata = $("#projectTblGrid").getRowData(Id);
      
        var ProjectId = rowdata.Project_ID;
       
         ProjectIC = rowdata.Project_ID;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/InstalltionProtocol/GetInstallationProtocolDetails?ProjectIC=" + ProjectId), //UploadFiles
                type: "Get",
                async: false,
                success: function (resp)
                {
                    debugger
                    console.log(resp);
                    Creardata();          
                    $("#IdTxtProjectCode").val(resp[0].InstalltionProtocolHeaderlist[0].Project_Code);
                    $("#IdTxtProjectName").val(resp[0].InstalltionProtocolHeaderlist[0].Project_Name);
                    $("#IdTxtClient").val(resp[0].InstalltionProtocolHeaderlist[0].Site_Client_Name);
                    $("#IdTxtState").val(resp[0].InstalltionProtocolHeaderlist[0].Location);
                    $("#IdTxtLocation").val(resp[0].InstalltionProtocolHeaderlist[0].Location);
                    $("#IdTxtProjectStatus").val(resp[0].InstalltionProtocolHeaderlist[0].ProtocolStageStatusName);
                    $("#IdTxtProtocalStatus").val(resp[0].InstalltionProtocolHeaderlist[0].InstallationProtocolApprovaStatusName);
                    $("#IdTxtProtocalSubmittedDate").val(resp[0].InstalltionProtocolHeaderlist[0].InstallationProtocol_Submitted_date);
                    $("#IdTxtProtocalApprovalDate").val(resp[0].InstalltionProtocolHeaderlist[0].InstallationProtocol_Approved_date);

                    //------------------------------

                    $("#IdEMD_Customer").val(resp[0].InstalltionProtocolHeaderlist[0].Site_Client_Name);
                    $("#IdEMD_Date").val(resp[0].InstalltionProtocolHeaderlist[0].Project_CreatedDate);
                    $("#IdEMD_Siteaddress").val(resp[0].InstalltionProtocolHeaderlist[0].Location);

                    //------------------------------

                    $("#IdEIC_Customer").val(resp[0].InstalltionProtocolHeaderlist[0].Site_Client_Name);
                    $("#IdEIC_Date").val(resp[0].InstalltionProtocolHeaderlist[0].Project_CreatedDate);
                    $("#IdEIC_Siteaddress").val(resp[0].InstalltionProtocolHeaderlist[0].Location);

                    //------------------------------

                    $("#IdECC_Customer").val(resp[0].InstalltionProtocolHeaderlist[0].Site_Client_Name);
                    $("#IdECC_Date").val(resp[0].InstalltionProtocolHeaderlist[0].Project_CreatedDate);
                    $("#IdECC_Siteaddress").val(resp[0].InstalltionProtocolHeaderlist[0].Location);

                    //------------------------------

                    $("#IdEPCC_Customer").val(resp[0].InstalltionProtocolHeaderlist[0].Site_Client_Name);
                    $("#IdEPCC_Date").val(resp[0].InstalltionProtocolHeaderlist[0].Project_CreatedDate);
                    $("#IdEPCC_Siteaddress").val(resp[0].InstalltionProtocolHeaderlist[0].Location);

                    //@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@//
                    $("#DivAcoordian").html("");
                    var AttachmentList = [];
                    for (var i = 0; i < resp[0].InstalltionProtocolHeaderlist.length; i++) {
                        AttachmentList.push(resp[0].InstalltionProtocolHeaderlist[i].Attachment1);
                        AttachmentList.push(resp[0].InstalltionProtocolHeaderlist[i].Attachment2);
                        AttachmentList.push(resp[0].InstalltionProtocolHeaderlist[i].Attachment3);
                    }
                    //@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@//

                    var Specpointslist = [];
                    for (var k = 0; k < resp[0].InstalltionProtocolHeaderlist.length; k++)
                    {
                        var LevelValues1 = [];
                        var LevelValues2 = [];
                        var LevelValues3 = [];
                        var SplitAgainForCorrectedValues = [];
                        var SplitChangedLevelValues = [];

                        // SpecPoints1 ================================================
                        var SpecPoints1 = (resp[0].InstalltionProtocolHeaderlist[k].SpecPoints1);
                        var SplitLevelValues = SpecPoints1.split(",");
                        var SpecPoints1_ChangeValues = (resp[0].InstalltionProtocolHeaderlist[k].SpecPoints1_ChangeValues);

                        if (SpecPoints1_ChangeValues != "")
                        {
                            SplitChangedLevelValues = SpecPoints1_ChangeValues.split(",");

                        }
                        for (var i = 0; i < SplitLevelValues.length; i++)
                        {
                            var SplitAgain = SplitLevelValues[i].split(" ");
                            if (SplitChangedLevelValues.length == 0)
                            {
                                SplitAgainForCorrectedValues[1] = "0";
                            } else
                            {
                                SplitAgainForCorrectedValues = SplitChangedLevelValues[i].split(" ");
                            }

                            LevelValues =
                               {
                                   LevelValues: SplitAgain[0],
                                   Initial: SplitAgain[1],
                                   Corrected: SplitAgainForCorrectedValues[1],
                               }
                            LevelValues1.push(LevelValues);
                        }
                        Specpointslist.push(LevelValues1);
                        // SpecPoints2 ================================================
                        var SpecPoints2 = (resp[0].InstalltionProtocolHeaderlist[k].SpecPoints2);
                        var SplitLevelValues = SpecPoints2.split(",");

                        var SpecPoints2_ChangeValues = (resp[0].InstalltionProtocolHeaderlist[k].SpecPoints2_ChangeValues);

                        if (SpecPoints1_ChangeValues != "")
                        {
                            SplitChangedLevelValues = SpecPoints2_ChangeValues.split(",");
                        }

                        for (var i = 0; i < SplitLevelValues.length; i++)
                        {

                            var SplitAgain = SplitLevelValues[i].split(" ");

                            if (SplitChangedLevelValues.length == 0)
                            {
                                SplitAgainForCorrectedValues[1] = "0";
                            }
                            else
                            {
                                SplitAgainForCorrectedValues = SplitChangedLevelValues[i].split(" ");
                            }

                            LevelValues =
                               {
                                   LevelValues: SplitAgain[0],
                                   Initial: SplitAgain[1],
                                   Corrected: SplitAgainForCorrectedValues[1],
                               }
                            LevelValues2.push(LevelValues);
                        }
                        Specpointslist.push(LevelValues2);
                        // SpecPoints3 ================================================
                        var SpecPoints3 = (resp[0].InstalltionProtocolHeaderlist[k].SpecPoints3);
                        var SplitLevelValues = SpecPoints3.split(",");
                        var SpecPoints3_ChangeValues = (resp[0].InstalltionProtocolHeaderlist[k].SpecPoints3_ChangeValues);

                        if (SpecPoints1_ChangeValues != "") {
                            SplitChangedLevelValues = SpecPoints3_ChangeValues.split(",");
                        }

                        for (var i = 0; i < SplitLevelValues.length; i++)
                        {
                            var SplitAgain = SplitLevelValues[i].split(" ");

                            if (SplitChangedLevelValues.length == 0)
                            {
                                SplitAgainForCorrectedValues[1] = "0";
                            }
                            else
                            {
                                SplitAgainForCorrectedValues = SplitChangedLevelValues[i].split(" ");
                            }

                            LevelValues =
                               {
                                   LevelValues: SplitAgain[0],
                                   Initial: SplitAgain[1],
                                   Corrected: SplitAgainForCorrectedValues[1],
                               }
                            LevelValues3.push(LevelValues);
                        }
                        Specpointslist.push(LevelValues3);
                    }
                    //@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@//

                    var CheckPointsValueslist = [];
                    var CheckPoints = null;
                    for (var m = 0; m < resp[0].InstallationProtocolDetailerlist.length; m++) {
                        CheckPointsValues =
                            {
                                InstallationProtocolHeaderIC: resp[0].InstallationProtocolDetailerlist[m].InstallationProtocolHeaderIC,
                                CheckPoints: resp[0].InstallationProtocolDetailerlist[m].CheckPointsName.split(/(_.+)/)[0],
                                CheckPoint: (resp[0].InstallationProtocolDetailerlist[m].CheckPointsName.split(/(_.+)/)[1]).substr(1),
                                InstallationEngineerStatus: resp[0].InstallationProtocolDetailerlist[m].InstallationProtocol_CheckPoints_IE_Status,
                                InstallationEngineerRemarks: resp[0].InstallationProtocolDetailerlist[m].InstallationProtocol_CheckPoints_IE_remarks,
                                ServiceEngineerStatus: resp[0].InstallationProtocolDetailerlist[m].InstallationProtocol_CheckPoints_SE_Status,
                                ServiceEngineerRemarks: resp[0].InstallationProtocolDetailerlist[m].InstallationProtocol_CheckPoints_SE_remarks,
                                ApprovedDateTime: ""
                            }
                        CheckPointsValueslist.push(CheckPointsValues)
                    }
                    //@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@//
                    debugger
                    var counter = 0;
                    var CheckPointDetail = [];
                    var a1 = 0;
                    var CheckPointName = null;
                    var CheckPointId = null;
                    var a = 0 * 1;
                    var b = (AttachmentList.length / resp[0].ItemNames.length) * 1
                    var c = 1;
                    for (var i = 0; i < resp[0].ItemNames.length; i++)
                    {

                        $("#DivAcoordian").append(" <div class='panel panel-default' id='div_PlantAccoridn" + resp[0].ItemNames[i].toString().trim()+i+ "'><div id= 'IdDiv" + resp[0].ItemNames[i].toString().trim()+i+ "' class='panel-heading accordion-toggle collapsed' style='box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer;' data-toggle='collapse' data-parent='#accordion' data-target='#IDPlant" + resp[0].ItemNames[i].toString().trim()+i+ "'><h4 class='panel-title text-center' style='font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;'>" +
                         resp[0].ItemNames[i] + "&nbsp&nbsp" + resp[0].InstalltionProtocolHeaderlist[i].SIC_Models_Name + "&nbsp&nbsp(" + resp[0].InstalltionProtocolHeaderlist[i].StageName + "_Stage)" + "</h4></div><div id='IDPlant" + resp[0].ItemNames[i].toString().trim() + i + "'style='font-family: Arial; font-size: 13px; color: black;' class='panel-collapse collapse'><div class='panel-body'><div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'></div><div  id= 'IdDivAttachment" + resp[0].ItemNames[i].toString().trim() + i + "'></div><br /><div  id= 'IdDivCheckPoints" + resp[0].ItemNames[i].toString().trim() + i + "'></div></div></div></div>");
                        for (var j = a; j < b; j++)
                        {
                            if (AttachmentList[j] != "")
                            {
                                $("#IdDivAttachment" + resp[0].ItemNames[i].toString().trim()+i).append("<div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #1f716c; width: 97%; margin-left: 13px; font-weight: bolder'>Supporting Structure Level (top of insert plate)</div></div><br /><br /><div class='row'><div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'></div><div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'><div id='DivImg" + j + resp[0].ItemNames[i].toString().trim() + "'><img style='height: 100%; width: 100%' id='Img" + j + resp[0].ItemNames[i].toString().trim() + "'/></div></div></div><br /><br /><div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>Level Values</div></div><br /><div class='row'><div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'></div><div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'><table id= 'LevelTblGrid" + j + resp[0].ItemNames[i].toString().trim() + "'></table><div id='LevelTblPager" + j + resp[0].ItemNames[i].toString().trim() + "'></div></div></div><br/><br/>");
                                $("#Img" + j + resp[0].ItemNames[i].toString().trim()).attr('src', AbsolutePath("/UploadedFiles/" + AttachmentList[j]));

                                $.LevelGrid("#LevelTblGrid" + j + resp[0].ItemNames[i].toString().trim(), "#LevelTblPager" + j + resp[0].ItemNames[i].toString().trim(), Specpointslist[j])
                            }
                        }
                        c = (c + 1) * 1;
                        a = b;
                        b = ((AttachmentList.length / resp[0].ItemNames.length) * 1) * c;

                    }
                    //&& CheckPointId != CheckPointsValueslist[n].InstallationProtocolHeaderIC
                    debugger
                    for (var n = a1; n < CheckPointsValueslist.length; n++)
                    {
                        if (CheckPointName == null || CheckPointName != (CheckPointsValueslist[n].CheckPoints).trim() || CheckPointId != CheckPointsValueslist[n].InstallationProtocolHeaderIC)
                        {
                            if (CheckPointName != (CheckPointsValueslist[n].CheckPoints).trim() && CheckPointDetail.length != 0 || (CheckPointId != CheckPointsValueslist[n].InstallationProtocolHeaderIC && CheckPointName == (CheckPointsValueslist[n].CheckPoints).trim()))
                            {
                                if (CheckPointsValueslist[n - 1].InstallationProtocolHeaderIC == resp[0].InstalltionProtocolHeaderlist[counter].InstallationProtocolID)
                                {
                                    $("#IdDivCheckPoints" + resp[0].ItemNames[counter].toString().trim()+counter).append("<div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>" + CheckPointName + "</div></div><br /><div class='row'><div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'></div><div class='col-lg-11 col-md-11 col-sm-11 col-xs-12'><div style='width: inherit; overflow: auto;'><table id='StatusGrid" + n + resp[0].ItemNames[counter].toString().trim() + "'></table><div id='StatusPager" + n + resp[0].ItemNames[counter].toString().trim() + "'></div></div></div></div><br/><br/>")
                                    $.Checkpoints("#StatusGrid" + n + resp[0].ItemNames[counter].toString().trim(), "#StatusPager" + n + resp[0].ItemNames[counter].toString().trim(), CheckPointDetail);
                                    CheckPointName = null;
                                    CheckPointId = null;
                                    CheckPointDetail = [];
                                    a1 = n;
                                }
                                if (CheckPointsValueslist[n].InstallationProtocolHeaderIC != resp[0].InstalltionProtocolHeaderlist[counter].InstallationProtocolID) {
                                    a1 = n;
                                    counter = counter + 1 * 1;
                                }
                            }
                            CheckPointName = CheckPointsValueslist[n].CheckPoints.trim();
                            CheckPointId = CheckPointsValueslist[n].InstallationProtocolHeaderIC;
                        }

                        if (CheckPointsValueslist[n].InstallationProtocolHeaderIC == resp[0].InstalltionProtocolHeaderlist[counter].InstallationProtocolID && CheckPointName == (CheckPointsValueslist[n].CheckPoints).trim())
                        {
                            CheckPointDetail.push(CheckPointsValueslist[n]);
                        }

                        if (n == CheckPointsValueslist.length - 1)
                        {
                            if (CheckPointsValueslist[n].InstallationProtocolHeaderIC == resp[0].InstalltionProtocolHeaderlist[counter].InstallationProtocolID)
                            {
                                $("#IdDivCheckPoints"+resp[0].ItemNames[counter].toString().trim()+counter).append("<div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'>" + CheckPointName + "</div></div><br /><div class='row'><div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'></div><div class='col-lg-11 col-md-11 col-sm-11 col-xs-12'><div style='width: inherit; overflow: auto;'><table id='StatusGrid" + n + resp[0].ItemNames[counter].toString().trim() + "'></table><div id='StatusPager" + n + resp[0].ItemNames[counter].toString().trim() + "'></div></div></div></div>")
                                $.Checkpoints("#StatusGrid" + n + resp[0].ItemNames[counter].toString().trim(), "#StatusPager" + n + resp[0].ItemNames[counter].toString().trim(), CheckPointDetail);
                                CheckPointName = null;
                                CheckPointId = null;
                                CheckPointDetail = [];
                                a1 = n;
                            }
                        }
                    }
                    //@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@//

                    var a = [];
                    var length = 0;
                    var Value = null;
                    var DivId = null;
                    var List = null;

                    var PanelDesclist = [];
                    PanelDesclist.push(resp[0].InstalltionProtocolHeaderlist[0].PrimaryDescription);
                    PanelDesclist.push(resp[0].InstalltionProtocolHeaderlist[0].SecondaryDescription)
                    PanelDesclist.push(resp[0].InstalltionProtocolHeaderlist[0].TerioryDescription)
                    PanelDesclist.push(resp[0].InstalltionProtocolHeaderlist[0].OthersDescription)

                    $(document).on('click', '.ClsEM', function ()
                    {
                        length = resp[0].ElectricalMotorDataCount.length;
                        Value = "EMD";
                        DivId = "#DivAcoordianForEMD";
                        List = resp[0].InstallationProtocolElectricalMotorDatalist;
                        AppendStageValue(length, Value, DivId, List,PanelDesclist);
                    });
                    $(document).on('click', '.ClsEI', function ()
                    {
                        length = resp[0].ElectricalInstallationCommissioningDataCheckuPCount.length;
                        Value = "EIC";
                        DivId = "#DivAcoordianForEIC";
                        List = resp[0].InstallationProtocolElectricalInstalltionCommmDatalist;
                        AppendStageValue(length, Value, DivId, List, PanelDesclist);
                    });
                    $(document).on('click', '.ClsEC', function ()
                    {
                        length = resp[0].ElectricalCommissoingDataCheckuPCount.length;
                        Value = "ECC";
                        DivId = "#DivAcoordianForECC";
                        List = resp[0].InstallationProtocolElectricalCommmDatalist;
                        AppendStageValue(length, Value, DivId, List, PanelDesclist);
                    });
                    $(document).on('click', '.ClsEPC', function () {
                        length = resp[0].ElectricalPreCommissioningDataCheckuPCount.length;
                        Value = "EPCC";
                        DivId = "#DivAcoordianForEPCC";
                        List = resp[0].InstallationProtocolElectricalPreCommmDatalist
                        AppendStageValue(length, Value, DivId, List, PanelDesclist);
                    });

                },
                error: function (resp) {

                }

            })
        }
        $.ajaxfunc()


        $(".ClsEM").removeClass("active");
        $(".ClsEI").removeClass("active");
        $(".ClsEC").removeClass("active");
        $(".ClsEPC").removeClass("active");
        $(".ClsP").removeClass("active");
        $(".ClsP").addClass("active");

        $(".ClsP").click();

        //$("#Protocol").show();
        //  $('.nav-pills a[href="#Protocol"]').tab('show');
        //  $("#IdDivModelCheckSheet").prop("refresh","true");
       // document.location.reload(true)

    };

    $(".ClsP").click(function ()
    {
        $('.nav-pills a[href="#Protocol"]').tab('show');
    });

    //=====================================================================================================//

    $.LevelGrid = function (GridTableId, GridPagerId, LevelValuesList) {
        $(GridTableId).jqGrid({
            ignoreCase: true,
            datatype: "local",
            data: LevelValuesList,

            colModel: [
                          { name: "LevelValues", index: "LevelValues", label: "Level Values", editable: true, align: "left", width: 110 },
                          { name: "Initial", index: "Initial", label: "Initial", editable: true, align: "left", hidden: false, width: 120 },
                          { name: "Corrected", index: "Corrected", label: "Corrected (Deviations if any)", editable: true, align: "left", width: 210, resizable: true, sortable: true, search: true, searchable: true, }
            ],
            height: 'auto',
            width: 470,
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            loadonce: false,
            rowNum: 20,
            rownumbers: true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: GridPagerId,
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "Root",
                page: "Page",
                total: "Total",
                records: "Records",
                repeatitems: false
            }
        })
        $(GridTableId).navGrid(GridPagerId, { add: false, edit: false, del: false, refresh: false, search: false })
        $(GridTableId).filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
    }

    //=====================================================================================================//
    function IE_RemarksValue(cellValue, options, rowobject, action)
    {
        if (cellValue == 1)
        {
            return a = "OK";
        }
        else if (cellValue == 2) {
            return a = "NOT OK";
        }
        else if (cellValue == 3) {
            return a = "NOT APPLICABLE";
        }
        else {
            return a = "";
        }
    };

    function ISCheck_ONValue(cellValue, options, rowobject, action) {
        if (cellValue == 1) {
            var checked = "checked";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' disabled='disabled' />";
            return a;

        }
        else {
            var checked = "";
            var a = "<input type='checkbox'" + checked + "value='" + cellValue + "' disabled='disabled' />";
            return a;
        }

    };

    $.Checkpoints = function (CheckpointsTableId, CheckpointsPagerId, CheckPointDetail) {
        $(CheckpointsTableId).jqGrid({
            ignoreCase: true,
            datatype: "local",
            data: CheckPointDetail,

            colModel: [
                     { name: "CheckPoint", index: "CheckPoint", label: "Check Points", editable: true, align: "left", width: 340 },
                     { name: "InstallationEngineerStatus", label: "Installation Engineer Status", formatter: IE_RemarksValue, editable: true, align: "left", width: 160 },
                     { name: "InstallationEngineerRemarks", label: "Installation Engineer Remarks", editable: true, align: "left", hidden: false, width: 170 },
                     {name: "ServiceEngineerStatus", label: "Service Engineer Status", formatter: IE_RemarksValue, editable: true, align: "left", width: 140, resizable: true, sortable: true, search: true, searchable: true,},
                     { name: "ServiceEngineerRemarks", label: "Service Engineer Remarks", editable: true, align: "left", width: 160, resizable: true, sortable: true, search: true, searchable: true, },
                     { name: "ApprovedDateTime", index: "ApprovedDateTime", label: "Approved Date & Time", editable: true, align: "left", width: 160, resizable: true, sortable: true, search: true, searchable: true, hidden: true },
            ],
            height: 'auto',
            width: 1000,
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            loadonce: false,
            rowNum: 20,
            rownumbers: true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: CheckpointsPagerId,
            loadComplete: function (data)
            {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "Root",
                page: "Page",
                total: "Total",
                records: "Records",
                repeatitems: false
            }
        })
        $(CheckpointsTableId).navGrid(CheckpointsPagerId, { add: false, edit: false, del: false, refresh: false, search: false })

        $(CheckpointsTableId).filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
    };

    //=====================================================================================================//

    $.EMotor = function (GridTableId, GridPagerId, LevelValuesList)
    {

        $(GridTableId).jqGrid({
            ignoreCase: true,
            datatype: "local",
            data: LevelValuesList,
            colModel: [
                 { name: "ElectricalMotorDataID", label: "ElectricalMotorDataID", editable: true, align: "left",  hidden: true },
                 { name: "SIC_Models_Name", label: "Models", editable: true, align: "left", width: 100 },
                 { name: "Motor1_Serialno", label: "Motor1 Serialno", editable: true, align: "right", width: 100 },
                 { name: "MotorData_KW", label: "KW", editable: true, align: "right", width: 90 },
                 { name: "MotorData_FLC", label: "FLC", editable: true, align: "right", width: 90 },
                 { name: "MotorData_CONN", label: "CONN", editable: true, align: "right", width: 90 },
                 { name: "MotorData_SPEED", label: "SPEED", editable: true, align: "right", width: 90 },
                 { name: "MotorData_SLNO", label: "SLNO", editable: true, align: "right", width: 90 },
                 { name: "MotorData_BGRDE_NDE", label: "BGR DE/NDE", editable: true, align: "right", width: 90 },
                 { name: "MotorData_MAKE", label: "MAKE", editable: true, align: "right", width: 90 },
                 { name: "Motor_IR_Value", label: " R-E/Y-E/B-E", editable: true, align: "right", width: 90 },
                 { name: "WINDING_RESISTANCE", label: "R-Y/Y-B/B-Y", editable: true, align: "right", width: 90 },
                 { name: "Over_load_setting", label: "OverLoad Setting", editable: true, align: "right", width: 90 },
                 { name: "DIRECT_OF_ROTATION", label: "Direct of Rotation", editable: true, align: "right", width: 90 },
                 { name: "NO_LOAD_CURRENT", label: "NoLoad Current", editable: true, align: "right", width: 90 },
                 { name: "LOAD_CURRENT", label: "Load Current", editable: true, align: "right", width: 90 },

    //========================================================================================================================================//

                  { name: "Motor2_Serialno", label: "Motor2_Serialno", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_KW", label: "KW", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_FLC", label: "FLC", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_CONN", label: "CONN", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_SPEED", label: "SPEED", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_SLNO", label: "SLNO", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_BGRDE_NDE", label: "BGR DE/NDE", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_MAKE", label: "MAKE", editable: true, align: "right", width: 90 },
                  { name: "MotorData2_IR_value", label: " R-E/Y-E/B-E", editable: true, align: "right", width: 90 },
                  { name: "MD2_Winding_resistance", label: "R-Y/Y-B/B-Y", editable: true, align: "right", width: 90 },
                  { name: "MD2_OverLoadSetting", label: "OverLoad Setting", editable: true, align: "right", width: 90 },
                  { name: "MD2_DirectofRotation", label: "Direct of Rotation", editable: true, align: "right", width: 90 },
                  { name: "MD2_NOLoadCurrent", label: "NoLoad Current", editable: true, align: "left", width: 90 },
                  { name: "MD2_LoadCurrent", label: "Load Current", editable: true, align: "right", width: 90 },
                  { name: "IERemarksValue", label: "IE Status", editable: true, align: "left", width: 90,},
                  { name: "IERemarks", label: "IE Remarks", editable: true, align: "left", width: 90, },
                  { name: "SERemarksValue", label: "SE Status", editable: true, align: "left", width: 90, },
                  { name: "SERemarks", label: "SE Remarks", editable: true, align: "left", width: 90 },
                  { name: "EngineerRemarks", label: "Engineer Remarks", editable: true, align: "left", width: 100 ,hidden:true },
                  { name: "ElectricalMotorData_SubmittedDate", label: "ElectricalMotorData_SubmittedDate", editable: true, align: "left",  hidden: true },
                  { name: "ElectricalInstallationProtocolHeader", label: "ElectricalInstallationProtocolHeader", editable: true, align: "left", hidden: true },
                  { name: "Is_ElectricalMotorDataSubmitted", label: "Is_ElectricalMotorDataSubmitted", editable: true, align: "left", hidden: true },
                  { name: "IERemarksStatusIC", label: "IERemarksStatusIC", editable: true, align: "left", hidden: true },
                  { name: "ModelIC", label: "ModelIC", editable: true, align: "left", hidden: true },
                  { name: "StageIC", label: "StageIC", editable: true, align: "left",hidden: true },
                  { name: "SE_Remarks", label: "SE_Remarks", editable: true, align: "left",hidden: true },
                  { name: "ProjectIC", label: "ProjectIC", editable: true, align: "left", hidden: true },
                  { name: "PlantStageNumber", label: "PlantStageNumber", editable: true, align: "left", hidden: true },
                  { name: "PanelDescription", label: "PanelDescription", editable: true, align: "left",hidden: true }],

            height: 'auto',
            // width: 3555,
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            loadonce: false,
            rowNum: 20,
            rownumbers: true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: GridPagerId,
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "Root",
                page: "Page",
                total: "Total",
                records: "Records",
                repeatitems: false
            }
        })
        $(GridTableId).navGrid(GridPagerId, { add: false, edit: false, del: false, refresh: false, search: false })

        $(GridTableId).filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
        $(GridTableId).jqGrid('setGroupHeaders', {
            useColSpanStyle: false,
            groupHeaders: [
              { startColumnName: 'MotorData_KW', numberOfColumns: 13, titleText: '<center>Motor 1</center>' },
              { startColumnName: 'MotorData2_KW', numberOfColumns: 13, titleText: '<center>Motor 2</center>' },
            ]
        });

        $(GridTableId).jqGrid('setGroupHeaders', {
            useColSpanStyle: false,
            groupHeaders: [
             { startColumnName: 'MotorData_KW', numberOfColumns: 7, titleText: '<center>Motor Data</center>' },
              { startColumnName: 'Motor_IR_Value', numberOfColumns: 1, titleText: '<center>MOTOR IR VALUE (Ohm)</center>' },
              { startColumnName: 'WINDING_RESISTANCE', numberOfColumns: 1, titleText: '<center>WINDING RESISTANCE (Ohm)</center>' },
              { startColumnName: 'MotorData2_KW', numberOfColumns: 7, titleText: '<center>Motor Data</center>' },
              { startColumnName: 'MotorData2_IR_value', numberOfColumns: 1, titleText: '<center>MOTOR IR VALUE (Ohm)</center>' },
              { startColumnName: 'MD2_Winding_resistance', numberOfColumns: 1, titleText: '<center>WINDING RESISTANCE (Ohm)</center>' },
            ]
        });
    };

    $.EICGrid = function (GridTableId, GridPagerId, LevelValuesList) {

        $(GridTableId).jqGrid({
            ignoreCase: true,
            datatype: "local",
            data: LevelValuesList,
            colModel: [
                     { name: "Description", label: "Description", editable: true, align: "left", width: 475 },
                     { name: "ElectricalCommssioningRemarks", label: "IE Status",align: "left", width: 130 },
                     { name: "Remarks", label: "IE Remarks", align: "left", width: 130 },

            ],
            height: 'auto',
            width: 765,
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            loadonce: false,
            rowNum: 20,
            rownumbers: true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: GridPagerId,
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "Root",
                page: "Page",
                total: "Total",
                records: "Records",
                repeatitems: false
            }
        })
        $(GridTableId).navGrid(GridPagerId, { add: false, edit: false, del: false, refresh: false, search: false })

        $(GridTableId).filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
    };

    $.ECCGrid = function (GridTableId, GridPagerId, LevelValuesList)
    {

        $(GridTableId).jqGrid({
            ignoreCase: true,
            datatype: "local",
            data: LevelValuesList,
            colModel: [
                     { name: "Description", index: "Description", label: "Description", editable: true, align: "left", width: 480 },
                     { name: 'ISCheck_ON', label: "ISCheck ON", width: 90, align: 'center', formatter: ISCheck_ONValue, search: false },
                     { name: "IE_Remarks", label: "IE Remarks", formatter: IE_RemarksValue, align: "left", width: 90,hidden:true },
                     { name: "Remarks", label: "Remarks", editable: true, align: "left", width: 165, resizable: true, sortable: true, search: true, searchable: true, },

            ],
            height: 'auto',
            width: 765,
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            loadonce: false,
            rowNum: 20,
            rownumbers: true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: GridPagerId,
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "Root",
                page: "Page",
                total: "Total",
                records: "Records",
                repeatitems: false
            }
        })
        $(GridTableId).navGrid(GridPagerId, { add: false, edit: false, del: false, refresh: false, search: false })

        $(GridTableId).filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
    };

    $.EPCCGrid = function (GridTableId, GridPagerId, LevelValuesList)
    {
        $(GridTableId).jqGrid({
            ignoreCase: true,
            datatype: "local",
            data: LevelValuesList,
            colModel: [{ name: "Description", index: "Description", label: "Description", editable: true, align: "left", width: 450 },
                    // { name: 'Is_Electrical_PC_Submitted', label: "Is Electrical PC Submitted", width: 150, align: 'center', formatter: ISCheck_ONValue},
                     { name: "IERemarksIC", label: "IE Status", formatter: IE_RemarksValue, align: "left", width: 130 },
                     { name: "IERemarks", label: "IE Remarks",  align: "left", width: 130 },
                     { name: "SE_Remarks_IC", label: "SE Status", formatter: IE_RemarksValue, align: "left", width: 130 },
                     { name: "SERemarks", label: "SE Remarks",  align: "left", width: 130 },
                     { name: "REMARKS", label: "Remarks",  align: "left",hidden:true}],
            height: 'auto',
            width: 1000,
            viewrecords: true,
            selrow: true,
            shrinkToFit: false,
            loadonce: false,
            rowNum: 20,
            rownumbers: true,
            rowList: [20, 40, 60, 80, 100, 120],
            pager: GridPagerId,
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            jsonReader: {
                root: "Root",
                page: "Page",
                total: "Total",
                records: "Records",
                repeatitems: false
            }
        })
        $(GridTableId).navGrid(GridPagerId, { add: false, edit: false, del: false, refresh: false, search: false })

        $(GridTableId).filterToolbar({
            stringResult: true,
            searchOnEnter: true,
            searchOperators: false,
            defaultSearch: "cn",
            sopt: ["cn", "eq", "neq"],
            resetIcon: "",
            ignoreCase: true
        });
    };

</script>
