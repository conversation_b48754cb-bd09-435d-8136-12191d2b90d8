﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>HydraAvailability</title>

    <style>
    .modal-open .modal 
    {
        overflow-x: hidden;
        overflow-y: hidden;
    }
        .bold {
            font-size: 13px;
    font-weight: bolder;
        }
  .bold1 {
            font-size: 11px;
    font-weight: bolder;
        }
   </style>

</head>
<body>
   
<div style="width: inherit; overflow: auto;">
    <table id="IdTblHydraAvailabilityGrid"></table>
    <div id="IdDivHydraAvailabilityPager"></div>    

</div>
    @*//===================================================================================================//*@
     <div class="modal fade" data-backdrop="false" data-keyboard="false" id="IdDivModelViewHydraAvailability" role="dialog" style="position: absolute !important;overflow-y: scroll;">
                    <div class="modal-dialog modal-lg" style="width: 97%">
                        <!-- Modal content-->
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close closeHydraAvailability" >&times;</button>
                                <button type="button" class="Colsemodal" style="display:none" data-dismiss="modal"></button>
                                 <h4 class="modal-title">Hydra Availability Details</h4>
                            </div>
                            <div id="IDDivCustModalBody" class="modal-body">
                                <div id="IdDivCustHeaderPart">
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectCode" class="ClsLabelHydraAvailabilityTxtBox">Project Code:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">                                           
                                                <input id="IdTxtProjectCode" type="text" class="form-control HydraAvailabilityValidation titlevalue" readonly="readonly">                                           
                                        </div>
                                         <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectnamenew" class="ClsLabelHydraAvailabilityTxtBox">Project Name:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">                                           
                                                <input id="IdTxtProjectnamenew" type="text" class="form-control HydraAvailabilityValidation titlevalue" readonly="readonly">                                           
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectName" class="ClsLabelHydraAvailabilityTxtBox">Plant Type:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control HydraAvailabilityValidation titlevalue" id="IdTxtProjectName" readonly />
                                        </div>

                                    
                                    </div>
                                    <br />
                                    <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtClient" class="ClsLabelHydraAvailabilityTxtBox">Client:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control HydraAvailabilityValidation titlevalue" id="IdTxtClient" readonly />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtState" class="ClsLabelHydraAvailabilityTxtBox">State:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control HydraAvailabilityValidation titlevalue" id="IdTxtState" readonly />
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtLocation" class="ClsLabelHydraAvailabilityTxtBox">Location:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control HydraAvailabilityValidation titlevalue" id="IdTxtLocation" readonly />
                                        </div>                                      
                                    </div>
                                    <br />
                                    <div class="row">
                                         <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtStatus" class="ClsLabelHydraAvailabilityTxtBox">Status:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control HydraAvailabilityValidation titlevalue" id="IdTxtStatus" readonly />
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"  style="background-color: #8bcaca; color:#1f716c; width: 97%; margin-left: 13px; font-weight:bolder;">
                                            Hydra Availability History
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        @*<div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>*@
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                                            <div style="width: inherit; overflow: auto;" id="IdDivHydraAvailabilityHistoryDetails">
                                                <table id='IdTblHydraAvailabilityHistoryGrid'></table>
                                                <div id='IdTblHydraAvailabilityHistoryPager'></div>
                                            </div>
                                        </div>
                                    </div>

                                    <br />
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"  style="background-color: #8bcaca; color:#1f716c; width: 97%; margin-left: 13px; font-weight:bolder;">
                                            Summary
                                        </div>
                                    </div>
                                    <br />
                                     <div class="row">

                                          <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtAvailable" class="ClsLabelHydraAvailabilityTxtBox">Available:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control HydraAvailabilityValidation" id="IdTxtAvailable" readonly />
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtNotAvailable" class="ClsLabelHydraAvailabilityTxtBox">Not Available:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control HydraAvailabilityValidation" id="IdTxtNotAvailable" readonly />
                                        </div>
                                     </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                  <button type="button" id="btnExport" class="btn ButtonStyle ">Export</button>
                                @*<button type="button" id="IdBtnHydraAvailability" class="btn ButtonStyle">Draft</button>*@
                                @*<button type="button" id="IdBtnHydraAvailabilitySubmit" class="btn ButtonStyle">Submit</button>*@
                                <button type="button" id="IdBtnHydraAvailabilityCancel" class="btn ButtonStyle closeHydraAvailability">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

@*</div>*@
</body>
</html>
<script>
    function AbsolutePath(url) {
        var path = '@Request.ApplicationPath';
          if (path == '/')
              return url;
          else
              return path + url;
      }

    var ProjectID = 0;
    if ('@ViewBag.HydraAvailabilityProjectCode' == '0')
    {
        $("#IdToAppend").html("");
        //function NewButton() {
        //    $("#IdToAppend").html("");
        //    $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        //    //$("#IdToAppend").append("<Button id='IdBtnNewHydraAvailability' class='AddNew'>New</Button>")
        //    //$("#IDEnquiryLogDetailsAccordian").hide();
        //}
        //NewButton();
        $(document).on('click', '#IdBtnNewHydraAvailability', function () {
            $("#IdDivModelViewHydraAvailability").modal({ backdrop: false });
        });
        newbuttonValue = 1;
        ProjectID = '@ViewBag.HydraAvailabilityProjectCode';
    }


    if ('@ViewBag.HydraAvailabilityProjectCode' != '0')
    {
        newbuttonValue = 0;
        ProjectID = '@ViewBag.HydraAvailabilityProjectCode';
    }

    function LeftMenuInactive() {
        for (var i = 0; i < $("#IdLeftMenu li").length; i++) {
            $($("#IdLeftMenu li ")[i]).removeClass("menuActive");
        }
    }

    $(".titlevalue ").mouseenter(function () {
        var word = $(this).val();

        $(".titlevalue ").attr('title', word);
    });
   
    //============================================= Hydra Availability ==========================================//

    function HydraAvilablLeftMenu() {
        $("#IdToAppend").html("");
        $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        $("#IdToAppend").append("<ul id='IdLeftMenu'></ul>")
        $("#IdLeftMenu").css("margin-left", "-30px");
        $("#IdLeftMenu").append("<li id='IdHydraAvilablHydraAvailability'>Hydra Availability</li>")
        $("#IdLeftMenu").append("<li id='IdHydraAvilablProject'>Project</li>")
        $("#IdLeftMenu").append("<li id='IdHydraAvilablCheckSheet'>Progress Sheet</li>")
        $("#IdLeftMenu").append("<li id='IdHydraAvilablInstallationProtocalls'>Installation Protocols</li>")
        $("#IdLeftMenu").append("<li id='IdHydraAvilablIdleReport'>Idle Report</li>")
        $("#IdLeftMenu").append("<li id='IdHydraAvilablCommissioning'>Commissioning</li>")
     //   $("#IdLeftMenu").append("<li id='IdHydraAvilablShortSupplies'>Short Supplies</li>")

    }

    $(document).on('click', '#IdHydraAvilablHydraAvailability', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/HydraAvailability?ProjectId=" + 0));
        newbuttonValue = 1;
    })

    $(document).on('click', '#IdHydraAvilablProject', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Projects?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdHydraAvilablCommissioning', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdHydraAvilablCheckSheet', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/CheckSheets?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdHydraAvilablIdleReport', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/IdleReport?ProjectId=" + ProjectID));
    })

    $(document).on('click', '#IdHydraAvilablInstallationProtocalls', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/InstallationProtocalls?ProjectId=" + ProjectID));
    })

    //$(document).on('click', '#IdHydraAvilablShortSupplies', function () {
    //    $("#IdDivModelViewCust").modal("hide");
    //    LeftMenuInactive()
    //    $(this).addClass("menuActive")
    //    $(".MainContainerBody").html("");
    //    $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + 1));
    //})

//======================================================================================================//

    function EditHydraAvilablDetails(Id) {
        if ('@ViewBag.HydraAvailabilityProjectCode' == '0')
        {
            HydraAvilablLeftMenu();
        }
        //$("#IdDivModelViewHydraAvailability").modal({ backdrop: false });
       
    };

    function ViewHydraAvilablDetails(Id)
    {
        if ('@ViewBag.HydraAvailabilityProjectCode' == '0')
        {
            HydraAvilablLeftMenu()
        }
        //var rowdata = $("#IdTblHydraAvailabilityGrid").getRowData(Id);
        //var Avaiable = rowdata.Avaiable;
        //var notAvailiable = rowdata.notAvailiable;
      
        //$(this).parent().css("border", "solid red 1px")
        $("#IdDivModelViewHydraAvailability").modal({ backdrop: false })
       // var PDID = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(2).text()
       // ProjectID = PDID
       // var ProjectCode = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(3).text()
       // var Client = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(4).text()
       // var State = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(5).text()
       // var Location = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(6).text()
       // var ProjectType = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(7).text()

       // var Status = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(8).text()
       // var Created = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(9).text()
       // var Completed = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(10).text()
       // var Avaiable = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(13).text()
       // var notAvailiable = $("#IdTblHydraAvailabilityGrid tr").eq(Id).children().eq(14).text()
       //$("#IdTxtProjectCode").val(ProjectCode)
       //$("#IdTxtProjectName").val(ProjectType)
       // $("#IdTxtClient").val(Client)
       // $("#IdTxtState").val(State)
       // $("#IdTxtLocation").val(Location)
       // $("#IdTxtStatus").val(Status)
       // $("#IdTxtAvailable").val(Avaiable)
       // $("#IdTxtNotAvailable").val(notAvailiable)
       // MainLandingGridForHydraAvialabilityHistoryNew(PDID);
    }
    $(document).on('click', '#btnExport', function () {

        window.location.href = AbsolutePath("/HydraAvailibility/ExportToexcelHydraDetails?ProjectId=" + ProjectID)
    })
    $(document).on('click', ".editclass", function () {
        debugger
        // $(this).parent().next().next().css("border","solid red 2px")

        var PDID = $(this).parent().next().text()
        ProjectID = PDID
        
        $("#IdDivModelCheckSheet").modal({ backdrop: false });

        var ProjectCode = $(this).parent().next().next().text()
        var ProjectName = $(this).parent().next().next().next().next().text()
        var Client = $(this).parent().next().next().next().text()
        var State = $(this).parent().next().next().next().next().text()
        var Location = $(this).parent().next().next().next().next().next().text()
        var ProjectType = $(this).parent().next().next().next().next().next().next().text()

        var Status = $(this).parent().next().next().next().next().next().next().next().text()
        var Created = $(this).parent().next().next().next().next().next().next().next().next().text()
        var Completed = $(this).parent().next().next().next().next().next().next().next().next().next().text()

        var Employename = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().text()
        var Avaiable = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().text()
        var notAvailiable = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().next().text()
        var PName = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().next().next().text()
        
        $("#IdTxtProjectCode").val(ProjectCode)
        $("#IdTxtProjectName").val(ProjectType)
         $("#IdTxtClient").val(Client)
         $("#IdTxtState").val(State)
         $("#IdTxtLocation").val(Location)
         $("#IdTxtStatus").val(Status)
         $("#IdTxtAvailable").val(Avaiable)
         $("#IdTxtNotAvailable").val(notAvailiable)
         $("#IdTxtProjectnamenew").val(PName)
        MainLandingGridForHydraAvialabilityHistoryNew(PDID);
    })
   


    //===============================================================================//  

    function MainLandingGridForHydraAvailability(ProjectID)
    {
        var LoadStaticDataMainGrid = [
{ "ProjectDetailsId": "1", "Project_code": "C.005867 & C.005869", "Site_Client_Name": "Mr. K K Singh", "State_Name": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "NW-Rapid", "ProjectStatusName": "Completed", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
{ "ProjectDetailsId": "2", "Project_code": "C.005868 & C.005900", "Site_Client_Name": "Mr. Praveen", "State_Name": "Karnataka", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "Modular", "ProjectStatusName": "InProgress", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
{ "ProjectDetailsId": "3", "Project_code": "C.005869 & C.005901", "Site_Client_Name": "Mr. Suresh", "State_Name": "Tamilnadu", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "Modular", "ProjectStatusName": "Completed", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
{ "ProjectDetailsId": "4", "Project_code": "C.005870 & C.005902", "Site_Client_Name": "Mr. Mahesh", "State_Name": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "NW-Rapid", "ProjectStatusName": "InProgress", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" }];


      

        $("#IdTblHydraAvailabilityGrid").GridUnload();

        $("#IdTblHydraAvailabilityGrid").jqGrid
            ({
            caption: "Hydra Availability Details",
            url: AbsolutePath("/HydraAvailibility/ProjectInjfoForHydra?ProjectId=" + ProjectID),
            datatype: "json",
           mtype: "GET",
            //datatype: "local",
            //data: LoadStaticDataMainGrid,
            height: 'auto',
            width: '1002',
            viewrecords: true,
            rownumbers: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            sortname: "ProjectDetailsID",
            sortorder: "asc",
            pager: "#IdDivHydraAvailabilityPager",
            colModel: [
                //{
                //    name: 'Edit', width: 60, align: 'center', formatter: function view(a, b) {
                //        return "<span class='glyphicon glyphicon-pencil ClsViewEnquiryDetails' id='editImg_" + b.rowId + "'></span>";
                //    }, search: false,
                //},
               
                {
                    name: "View", align: "center", width: 80, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                        return "<i class='glyphicon glyphicon-file editclass'  title='Click Here For View' onClick='ViewHydraAvilablDetails(" + b.rowId + ")' ></i>"
                    }
                },
                { name: "ProjectDetailsId", index: "ProjectDetailsId", label: "ProjectDetailsId", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden:true},
                { name: "Project_code", index: "Project_code", label: "Project Code", width: "110", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "Site_Client_Name", index: "Site_Client_Name", label: "Client", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "State_Name", index: "State_Name", label: "State", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "Location", index: "Location", label: "Location", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "Plant_typeName", index: "Plant_typeName", label: "Project Type", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                { name: "ProjectStatusName", index: "ProjectStatusName", label: "Status", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false },
                { name: "Created_Date", index: "Created_Date", label: "Created Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
                { name: "HydraAvailabilitySubmittedDate", index: "HydraAvailabilitySubmittedDate", label: "Completed Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
                 { name: "HydraAvailabilityStatusName", index: "HydraAvailabilityStatusName", label: "HydraAvailabilityStatusName", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                  { name: "Company_Employee_Name", index: "Company_Employee_Name", label: "Company_Employee_Name", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
            { name: "Hydra_Available_Summary", index: "Hydra_Available_Summary", label: "Hydra_Available_Summary", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
    { name: "Hydra_NotAvailable_Summary", index: "Hydra_NotAvailable_Summary", label: "Hydra_NotAvailable_Summary", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
     { name: "ProjectName", index: "Project Name", label: "Hydra_NotAvailable_Summary", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
               ],
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                console.log(data)
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); }
        });
        
        $("#IdTblHydraAvailabilityGrid").navGrid("#IdDivHydraAvailabilityPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblHydraAvailabilityGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                enableClear: false,
                clearSearch: false
            });

        $("#IdTblHydraAvailabilityGrid").navButtonAdd("#IdDivHydraAvailabilityPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                // $("#IdTblHydraAvailabilityGrid").trigger('reloadGrid');
                debugger
                $("#IdTblHydraAvailabilityGrid").GridUnload();
                MainLandingGridForHydraAvailability(ProjectID)
            }
        });
        $("#IdTblHydraAvailabilityGrid").navButtonAdd("#IdDivHydraAvailabilityPager", {
            title: 'Export',
            caption: "",
            buttonicon: 'ui-icon-bookmark',
            onClickButton: function () {
              
                window.location.href = AbsolutePath("/HydraAvailibility/ExportToexcel");
            }
        });
    };


    MainLandingGridForHydraAvailability('@ViewBag.HydraAvailabilityProjectCode');

    //================================================================================//  

    function MainLandingGridForHydraAvialabilityHistory(PDID) {
        $("#IdDivHydraAvailabilityHistoryDetails").html("");

        $("#IdDivHydraAvailabilityHistoryDetails").append("<table id='IdTblHydraAvailabilityHistoryGrid'></table>")
        $("#IdDivHydraAvailabilityHistoryDetails").append("<div id='IdTblHydraAvailabilityHistoryPager'></div>")


       // var LoadStaticHydraHistoryDataMainGrid = [
       //{ "HydraAvailabilityHistoryIC": "1", "Date": "30-08-2018", "Status": "Available", "Idle Reason": " ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "From1": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", "Status": "Available", "Idle Reason": " ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "SecondFrom": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", },
       //{ "HydraAvailabilityHistoryIC": "2", "Date": "31-08-2018", "Status": "Idle", "Idle Reason": " Breakdown ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "From1": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", "Status": "Available", "Idle Reason": " ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "SecondFrom": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", },
       //{ "HydraAvailabilityHistoryIC": "3", "Date": "01-09-2018", "Status": "Idle", "Idle Reason": " ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "From1": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", "Status": "Available", "Idle Reason": " ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "SecondFrom": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", },
       //{ "HydraAvailabilityHistoryIC": "4", "Date": "02-09-2018", "Status": "Available", "Idle Reason": " ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "From1": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", "Status": "Available", "Idle Reason": " ", "Remarks": " ", "From": "09:00 AM", "To": "07:00 PM", "SecondFrom": "01:00 PM - 02:00 PM", "To": "07:00 PM", "Hours": "09:00:00 Hrs", },
       // ];

        //$("#IdTblHydraAvailabilityHistoryGrid").GridUnload();
        $("#IdTblHydraAvailabilityHistoryGrid").jqGrid({
            //caption:"Hydra Availability Details",
            url: AbsolutePath("/HydraAvailibility/HydraDetails?ProjectID=" + PDID),
            datatype: "json",
            mtype: "GET",
           // datatype: "local",
            //data: LoadStaticHydraHistoryDataMainGrid,
            height: 'auto',
            width: '700',
            viewrecords: true,
            rownumbers: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            sortname: "HydraAvailabilityDetailsId",
            sortorder: "asc",
            pager: "#IdTblHydraAvailabilityHistoryPager",
            colModel: [
                // {
                //     name: "Edit", align: "center", width: 80, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                //         return "<i class='glyphicon glyphicon-pencil' title='Click Here For Edit' onClick='$.EditOrUpdateHydraAvailabilityHistoryDetails(" + b.rowId + ")' ></i>"
                //     }
                // },
                //{
                //    name: "View", align: "center", width: 80, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                //        return "<i class='glyphicon glyphicon-file'  title='Click Here For View' onClick='$.ViewHydraAvailabilityHistoryDetails(" + b.rowId + ")' ></i>"
                //    }
                //},
                { name: "HydraAvailabilityDetailsId", index: "HydraAvailabilityHistoryIC", label: "HydraAvailabilityHistoryIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                {
                    name: "HydraDate", index: "HydraDate", label: "Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, formatter: "date", formatoptions: { srcformat: 'm/d/Y', newformat: 'd-M-Y' }, editable: true,
                    //editoptions: {
                    //    dataInit: function (element) {
                    //        $(element).datepicker({ "minDate": "+1D", "dateFormat": "dd-mm-yy" });
                    //    }
                    //}
                },
                { name: "HYdra1Status", index: "HYdra1Status", label: "Status", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true, edittype: 'select', editoptions: { value: { 1: '---Select---', 2: 'Idle', 3: 'Available' } } },
                {
                    name: "Hydra1IdleReason", index: "Hydra1IdleReason", label: "Idle Reason", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                    edittype: 'select',
                    editoptions: {
                        value: { 1: '---Select---', 2: 'Breakdown', 3: 'Unavailable', 4: 'Not Safe', 5: 'No Lifting Tools', 6: 'Not Corporating', 7: 'Client Work', 8: 'Un Skilled' }                       
                    }
                },
                 {
                     name: "Hydraone_Idle_reasonremarks", index: "Hydraone_Idle_reasonremarks", label: "Remarks", width: "200", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,

                 },
                { name: "Hydraone_From", index: "Hydraone_From", label: "From", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                  { name: "Hydraone_To", index: "Hydraone_To", label: "To", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                    //{ name: "From1", index: "LunchBreak", label: "From", width: "200", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                    //  { name: "To", index: "LunchBreak", label: "To", width: "200", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                { name: "Hydraone_Hours", index: "Hydraone_Hours", label: "Hours", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },

                 {
                     name: "Hydra2Status", index: "Hydra2Status", label: "Status", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                     edittype: 'select',
                     editoptions: {
                         value: { 1: '---Select---', 2: 'Idle', 3: 'Available' }                     
                     }
                 },
                 {
                     name: "Hydra2IdleReason", index: "Hydra2IdleReason", label: "Idle Reason", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                     edittype: 'select',
                     editoptions: {
                         value: { 1: '---Select---', 2: 'Breakdown', 3: 'Unavailable', 4: 'Not Safe', 5: 'No Lifting Tools', 6: 'Not Corporating', 7: 'Client Work', 8: 'Un Skilled' }
                     }
                 },
          {
              name: "HydraTwo_Idle_reasonremarks", index: "HydraTwo_Idle_reasonremarks", label: "Remarks", width: "200", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,

          },

                  { name: "HydraTwo_From", index: "HydraTwo_From", label: "From", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                  { name: "HydraTwo_To", index: "HydraTwo_To", label: "To", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                  { name: "HydraTwo_Hours", index: "Hours", label: "Hours", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
              
            ],
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                console.log("2")
                console.log(data)
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); }
        });
        //var $grid = $('#IdTblHydraAvailabilityHistoryGrid');
        //$grid.jqGrid('setCaption', 'Hydra Availability Details');
        $("#IdTblHydraAvailabilityHistoryGrid").navGrid("#IdTblHydraAvailabilityHistoryPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblHydraAvailabilityHistoryGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                enableClear: false,
                clearSearch: false
            });
        //$("#IdTblHydraAvailabilityHistoryGrid").navButtonAdd("#IdTblHydraAvailabilityHistoryPager", {
        //    title: 'Add',
        //    caption: "",
        //    buttonicon: 'ui-icon-plus',
        //    onClickButton: function () {
        //        debugger
        //        rowid = $("#IdTblHydraAvailabilityHistoryGrid").getDataIDs();

        //        if (rowid.length == 0) {
        //            var newRowId = 1
        //        }
        //        else {
        //            var newRowId = parseInt(rowid[(rowid.length - 1)]) + 1;
        //        }

        //        $("#IdTblHydraAvailabilityHistoryGrid").addRowData(newRowId, "last");
        //        $("#IdTblHydraAvailabilityHistoryGrid").editRow(newRowId);
        //        $("#" + newRowId + "_LunchBreak").val("01:00 PM - 02:00 PM").attr("disabled", true);
        //        $("#" + newRowId + "_Hours").attr("disabled", true);
        //        $("#" + newRowId + "_Hours_Hydra2").attr("disabled", true);

        //    }
        //});



        $("#IdTblHydraAvailabilityHistoryGrid").navButtonAdd("#IdTblHydraAvailabilityHistoryPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblHydraAvailabilityHistoryGrid").GridUnload();
                MainLandingGridForHydraAvialabilityHistory();
            }
        });

        $("#IdTblHydraAvailabilityHistoryGrid").jqGrid('setGroupHeaders', {
            useColSpanStyle: false,
            groupHeaders: [
              { startColumnName: 'HYdra1Status', numberOfColumns: 6, titleText: '<center class="bold">Hydra 1</center>' },
              { startColumnName: 'Hydra2Status', numberOfColumns: 6, titleText: '<center class="bold">Hydra 2</center>' },

            ]
        });
        //$("#IdTblHydraAvailabilityHistoryGrid").jqGrid('setGroupHeaders', {
        //    useColSpanStyle: false,
        //    groupHeaders: [
        //      { startColumnName: 'From1', numberOfColumns: 2, titleText: '<center class="bold1" >Lunch Break</center>' },
        //       { startColumnName: 'SecondFrom', numberOfColumns: 2, titleText: '<center class="bold1">Lunch Break</center>' },


        //    ]
        //});


    };
    function MainLandingGridForHydraAvialabilityHistoryNew(PDID) {

        var LoadStaticDataMainGrid = [
        { "HydraAvailabilityDetailsId": "1", "HydraNames": "Hydra1", "HydraDate": "11/7/2018", "Hydra_From": "9", "Hydra_To": "1 ", "Hydra_Hours": "5", "Category": "Available", "ManDays": "0.9", "Remarks": " " },
        { "HydraAvailabilityDetailsId": "1", "HydraNames": "Hydra2", "HydraDate": "11/7/2018", "Hydra_From": "9", "Hydra_To": "1 ", "Hydra_Hours": "5", "Category": "Not Available", "ManDays": "0.9", "Remarks": "Weather " }]
        $("#IdDivHydraAvailabilityHistoryDetails").html("");
        
        $("#IdDivHydraAvailabilityHistoryDetails").append("<table id='IdTblHydraAvailabilityHistoryGrid'></table>")
        $("#IdDivHydraAvailabilityHistoryDetails").append("<div id='IdTblHydraAvailabilityHistoryPager'></div >")


        $("#IdTblHydraAvailabilityHistoryGrid").jqGrid({
            caption: "Hydra Availability Details",
            url: AbsolutePath("/HydraAvailibility/HydraDetails?ProjectID=" + PDID),
            datatype: "json",
            mtype: "GET",
            //datatype: "local",
           // data: LoadStaticDataMainGrid,
            height: 'auto',
            width: '980',
            viewrecords: true,
            rownumbers: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            sortname: "HydraAvailabilityDetailsId",
            sortorder: "asc",
            pager: "#IdTblHydraAvailabilityHistoryPager",
            colModel: [               
                { name: "HydraAvailabilityDetailsId", index: "HydraAvailabilityHistoryIC", label: "HydraAvailabilityHistoryIC", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                {
                    name: "HydraNames", index: "HydraNames", label: "Hydra Name", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                  
                },
                { name: "HydraDate", index: "HydraDate", label: "Date", width: "80", align: "center", resizable: true, sortable: true, search: true, searchable: true, editable: true, edittype: 'select', formatter: "date", formatoptions: { srcformat: 'm/d/Y', newformat: 'd-M-Y' },align:'left' },
                
                 {
                     name: "Hydra_From", index: "From", label: "From", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,

                 },
                { name: "Hydra_To", index: "To", label: "To", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                  { name: "Hydra_Hours", index: "Hours", label: "Hours", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                    //{ name: "From1", index: "LunchBreak", label: "From", width: "200", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                    //  { name: "To", index: "LunchBreak", label: "To", width: "200", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },
                { name: "IdleReport_IdleReasonCategoryName", index: "Category", label: "Category", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true },

                 {
                     name: "ManDays", index: "ManDays", label: "Man Days", width: "50", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                     edittype: 'select',
                     editoptions: {
                         value: { 1: '---Select---', 2: 'Idle', 3: 'Available' }
                     }
                 },
                 {
                     name: "Remarks", index: "Details", label: "Remarks", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, editable: true,
                     edittype: 'select',
                     editoptions: {
                         value: { 1: '---Select---', 2: 'Breakdown', 3: 'Unavailable', 4: 'Not Safe', 5: 'No Lifting Tools', 6: 'Not Corporating', 7: 'Client Work', 8: 'Un Skilled' }
                     }
                 },         

            ],
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                console.log("2")
                console.log(data)
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); }
        });
    }
  
</script>