<?xml version="1.0" encoding="utf-8"?>
<!-- (c) ammap.com | SVG map of Curacao - HIgh -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:amcharts="http://amcharts.com/ammap" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
	<defs>
		<style type="text/css">
			.land
			{
				fill: #CCCCCC;
				fill-opacity: 1;
				stroke:white;
				stroke-opacity: 1;
				stroke-width:0.5;
			}
		</style>

		<amcharts:ammap projection="mercator" leftLongitude="-69.1624547" topLatitude="12.3927182" rightLongitude="-68.73046875" bottomLatitude="12.0312619264"></amcharts:ammap>

		<!-- All areas are listed in the line below. You can use this list in your script. -->
		<!--{id:"CW"}-->

	</defs>
	<g>
		<path id="CW" title="Curaçao" class="land" d="M697.68,662.5L743.72,659.49L782.92,651.72L799.4,640.34L789.24,622.95L723.56,555.71L680.52,519.39L666.08,496.07L660.58,457.74L649.52,433.65L622.74,417.18L389.73,359.84L310.73,321.43L242.79,256.37L225.79,218.41L206.45,130.47L185.91,94.89L152.51,65.55L118.88,44.83L39.65,10.94L29.27,1.69L20.09,0L10.99,6.93L0.6,23.88L15.27,56.08L18.66,92.12L14.37,160.05L23.17,185.77L44.32,202.71L69.67,216.1L91.34,231.81L148.22,324.81L172.82,354.37L201.86,365.38L237.53,368.69L267.85,381.16L280.71,419.65L304.41,460.44L359.26,505.07L420.35,540.93L515.23,569.48L611.83,635.72L660.58,659.03L697.68,662.5z"/>
	</g>
</svg>
