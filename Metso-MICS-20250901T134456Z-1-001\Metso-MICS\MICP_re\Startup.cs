﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Owin;
using Microsoft.Owin;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.OpenIdConnect;
using Microsoft.Owin.Security.Notifications;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using System.Web.Helpers;



[assembly: OwinStartupAttribute(typeof(Metso_MICP.Startup))]

namespace Metso_MICP
{
    public class Startup
    {
        // The Client ID (a.k.a. Application ID) is used by the application to uniquely identify itself to Azure AD
        string clientId = System.Configuration.ConfigurationManager.AppSettings["ClientId"];

        // RedirectUri is the URL where the user will be redirected to after they sign in
        string redirectUrl = System.Configuration.ConfigurationManager.AppSettings["redirectUrl"];

        // Tenant is the tenant ID (e.g. contoso.onmicrosoft.com, or 'common' for multi-tenant)
        static string tenant = System.Configuration.ConfigurationManager.AppSettings["Tenant"];

        // Authority is the URL for authority, composed by Azure Active Directory endpoint and the tenant name (e.g. https://login.microsoftonline.com/contoso.onmicrosoft.com)
        string authority = String.Format(System.Globalization.CultureInfo.InvariantCulture, System.Configuration.ConfigurationManager.AppSettings["Authority"], tenant);


        public void Configuration(IAppBuilder app)
        {
            //AntiForgeryConfig.UniqueClaimTypeIdentifier = ClaimTypes.NameIdentifier;

            app.SetDefaultSignInAsAuthenticationType(CookieAuthenticationDefaults.AuthenticationType);

            //app.UseCookieAuthentication(new CookieAuthenticationOptions());

            app.UseKentorOwinCookieSaver();
            app.UseCookieAuthentication(new CookieAuthenticationOptions
            {
                CookieSecure = CookieSecureOption.Always,
            });

          
            app.UseOpenIdConnectAuthentication(
                new OpenIdConnectAuthenticationOptions
                {
                    // Sets the ClientId, authority, RedirectUri as obtained from web.config
                    ClientId = clientId,
                    Authority = authority,
                    RedirectUri = redirectUrl,

                    // PostLogoutRedirectUri is the page that users will be redirected to after sign-out. In this case, it is using the home page
                    PostLogoutRedirectUri = redirectUrl,

                    //Scope is the requested scope: OpenIdConnectScopes.OpenIdProfileis equivalent to the string 'openid profile': in the consent screen, this will result in 'Sign you in and read your profile'
                    Scope = OpenIdConnectScope.OpenIdProfile,
                 
                    // ResponseType is set to request the id_token - which contains basic information about the signed-in user
                    ResponseType = OpenIdConnectResponseType.IdToken,

                    
                    // ValidateIssuer set to false to allow work accounts from any organization to sign in to your application
                    // To only allow users from a single organizations, set ValidateIssuer to true and 'tenant' setting in web.config to the tenant name or Id (example: contoso.onmicrosoft.com)
                    // To allow users from only a list of specific organizations, set ValidateIssuer to true and use ValidIssuers parameter
                    TokenValidationParameters = new TokenValidationParameters()
                    {
                        ValidateIssuer = false
                    },

                    SignInAsAuthenticationType = "Cookies",
                    
                    // OpenIdConnectAuthenticationNotifications configures OWIN to send notification of failed authentications to OnAuthenticationFailed method
                    Notifications = new OpenIdConnectAuthenticationNotifications
                    {



                        AuthorizationCodeReceived = OnAuthorizationCodeReceived,
                        AuthenticationFailed = OnAuthenticationFailed


                        //AuthenticationFailed =  context =>
                        //{
                        //    context.HandleResponse();
                        //    string redirect = "/Home/Error?message=" + context.Exception.Message;
                          

                        //    context.Response.Redirect(redirect);
                           
                        //    return Task.FromResult(0);
                        //},
                        //AuthorizationCodeReceived = async context =>
                        //{
                        //    var id = new ClaimsIdentity(context.AuthenticationTicket.Identity.AuthenticationType);
                        //    id.AddClaims(context.AuthenticationTicket.Identity.Claims);
                        //    var appToken = "MyToken";
                        //    id.AddClaim(new Claim("MyTokenKey", appToken));

                        //    context.AuthenticationTicket = new AuthenticationTicket
                        //    (
                        //         new ClaimsIdentity(id.Claims, context.AuthenticationTicket.Identity.AuthenticationType),
                        //           context.AuthenticationTicket.Properties
                        //    );
                        //}



                       
                    }
                }
            );
        }

        //private Task OnAuthenticationFailed(AuthenticationFailedNotification<OpenIdConnectMessage, OpenIdConnectAuthenticationOptions> context)
        //{
            
        //    context.HandleResponse();
        //    context.Response.Redirect("/?errormessage=" + context.Exception.Message);
        //    return Task.FromResult(0);
        //}

        private Task OnAuthorizationCodeReceived(AuthorizationCodeReceivedNotification notification)
        {
            notification.HandleResponse();
            string redirect = "/Home/Error?message=" + notification.Code;
            notification.Response.Redirect(redirect);
            return Task.FromResult(0);
        }

        private  Task OnAuthenticationFailed(AuthenticationFailedNotification<OpenIdConnectMessage,
        OpenIdConnectAuthenticationOptions> notification)
        {
            notification.HandleResponse();

            if (notification.Exception.Message.StartsWith("OICE_20004") || notification.Exception.Message.Contains("IDX10311") )
            {
                notification.SkipToNextMiddleware();
                return Task.FromResult(0);
            }
            //else if (notification.Exception.Message.Contains("IDX21323"))
            //{
            //    notification.HandleResponse();
            //   // string url='http://*************/MICS/Home/Index1';
            //    notification.OwinContext.Authentication.Challenge();
            //    return Task.FromResult(0);
            //}
            else
            {
                string redirect = "/Home/Error?message=" + notification.Exception.Message;

                notification.Response.Redirect(redirect);
                return Task.FromResult(0);
            }
        }

      

     




        //private async Task OnAuthorizationCodeReceived(AuthorizationCodeReceivedNotification context)
        //{
        //    var code = context.Code;

        //    ClientCredential credential = new ClientCredential(clientId, appKey);
        //    string userObjectID = context.AuthenticationTicket.Identity.FindFirst("http://schemas.microsoft.com/identity/claims/objectidentifier").Value;
        //    AuthenticationContext authContext = new AuthenticationContext(Authority, new NaiveSessionCache(userObjectID));

        //    // If you create the redirectUri this way, it will contain a trailing slash.  
        //    // Make sure you've registered the same exact Uri in the Azure Portal (including the slash).
        //    Uri uri = new Uri(HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Path));

        //    AuthenticationResult result = await authContext.AcquireTokenByAuthorizationCodeAsync(code, uri, credential, graphResourceId);
        //}


    }
}