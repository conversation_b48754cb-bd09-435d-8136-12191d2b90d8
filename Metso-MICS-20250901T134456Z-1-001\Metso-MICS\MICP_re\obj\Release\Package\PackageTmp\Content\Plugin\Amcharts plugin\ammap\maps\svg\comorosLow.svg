<?xml version="1.0" encoding="utf-8"?>
<!-- (c) ammap.com | SVG map of Comoros - Low -->
<svg xmlns="http://www.w3.org/2000/svg" xmlns:amcharts="http://amcharts.com/ammap" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
	<defs>
		<style type="text/css">
			.land
			{
				fill: #CCCCCC;
				fill-opacity: 1;
				stroke:white;
				stroke-opacity: 1;
				stroke-width:0.5;
			}
		</style>

		<amcharts:ammap projection="mercator" leftLongitude="43.2194" topLatitude="-11.3646" rightLongitude="44.5417" bottomLatitude="-12.4138"></amcharts:ammap>

		<!-- All areas are listed in the line below. You can use this list in your script. -->
		<!--{id:"KM-AN"},{id:"KM-GC"},{id:"KM-MO"}-->

	</defs>
	<g>
		<path id="KM-AN" title="Anjouan" class="land" d="M788.66,634.3L799.37,542.37L761.6,437.41L727.94,450.59L704.64,509.66L602.82,497.33L711.78,554.14L788.66,634.3z"/>
		<path id="KM-GC" title="Grande Comore" class="land" d="M84.65,0L36.42,11.38L0.63,241.85L33.05,290.37L134.57,354.7L171.5,323.85L152.17,286.31L97.69,151.55L108.5,24.17L84.65,0z"/>
		<path id="KM-MO" title="Moheli" class="land" d="M348.42,589.24L255.23,544.91L273.17,616.95L390.71,628.72L348.42,589.24z"/>
	</g>
</svg>
