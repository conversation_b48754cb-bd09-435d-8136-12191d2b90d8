﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Settings</title>


<style>    
     .valdation 
     {
       border:solid red 1px;
     }
     #ItemTypesDetailsDetailsPager_right > div {
    font-weight: normal;
    height: 19px;
    margin-top: 3px;
    margin-right: 20px !important;
}
   #IdDivElectricalCommissioningCheckListPager_right > div {

 font-weight: normal;
    height: 19px;
    margin-top: 3px;
    margin-right: 20px !important;
}
</style>
</head>
<body>
    <div class="row">
        <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12"></div>
        <div class="col-lg-5 col-md-5 col-sm-5 col-xs-12">
            <div class="input-group">
                <span class="input-group-addon">Select Master</span>
                <select class="form-control valid_DropDown SelectStyle js-example-basic-single" id="IdSlcMaster">
                    <option value="1">Plant Type</option>
                    <option value="2">ZeroDateMileStone</option>
                    <option value="3">Hydra Availability Status</option>
                    <option value="4">Hydra Availability Idle Reason</option>
                    <option value="5">IdleReport IdleReason Category</option>
                    <option value="6">Commission Activity</option>
                    <option value="7">Commission Error Base</option>
                    <option value="9">Installation Protocol Specs</option>
                    <option value="10">Contractor</option>
                    <option value="11">Idle Report Designation</option>
                    <option value="12">Item types</option>
                    <option value="13">Models</option>
                    <option value="14">Electrical Commissioning</option>

                </select>
            </div>
        </div>
    </div>
    <br />
    <div class="row">
        <div class="col-md-12 col-sm-12 col-xs-12" id="DivMasterGrid" style="width: 100%; overflow-x: auto">
        </div>
    </div>
    @*//============================================================================================//*@
    <div class="modal fade" data-backdrop="false" data-keyboard="false" id="IdDivModelViewForAttachment" role="dialog" style="position: absolute !important; overflow-y: scroll;">
        <div class="modal-dialog modal-lg" style="width: 97%">
            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close closeProject" id="btnCLoseX">&times;</button>
                    <button type="button" class="Colsemodal" style="display: none" data-dismiss="modal"></button>
                    <h4 class="modal-title">Installation Protocol Specs Details</h4>
                </div>
                <div id="IDDivProjectModalBody" class="modal-body" style="overflow-y: scroll; height: 565px;">

                    <div class="row">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <label for="IdModelName" class="ClsLabelCustTxtBox ">Model Name</label>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <select class="form-control valid_DropDown SelectStyle" id="IdModelName">
                            </select>
                        </div>

                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <label for="IdTxtCreatedBy" class="ClsLabelCustTxtBox ">Number fo Attachments</label>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <select class="form-control valid_DropDown SelectStyle" id="IdNumberfoAttachments">
                                <option value="0">0</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>

                            </select>
                        </div>
                    </div>
                    <br />
                    <div class="row">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <label for="IdModelName" class="ClsLabelCustTxtBox">Is Active?</label>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <input type='checkbox' value='' id='IdIsActiveIPS' />
                        </div>
                    </div>
                    @*/==================== attachments ========================/*@

                    <div class="panel panel-default" id="IdDivAttachment1" style="margin-top: 6px">
                        <div class="panel-heading accordion-toggle collapsed" style="box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer" data-toggle="collapse" data-parent="#accordion" data-target="#IDIdDivAttachment1Details">
                            <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;">Attachment-1</h4>
                        </div>
                        <div id="IDIdDivAttachment1Details" style="font-family: Arial; font-size: 13px; color: black;" class="panel-collapse collapse">
                            <div class="panel-body">
                                <div class="row" style="margin-top: 6px">
                                    <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12" id="">
                                        <label for="IdModelName" class="ClsLabelCustTxtBox ">Attachment</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <input type="file" id="IdTxtAttachment1" accept="image/x-png,image/jpeg"/>
                                    </div>

                                     <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12" style="text-align:right">
                                        <label for="IdTxtCreatedBy" class="ClsLabelCustTxtBox ">Spec Point</label>
                                    </div>

                                     <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                        <textarea rows="2" cols="3" class="form-control" id="IdTxtSpecPoint1"></textarea>
                                    </div>

                                      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12" style="text-align:right">
                                        <label for="IdTxtDescription1" class="ClsLabelCustTxtBox ">Description</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                                        <textarea rows="2" cols="3" class="form-control" id="IdTxtDescription1"></textarea>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>
                                    <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">
                                        <div id="DivAttachment1Img1">
                                            @*<img src="" style="height: 100%; width: 100%" id="Attachment1Img1" />*@
                                        </div>
                                       @* <div id="DivAttachment1Img2">
                                            <img src="~/Assets/Images/save2.png" style="height: 100%; width: 100%" />
                                        </div>*@
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default" id="IdDivAttachment2">
                        <div class="panel-heading accordion-toggle collapsed" style="box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer" data-toggle="collapse" data-parent="#accordion" data-target="#IDIdDivAttachment2Details">
                            <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;">Attachment-2</h4>
                        </div>
                        <div id="IDIdDivAttachment2Details" style="font-family: Arial; font-size: 13px; color: black;" class="panel-collapse collapse">
                            <div class="panel-body">
                                <div class="row" style="margin-top: 6px" id="">

                                    <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12">
                                        <label for="IdModelName" class="ClsLabelCustTxtBox ">Attachment</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                                        <input type="file" id="IdTxtAttachment2"  accept="image/x-png,image/jpeg"/>
                                    </div>

                                     <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12" style="text-align:right">
                                        <label for="IdTxtCreatedBy" class="ClsLabelCustTxtBox ">Spec Point</label>
                                    </div>
                                     <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                                        <textarea rows="2" cols="3" class="form-control" id="IdTxtSpecPoint2"></textarea>
                                    </div>

                                     <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12" style="text-align:right">
                                        <label for="IdTxtDescription" class="ClsLabelCustTxtBox ">Description</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                                        <textarea rows="2" cols="3" class="form-control" id="IdTxtDescription2"></textarea>
                                    </div>


                                </div>
                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>
                                    <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">
                                        <div id="DivAttachment2Img1">
                                            @*<img src="" style="height: 100%; width: 100%" id="Attachment2Img1" />*@
                                        </div>
                                       @* <div id="DivAttachment2Img2">
                                            <img src="~/Assets/Images/save2.png" style="height: 100%; width: 100%"  />
                                        </div>*@
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default" id="IdDivAttachment3">
                        <div class="panel-heading accordion-toggle collapsed" style="box-shadow: 2px 2px 2px rgba(255,255,255,.4) inset, inset -2px -2px 2px rgba(0,0,0,.4); font-weight: bolder; background-color: #d9efef; color: #327d78; cursor: pointer" data-toggle="collapse" data-parent="#accordion" data-target="#IDIdDivAttachment3Details">
                            <h4 class="panel-title text-center" style="font-family: Arial; font-size: 16px; font-weight: bold; color: #327d78;">Attachment-3</h4>
                        </div>
                        <div id="IDIdDivAttachment3Details" style="font-family: Arial; font-size: 13px; color: black;" class="panel-collapse collapse">
                            <div class="panel-body">
                                <div class="row" style="margin-top: 6px" id="">
                                    <div class="col-lg-1 col-md-1 col-sm-12 col-xs-12">
                                        <label for="IdModelName" class="ClsLabelCustTxtBox ">Attachment</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                                        <input type="file" id="IdTxtAttachment3"  accept="image/x-png,image/jpeg"/>
                                    </div>

                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12" style="text-align:right">
                                        <label for="IdTxtCreatedBy" class="ClsLabelCustTxtBox ">Spec Point</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                                        <textarea rows="2" cols="3" class="form-control" id="IdTxtSpecPoint3"></textarea>
                                    </div>

                                     <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12" style="text-align:right">
                                        <label for="IdTxtDescription" class="ClsLabelCustTxtBox ">Description</label>
                                    </div>
                                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                                        <textarea rows="2" cols="3" class="form-control" id="IdTxtDescription3"></textarea>
                                    </div>

                                </div>
                                <div class="row">
                                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>
                                    <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">
                                        <div id="DivAttachment3Img1">
                                            @*<img src="" style="height: 100%; width: 100%" id="Attachment3Img1"  />*@
                                        </div>
                                     @*   <div id="DivAttachment3Img2">
                                            <img src="~/Assets/Images/save2.png" style="height: 100%; width: 100%" />
                                        </div>*@
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <br />
                    <div class='row'><div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' style='background-color: #8bcaca; color: #327d78; width: 97%; margin-left: 13px; font-weight: bolder'><span>Check Points</span></div></div><br />

                    <div class="row">
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12"></div>
                    <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12">
                        <div id="DivCheckpointsGrid" style="width: 100%; overflow-x: auto"></div>
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-12"></div>
                    </div>

                </div>

                <div class="modal-footer">
                    <button type="button" id="IdBtnSubmit" class="btn ButtonStyle ClsEnquiryFrombtn">Save</button>
                    <button type="button" id="IdBtnCancel" class="btn ButtonStyle ClsEnquiryFrombtn closeProject">Cancel</button>
                </div>

            </div>
        </div>
    </div>


    @*//=======================================Model Contractor=======================================================//*@

    <div class="modal fade ClsDivModelView" id="IdDivModelContractor" role="dialog" style="position: absolute !important;">
        <div class="modal-dialog modal-lg" style="width: 97%">
            <!-- Modal content-->
            <div class="modal-content">

                <div class="modal-header">
                     <button type="button" class="close closeProject" id="IdbtnCloseArrow">&times;</button>
                    <button type="button" class="Colsemodal" style="display: none" data-dismiss="modal"></button>                    
                    <h4 class="modal-title">Contractor Details</h4>


                </div>
                <div id="IDDivSheetModalBody" class="modal-body">
                    <div id="IdDivCheckSheetDetailForm">
                        <br />
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtContractorName" class="ClsLabelCustTxtBox ">Contractor Name</label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <div class="input-group">
                                    <input id="IdTxtContractorName" type="text" style="background-color: transparent" class="form-control input-sm ClsTxtCommonChkSheetDetails" autofocus="autofocus" />

                                </div>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <label for="IdTxtEnquiryDate" class="ClsLabelCustTxtBox ">Is Active ?</label>
                            </div>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                <input type='checkbox' value='' id='IdIsActiveContractor' />
                            </div>                          
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="background-color: #8bcaca; color: #1f716c; width: 97%; margin-left: 13px; font-weight: bolder;">
                                Site Contractor Details
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12"></div>
                            <div class="col-lg-9 col-md-9 col-sm-9 col-xs-12">
                                <div style="width: inherit; overflow: auto;" id="IdDivGridCheckSheet">
                                    <table id='IdTblSiteContractorGrid'></table>
                                    <div id='IdTblSiteContractorPager'></div>
                                </div>
                            </div>
                        </div>
                        <br />

                        <br />
                        <br />


                    </div>
                    <div class="modal-footer">
                        <button type="button" id="txtContractDetails" class="btn ButtonStyle ClsEnquiryFrombtn">Save</button>
                        @* <button type="button" id="btnGenerateQtnDetails" class="btn ButtonStyle ClsEnquiryFrombtn" data-dismiss="modal">Reset</button>*@
                        <button type="button" id="btnCLoseEnqForm" class="btn ButtonStyle ClsEnquiryFrombtn closeCheckSheet">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
<script>

    function AlertMessage(msg, msgType)
    {
        swal({
            title: msg,
            type: msgType,
            showCancelButton: false,
            confirmButtonText: "OK",
            closeOnConfirm: true,
            confirmButtonColor: "#e6592e"

        },
        function () {
            window.onkeydown = null;
            window.onfocus = null;
        });
};


    function view(a, b)
    {
        return "<span class='glyphicon glyphicon-pencil ClsEditContactDetails'></span>";
    };

    function IsActive(cellValue, options, rowobject, action) {
      
        if (cellValue == undefined) {
            var checked = "checked";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='IDISActiveClass'  disabled='disabled' />";


            return a;

        }
        else {
            var checked = cellValue == true ? " checked='checked' " : "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='IDISActiveClass'  disabled='disabled' />";


            return a;
        }
    };
    function IsActiveforCheckPoints(cellValue, options, rowobject, action)
    {
        if (cellValue == undefined) {
            var checked = "checked";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='IDISActiveClassforcheck'  disabled='disabled' />";


            return a;

        }
        else {
            var checked = cellValue == true ? " checked='checked' " : "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='IDISActiveClassforcheck'  disabled='disabled' />";
            return a;
        }
    };
    

    //bharath
    function ISApplicableforNWS(cellValue, options, rowobject, action) {
        if (cellValue == undefined)
        {
            var checked = "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforNWSClass enable' />";


            return a;

        }
        else
        {
            var checked = rowobject.ISApplicablefor_NWS == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforNWSClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforNWR(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforNWRClass enable' />";


            return a;

        }
        else {
            //var checked = rowobject.ISApplicablefor_NWR == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforNWRClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforHYB(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforHYBClass enable' />";
            return a;
        }
        else {
            //var checked = rowobject.ISApplicablefor_HYB == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforHYBClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforMOD(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforMODClass enable' />";


            return a;

        }
        else {
            // var checked = rowobject.ISApplicablefor_MOD == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforMODClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforTRACK(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforTRACKClass enable' />";


            return a;

        }
        else {
            //  var checked = cellValue == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforTRACKClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforAC(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforACClass enable' />";


            return a;

        }
        else {
            // var checked = rowobject.ISApplicablefor_AC == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforACClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforHRC(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforHRCClass enable' />";


            return a;

        }
        else {
            //  var checked = rowobject.ISApplicablefor_HRC == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforHRCClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforRWK(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforRWKClass enable' />";


            return a;

        }
        else
        {
            // var checked = rowobject.ISApplicablefor_RWK == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforRWKClass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforUnit(cellValue, options, rowobject, action)
    {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforUnitclass enable' />";


            return a;

        }
        else {
            //  var checked = rowobject.ISApplicablefor_Unit == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforUnitclass enable'  disabled='disabled' />";
            return a;
        }

    };
    function ISApplicableforother(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforotherclass enable' />";
            return a;
        }
        else {
            //  var checked = rowobject.ISApplicablefor_other == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableforotherclass enable'  disabled='disabled' />";
            return a;
        }

    };

    EditFunction = function (e, gridname)
    {
        debugger
        Ischeckedornotbeforeresave = 0
        Ischeckedornotbeforeresavecheck=0
        var id = $(e).parent().parent().attr("id");
      
        $("#" + gridname).editRow(id);
        $($(".ClsChkQuotAccess")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforNWSClass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforNWRClass")[id - 1]).attr("disabled", false);

        $($(".ISApplicableforHYBClass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforMODClass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforTRACKClass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforACClass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforHRCClass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforRWKClass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforUnitclass")[id - 1]).attr("disabled", false);
        $($(".ISApplicableforotherclass")[id - 1]).attr("disabled", false);

        $($(".ISApplicableForElectricalCommissioningCheckUP")[id - 1]).attr("disabled", false);

        $($(".ISApplicableForElectricalPreCommissioningCheckUP")[id - 1]).attr("disabled", false);

        $($(".ISApplicableForElectricalInstallationCheckUP")[id - 1]).attr("disabled", false);

       debugger
        $($(".IDISActiveClass")[id - 1]).attr("disabled", false);
       
        if ($($(".IDISActiveClass")[id - 1]).prop("checked") == true)
        {
            Ischeckedornotbeforeresave = 1
        }
        else {
            Ischeckedornotbeforeresave = 0
        }
        $($(".IDISActiveClassforcheck")[id - 1]).attr("disabled", false);
       
        if ($($(".IDISActiveClassforcheck")[id - 1]).prop("checked") == true) {
            Ischeckedornotbeforeresavecheck = 1
        }
        else {
            Ischeckedornotbeforeresavecheck = 0
        }
        
       // $($("#" + gridname + " tr")[id]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-repeat ClsRefContactDetails");

    }



    function IS_ApplicableFor_Electrical_CommissioningCheckUP(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableForElectricalCommissioningCheckUP enable' />";


            return a;

        }
        else {
            //  var checked = rowobject.ISApplicablefor_other == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableForElectricalCommissioningCheckUP enable'  disabled='disabled' />";
            return a;
        }
    };
    function IS_ApplicableFor_Electrical_PreCommissioningCheckUP(cellValue, options, rowobject, action) {
        if (cellValue == undefined) {
            var checked = "";

            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableForElectricalPreCommissioningCheckUP enable' />";


            return a;

        }
        else {
            //  var checked = rowobject.ISApplicablefor_other == true ? " checked='checked' " : "";
            var checked = cellValue == true ? " checked='checked' " : "";
            var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableForElectricalPreCommissioningCheckUP enable'  disabled='disabled' />";
            return a;
        }

    };
    function IS_ApplicableFor_Electrical_InstallationCheckUP(cellValue, options, rowobject, action)
    {
    
        if (cellValue == undefined)
        {
                    var checked = "";

                    var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableForElectricalInstallationCheckUP enable' />";

                    return a;

          }
        else
        {
                    //  var checked = rowobject.ISApplicablefor_other == true ? " checked='checked' " : "";
                    var checked = cellValue == true ? " checked='checked' " : "";
                    var a = "<input type='checkbox' " + checked + " value='" + cellValue + "' class='ISApplicableForElectricalInstallationCheckUP enable'  disabled='disabled' />";
                    return a;
         }


    };

    //===========================================================start=================================//
    $("#IdToAppend").html("");

    $("#IdSlcMaster").change(function (e) {
        var Mastergridtodisplay = $("#IdSlcMaster option:selected").val()

        switch (Mastergridtodisplay) {
            case "1":
                $.PlantType();
                break;
            case "2":
                $.ZeroDateMileStone();
                break;
            case "3":
                $.HydraAvailabilityStatus();
                break;
            case "4":
                $.HydraAvailabilityIdleReason();
                break;
            case "5":
                $.IdleReportIdleReasonCategory();
                break;
            case "6":
                $.CommissionActivity();
                break;
            case "7":
                $.CommissionErrorBase();
                break;
            case "8":
                $.InstallationProtocolCheckPoints();
                break;
            case "9":
                $.InstallationProtocolSpecs();
                break;
            case "10":
                $.Contractor();
                break;
            case "11":
                $.IdleReportDesignation();
                break;
            case "12":
                $.Itemtypes();
                break;
            case "13":
                $.Models();
                break;
            case "14":
                $.ElectricalCommissioningCheck();
                break;
            default:
                $.PlantType();
                break;
        }

    });

   

    //=========================================  PlantType   ===============================

    $.PlantType = function ()
    {
        var PalntTypeList = [];
        var PalntTypeDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetPlantTypeListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    PalntTypeList = resp
                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblPlantTypeDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivPlantTypeDetailsPager'></div>")
        $("#IdTblPlantTypeDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetPlantTypeDetails"),        
            datatype: "Json",       
            mtype: "GET",
            height: 'auto',
            width:500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "Plant_typeName",
            sortorder: "desc",
            pager: "#IdDivPlantTypeDetailsPager",
            loadonce: true,            
            colNames: ['Edit', ' Planttype Id', ' Plant Type', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {name: 'Plant_typeId', width: 90, align: 'center', editable: true, hidden: true},
                {
                    name: 'Plant_typeName', width: 250, align: 'left', editable: true,
                    editoptions: {
                        dataEvents: [
                            {
                                type: "focus",
                                fn: function ()
                                {
                                    $(this).css("border", "");
                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < PalntTypeList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == PalntTypeList[i].Plant_typeName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Plant Type Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },               
                { name: 'is_Active', width: 50, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function ()
            {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblPlantTypeDetailsGrid").navGrid("#IdDivPlantTypeDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblPlantTypeDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,          
                defaultSearch: "cn",
                //sopt: ["cn", "eq", "neq"],
                //resetIcon: "",
                ignoreCase: true
            });

        $("#IdTblPlantTypeDetailsGrid").navButtonAdd("#IdDivPlantTypeDetailsPager",
            {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblPlantTypeDetailsGrid").jqGrid("GridUnload");
                $.PlantType();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblPlantTypeDetailsGrid").navButtonAdd("#IdDivPlantTypeDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblPlantTypeDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblPlantTypeDetailsGrid").addRowData(newRowId, {});
                $("#IdTblPlantTypeDetailsGrid").editRow(newRowId);
                $($("#IdTblPlantTypeDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblPlantTypeDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblPlantTypeDetailsGrid").navButtonAdd("#IdDivPlantTypeDetailsPager",
            {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function ()
            {               
                var rowid = $("#IdTblPlantTypeDetailsGrid").getDataIDs();
                var PlanttypeIdData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblPlantTypeDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        var PlantTypeName = $("#IdTblPlantTypeDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblPlantTypeDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var PlantTypeIc = 0;
                        if (IsUpdate) {
                            PlantTypeIc = $("#IdTblPlantTypeDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            PlantTypeIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true)
                        {
                            isActive = 1
                        }
                        else
                        {
                            isActive = 0
                        }
                       
                        var IsValidateBit = 0
                        if (PlantTypeName != "")
                        {
                            var PlanttypeValues =
                                {
                                    Plant_typeId: PlantTypeIc,
                                    Plant_typeName: PlantTypeName,
                                    is_Active: isActive,
                                    Isupdate: IsUpdate,
                                    Remarks: "No Remarks",
                                }
                            PlanttypeIdData.push(PlanttypeValues)
                            IsValidateBit = 0
                        }
                        else {
                            IsValidateBit++
                        }
                    }
                    PlantTypeName == "" ? $("#IdTblPlantTypeDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblPlantTypeDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }                
                PalntTypeDuplicateList = PlanttypeIdData
             
                for (var i = 0; i < PlanttypeIdData.length; i++)
                {
                    for (var j = 0; j < PalntTypeDuplicateList.length; j++)
                    {
                        if (i != j)
                        {
                            if (PlanttypeIdData[i].Plant_typeName.toString().toUpperCase().trim() == PalntTypeDuplicateList[j].Plant_typeName.toString().toUpperCase().trim())
                            {
                                isDuplicaterow++
                            }

                        }
                        else {


                        }
                    }
                }




                if (Rowselected != 0)
                {
                    var _formData = new FormData();
                    _formData.append("PlanttypeData", JSON.stringify(PlanttypeIdData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0)
                        {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertPlantTypeDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp)
                                {
                                  //  $("#IdTblPlantTypeDetailsGrid").jqGrid("GridUnload");
                                    AlertMessage("Records Saved Successfully", "success");
                                    $("#IdTblPlantTypeDetailsGrid").jqGrid("GridUnload");
                                    $.PlantType();
                                    PalntTypeList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")
                }

            }
        });

        $(document).on('click', '#IdTblPlantTypeDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblPlantTypeDetailsGrid");
        })

        $(document).on('click', "#IdTblPlantTypeDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblPlantTypeDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblPlantTypeDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblPlantTypeDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblPlantTypeDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblPlantTypeDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );
    }
    $.PlantType();
    //=========================================  ZeroDateMileStone   ===============================

    $.ZeroDateMileStone = function () {
        var ZeroDateMileStoneList = [];
        var ZeroDateMileStoneDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetZeroDateMileStoneListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    ZeroDateMileStoneList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblZeroDateMileStoneDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivZeroDateMileStoneDetailsPager'></div >")
        $("#IdTblZeroDateMileStoneDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetZeroDateMileStoneDetails"),          
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width: 500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "ZeroDateMileStoneName",
            sortorder: "desc",
            pager: "#IdDivZeroDateMileStoneDetailsPager",
            loadonce: true,
            colNames: ['Edit', ' ZeroDateMileStoneId', ' ZeroDateMileStone', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'ZeroDateMileStoneId', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    name: 'ZeroDateMileStoneName', width: 250, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < ZeroDateMileStoneList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == ZeroDateMileStoneList[i].ZeroDateMileStoneName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("ZeroDate MileStone Name Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },

                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function (data) {
                console.log("Hi");
                console.log(data);
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblZeroDateMileStoneDetailsGrid").navGrid("#IdDivZeroDateMileStoneDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblZeroDateMileStoneDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblZeroDateMileStoneDetailsGrid").navButtonAdd("#IdDivZeroDateMileStoneDetailsPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblZeroDateMileStoneDetailsGrid").jqGrid("GridUnload");
                $.ZeroDateMileStone();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblZeroDateMileStoneDetailsGrid").navButtonAdd("#IdDivZeroDateMileStoneDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblZeroDateMileStoneDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblZeroDateMileStoneDetailsGrid").addRowData(newRowId, {});
                $("#IdTblZeroDateMileStoneDetailsGrid").editRow(newRowId);
                $($("#IdTblZeroDateMileStoneDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblZeroDateMileStoneDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblZeroDateMileStoneDetailsGrid").navButtonAdd("#IdDivZeroDateMileStoneDetailsPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {               
                var rowid = $("#IdTblZeroDateMileStoneDetailsGrid").getDataIDs();
                var ZeroDateMileStoneData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
              
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblZeroDateMileStoneDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        var ZeroDateMileStoneName = $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var ZeroDateMileStoneIc = 0;
                        if (IsUpdate) {
                            ZeroDateMileStoneIc = $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            ZeroDateMileStoneIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else
                        {
                            isActive = 0

                        }
                        var IsValidateBit = 0
                        if (ZeroDateMileStoneName != "") {

                            var ApplicationValues =
                                {
                                    ZeroDateMileStoneId: ZeroDateMileStoneIc,
                                    ZeroDateMileStoneName: ZeroDateMileStoneName,
                                    IsActive: isActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No remark"
                                }

                            ZeroDateMileStoneData.push(ApplicationValues)
                            IsValidateBit = 0
                        }
                        else {

                            IsValidateBit++
                        }

                    }
                    ZeroDateMileStoneName == "" ? $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }

                ZeroDateMileStoneDuplicateList = ZeroDateMileStoneData

                for (var i = 0; i < ZeroDateMileStoneData.length; i++)
                {
                    for (var j = 0; j < ZeroDateMileStoneDuplicateList.length; j++) {
                        if (i != j) {
                            if (ZeroDateMileStoneData[i].ZeroDateMileStoneName.toString().toUpperCase().trim() == ZeroDateMileStoneDuplicateList[j].ZeroDateMileStoneName.toString().toUpperCase().trim()) {
                                isDuplicaterow++
                            }

                        }
                        else{
                        }
                    }
                }


          
                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("ZeroDateMileStoneData", JSON.stringify(ZeroDateMileStoneData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertZeroDateMileStoneeDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblZeroDateMileStoneDetailsGrid").jqGrid("GridUnload");
                                    $.ZeroDateMileStone();
                                    ZeroDateMileStoneList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblZeroDateMileStoneDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblZeroDateMileStoneDetailsGrid");
        })

        $(document).on('click', "#IdTblZeroDateMileStoneDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblZeroDateMileStoneDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblZeroDateMileStoneDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblZeroDateMileStoneDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblZeroDateMileStoneDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );
    }

    //============================================= Hydra Availability Status  ==============================================


    $.HydraAvailabilityStatus = function () {
        var HydraAvailabilityList = [];
        var HydraAvailabilityDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetHydraAvailabilityStatusListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    HydraAvailabilityList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblHydraAvailabilityDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivHydraAvailabilityDetailsPager'></div >")
        $("#IdTblHydraAvailabilityDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetHydraAvailabilityStatusDetails"),
           // caption: "Hydra Availability Status",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width: 500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "HydraAvailabilityStatusName",
            sortorder: "desc",
            pager: "#IdDivHydraAvailabilityDetailsPager",
            loadonce: true,
            colNames: ['Edit', ' Hydra Availability StatusId', ' Hydra Availability Status', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'HydraAvailabilityStatusId', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    name: 'HydraAvailabilityStatusName', width: 250, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < HydraAvailabilityList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == HydraAvailabilityList[i].HydraAvailabilityStatusName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Hydra Availability Status Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },

                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblHydraAvailabilityDetailsGrid").navGrid("#IdDivHydraAvailabilityDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblHydraAvailabilityDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                ////enableClear: false,
                ////clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblHydraAvailabilityDetailsGrid").navButtonAdd("#IdDivHydraAvailabilityDetailsPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblHydraAvailabilityDetailsGrid").jqGrid("GridUnload");
                $.HydraAvailabilityStatus();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblHydraAvailabilityDetailsGrid").navButtonAdd("#IdDivHydraAvailabilityDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblHydraAvailabilityDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblHydraAvailabilityDetailsGrid").addRowData(newRowId, {});
                $("#IdTblHydraAvailabilityDetailsGrid").editRow(newRowId);
                $($("#IdTblHydraAvailabilityDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblHydraAvailabilityDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblHydraAvailabilityDetailsGrid").navButtonAdd("#IdDivHydraAvailabilityDetailsPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var rowid = $("#IdTblHydraAvailabilityDetailsGrid").getDataIDs();
                var HydraAvailabilityData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblHydraAvailabilityDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        var HydraAvailabilityStatusName = $("#IdTblHydraAvailabilityDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblHydraAvailabilityDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var HydraAvailabilityIc = 0;
                        if (IsUpdate) {
                            HydraAvailabilityIc = $("#IdTblHydraAvailabilityDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            HydraAvailabilityIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0
                        if (HydraAvailabilityStatusName != "") {
                            var ApplicationValues =
                                {
                                    HydraAvailabilityStatusId: HydraAvailabilityIc,
                                    HydraAvailabilityStatusName: HydraAvailabilityStatusName,
                                    IsActive: isActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No remark"
                                }

                            HydraAvailabilityData.push(ApplicationValues)
                            IsValidateBit = 0
                        }
                        else {

                            IsValidateBit++
                        }

                    }
                    HydraAvailabilityStatusName == "" ? $("#IdTblHydraAvailabilityDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblHydraAvailabilityDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }

                HydraAvailabilityDuplicateList = HydraAvailabilityData



                for (var i = 0; i < HydraAvailabilityData.length; i++)
                {
                    for (var j = 0; j < HydraAvailabilityDuplicateList.length; j++) {
                        if (i != j) {
                            if (HydraAvailabilityData[i].HydraAvailabilityStatusName.toString().toUpperCase().trim() == HydraAvailabilityDuplicateList[j].HydraAvailabilityStatusName.toString().toUpperCase().trim()) {
                                isDuplicaterow++
                            }
                        }
                        else {
                        }
                    }
                }


                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("HydraAvailabilityData", JSON.stringify(HydraAvailabilityData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertHydraAvailabilityStatusDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblHydraAvailabilityDetailsGrid").jqGrid("GridUnload");
                                    $.HydraAvailabilityStatus();
                                    HydraAvailabilityList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblHydraAvailabilityDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblHydraAvailabilityDetailsGrid");
        })

        $(document).on('click', "#IdTblHydraAvailabilityDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblHydraAvailabilityDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblHydraAvailabilityDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblHydraAvailabilityDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblHydraAvailabilityDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblHydraAvailabilityDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );


    }

    //=========================================  Hydra AvailabilityIdleReason   ===============================

    $.HydraAvailabilityIdleReason = function ()
    {
        var HydraAvailabilityIdleReasonList = [];
        var HydraAvailabilityIdleReasonDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetHydraAvailabilityIdleReasoListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    HydraAvailabilityIdleReasonList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblHydraAvailabilityIdleReasonDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivHydraAvailabilityIdleReasonPager'></div >")
        $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetHydraAvailabilityIdleReasoDetails"),
          //  caption: "Hydra Availability IdleReason",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width: 500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "HydraAvailabilityIdleCategoryName",
            sortorder: "desc",
            pager: "#IdDivHydraAvailabilityIdleReasonPager",
            loadonce: true,
            colNames: ['Edit', ' HydraAvailabilityIdleReasonId', ' Hydra Availability IdleReason', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'HydraAvailabilityIdleCategoryId', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    name: 'HydraAvailabilityIdleCategoryName', width: 250, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < HydraAvailabilityIdleReasonList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == HydraAvailabilityIdleReasonList[i].HydraAvailabilityIdleCategoryName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Hydra Availability Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },

                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").navGrid("#IdDivHydraAvailabilityIdleReasonPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblHydraAvailabilityIdleReasonDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").navButtonAdd("#IdDivHydraAvailabilityIdleReasonPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").jqGrid("GridUnload");
                $.HydraAvailabilityIdleReason();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").navButtonAdd("#IdDivHydraAvailabilityIdleReasonPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    //  var newRowId = parseInt(rowidtoAdd[(rowidtoAdd.length - 1)]) + 1;
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").addRowData(newRowId, {});
                $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").editRow(newRowId);
                //  $("#" + newRowId + "_AppDescription").focus()
                $($("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").navButtonAdd("#IdDivHydraAvailabilityIdleReasonPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var rowid = $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").getDataIDs();
                var HydraAvailabilityIdleReasonData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        var HydraAvailabilityIdleReasonName = $("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var HydraAvailabilityIdleReasonIc = 0;
                        if (IsUpdate) {
                            HydraAvailabilityIdleReasonIc = $("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            HydraAvailabilityIdleReasonIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0
                        if (HydraAvailabilityIdleReasonName != "") {

                            var ApplicationValues =
                                {
                                    HydraAvailabilityIdleCategoryId: HydraAvailabilityIdleReasonIc,
                                    HydraAvailabilityIdleCategoryName: HydraAvailabilityIdleReasonName,
                                    IsActive: isActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No Remarks"
                                }

                            HydraAvailabilityIdleReasonData.push(ApplicationValues)
                            IsValidateBit = 0
                        }
                        else {

                            IsValidateBit++
                        }

                    }
                    HydraAvailabilityIdleReasonName == "" ? $("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }
                HydraAvailabilityIdleReasonDuplicateList = HydraAvailabilityIdleReasonData

                for (var i = 0; i < HydraAvailabilityIdleReasonData.length; i++) {
                    for (var j = 0; j < HydraAvailabilityIdleReasonDuplicateList.length; j++) {
                        if (i != j)
                        {
                            if (HydraAvailabilityIdleReasonData[i].HydraAvailabilityIdleReasonName.toString().toUpperCase().trim() == HydraAvailabilityIdleReasonDuplicateList[j].HydraAvailabilityIdleReasonName.toString().toUpperCase().trim())
                            {
                                isDuplicaterow++
                            }
                        }
                        else
                        {

                        }
                    }
                }


             
                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("HydraAvailabilityIdleReasonData", JSON.stringify(HydraAvailabilityIdleReasonData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertHydraAvailabilityIdleReasoDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").jqGrid("GridUnload");
                                    $.HydraAvailabilityIdleReason();
                                    HydraAvailabilityIdleReasonList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblHydraAvailabilityIdleReasonDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblHydraAvailabilityIdleReasonDetailsGrid");
        })

        $(document).on('click', "#IdTblHydraAvailabilityIdleReasonDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblHydraAvailabilityIdleReasonDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblHydraAvailabilityIdleReasonDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblHydraAvailabilityIdleReasonDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );


    }

    //=========================================  IdleReport IdleReason Category   ===============================

    $.IdleReportIdleReasonCategory = function ()
    {
        var IdleReportIdleReasonCategoryList = [];
        var IdleReportIdleReasonCategoryDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetIdleReportIdleReasonCategoryListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    IdleReportIdleReasonCategoryList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblIdleReportIdleReasonCategoryDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivIdleReportIdleReasonCategoryPager'></div >")
        $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetIdleReportIdleReasonCategoryDetails"),
           // caption: "IdleReport IdleReasonCategoryName",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width: 500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "IdleReport_IdleReasonCategoryName",
            sortorder: "desc",
            pager: "#IdDivIdleReportIdleReasonCategoryPager",
            loadonce: true,
            colNames: ['Edit', 'IdleReportIdleReasonCategoryId', 'IdleReport IdleReason Category', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'IdleReport_IdleReasonCategoryId', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    name: 'IdleReport_IdleReasonCategoryName', width: 250, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < IdleReportIdleReasonCategoryList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == IdleReportIdleReasonCategoryList[i].IdleReport_IdleReasonCategoryName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("IdleReasonCategory Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },

                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").navGrid("#IdDivIdleReportIdleReasonCategoryPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblIdleReportIdleReasonCategoryDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
               //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").navButtonAdd("#IdDivIdleReportIdleReasonCategoryPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").jqGrid("GridUnload");
                $.IdleReportIdleReasonCategory();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").navButtonAdd("#IdDivIdleReportIdleReasonCategoryPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    //  var newRowId = parseInt(rowidtoAdd[(rowidtoAdd.length - 1)]) + 1;
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").addRowData(newRowId, {});
                $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").editRow(newRowId);
                //  $("#" + newRowId + "_AppDescription").focus()
                $($("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").navButtonAdd("#IdDivIdleReportIdleReasonCategoryPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var rowid = $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").getDataIDs();
                var IdleReportIdleReasonCategoryData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        var IdleReportIdleReasonCategoryName = $("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var ApplicationIc = 0;
                        if (IsUpdate) {
                            ApplicationIc = $("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            ApplicationIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0
                        if (IdleReportIdleReasonCategoryName != "") {

                            var ApplicationValues =
                                {
                                    IdleReport_IdleReasonCategoryId: ApplicationIc,
                                    IdleReport_IdleReasonCategoryName: IdleReportIdleReasonCategoryName,
                                    IsActive: isActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No Remarks"
                                }

                            IdleReportIdleReasonCategoryData.push(ApplicationValues)
                            IsValidateBit = 0
                        }
                        else {

                            IsValidateBit++
                        }

                    }
                    IdleReportIdleReasonCategoryName == "" ? $("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }

                IdleReportIdleReasonCategoryDuplicateList = IdleReportIdleReasonCategoryData


                for (var i = 0; i < IdleReportIdleReasonCategoryData.length; i++) {
                    for (var j = 0; j < IdleReportIdleReasonCategoryDuplicateList.length; j++) {
                        if (i != j) {
                            if (IdleReportIdleReasonCategoryData[i].IdleReport_IdleReasonCategoryName.toString().toUpperCase().trim() == IdleReportIdleReasonCategoryDuplicateList[j].IdleReport_IdleReasonCategoryName.toString().toUpperCase().trim()) {

                                isDuplicaterow++
                            }


                        } else {


                        }

                    }

                }
                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("IdleReportIdleReasonCategoryData", JSON.stringify(IdleReportIdleReasonCategoryData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertIdleReportIdleReasonCategoryDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").jqGrid("GridUnload");
                                    $.IdleReportIdleReasonCategory();
                                    IdleReportIdleReasonCategoryList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblIdleReportIdleReasonCategoryDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblIdleReportIdleReasonCategoryDetailsGrid");
        })

        $(document).on('click', "#IdTblIdleReportIdleReasonCategoryDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblIdleReportIdleReasonCategoryDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblIdleReportIdleReasonCategoryDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblIdleReportIdleReasonCategoryDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );


    }


    //=========================================  Commission Activity  ===============================

    $.CommissionActivity = function () {
        var CommissionActivityList = [];
        var CommissionActivityDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetCommissionActivityListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    CommissionActivityList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblCommissionActivityDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivCommissionActivityPager'></div >")
        $("#IdTblCommissionActivityDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetCommissionActivityDetails"),
           // caption: "Commission Activity Details",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width: 500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "Commission_ActivityName",
            sortorder: "desc",
            pager: "#IdDivCommissionActivityPager",
            loadonce: true, 
            colNames: ['Edit', ' CommissionActivityId', 'Commission Activity', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'Ma_Commission_Activity', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    //Commission_ActivityName
                    name: 'Commission_ActivityName', width: 250, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < CommissionActivityList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == CommissionActivityList[i].Commission_ActivityName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Commission Activity Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },

                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblCommissionActivityDetailsGrid").navGrid("#IdDivCommissionActivityPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblCommissionActivityDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblCommissionActivityDetailsGrid").navButtonAdd("#IdDivCommissionActivityPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblCommissionActivityDetailsGrid").jqGrid("GridUnload");;
                $.CommissionActivity();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblCommissionActivityDetailsGrid").navButtonAdd("#IdDivCommissionActivityPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblCommissionActivityDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    //  var newRowId = parseInt(rowidtoAdd[(rowidtoAdd.length - 1)]) + 1;
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblCommissionActivityDetailsGrid").addRowData(newRowId, {});
                $("#IdTblCommissionActivityDetailsGrid").editRow(newRowId);
                //  $("#" + newRowId + "_AppDescription").focus()
                $($("#IdTblCommissionActivityDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblCommissionActivityDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblCommissionActivityDetailsGrid").navButtonAdd("#IdDivCommissionActivityPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {
                debugger
                var rowid = $("#IdTblCommissionActivityDetailsGrid").getDataIDs();
                var CommissionActivityData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblCommissionActivityDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        var CommissionActivityName = $("#IdTblCommissionActivityDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblCommissionActivityDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var ApplicationIc = 0;
                        if (IsUpdate) {
                            ApplicationIc = $("#IdTblCommissionActivityDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            ApplicationIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0
                        if (CommissionActivityName != "") {

                            var ApplicationValues =
                                {
                                    Ma_Commission_Activity: ApplicationIc,
                                    Commission_ActivityName: CommissionActivityName,
                                    IsActive: isActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No Remarks"
                                }

                            CommissionActivityData.push(ApplicationValues)
                            IsValidateBit = 0
                        }
                        else {

                            IsValidateBit++
                        }

                    }
                    CommissionActivityName == "" ? $("#IdTblCommissionActivityDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblCommissionActivityDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }              
                CommissionActivityDuplicateList = CommissionActivityData

                for (var i = 0; i < CommissionActivityData.length; i++) {
                    for (var j = 0; j < CommissionActivityDuplicateList.length; j++) {
                        if (i != j)
                        {
                            if (CommissionActivityData[i].CommissionActivityName.toString().toUpperCase().trim() == CommissionActivityDuplicateList[j].CommissionActivityName.toString().toUpperCase().trim()) {
                                isDuplicaterow++
                            }

                        }
                        else {
                        }
                    }
                }

                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("CommissionActivityData", JSON.stringify(CommissionActivityData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertGetCommissionActivityDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblCommissionActivityDetailsGrid").jqGrid("GridUnload");
                                    $.CommissionActivity()
                                    CommissionActivityList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblCommissionActivityDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblCommissionActivityDetailsGrid");
        })

        $(document).on('click', "#IdTblCommissionActivityDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblCommissionActivityDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblCommissionActivityDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblCommissionActivityDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblCommissionActivityDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblCommissionActivityDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );


    }

    //=========================================  Commission Error Base   ===============================

    $.CommissionErrorBase = function () {
        var CommissionErrorBaseList = [];
        var CommissionErrorBaseDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetCommissionErrorBaseListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    CommissionErrorBaseList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblCommissionErrorBaseDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivCommissionErrorBasePager'></div >")
        $("#IdTblCommissionErrorBaseDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetCommissionErrorBaseDetails"),
          //  caption: "Commission Error Base Details",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width: 500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "Commission_ErrorBaseName",
            sortorder: "desc",
            pager: "#IdDivCommissionErrorBasePager",
            loadonce: true,
            colNames: ['Edit', ' CommissionErrorBaseId', ' Commission Error Base', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'Commission_ErrorBaseId', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    //Commission_ErrorBaseName
                    name: 'Commission_ErrorBaseName', width: 250, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < CommissionErrorBaseList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == CommissionErrorBaseList[i].Commission_ErrorBaseName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Commission Error Base Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },

                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblCommissionErrorBaseDetailsGrid").navGrid("#IdDivCommissionErrorBasePager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblCommissionErrorBaseDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblCommissionErrorBaseDetailsGrid").navButtonAdd("#IdDivCommissionErrorBasePager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblCommissionErrorBaseDetailsGrid").jqGrid("GridUnload");;
                $.CommissionErrorBase();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblCommissionErrorBaseDetailsGrid").navButtonAdd("#IdDivCommissionErrorBasePager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblCommissionErrorBaseDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    //  var newRowId = parseInt(rowidtoAdd[(rowidtoAdd.length - 1)]) + 1;
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblCommissionErrorBaseDetailsGrid").addRowData(newRowId, {});
                $("#IdTblCommissionErrorBaseDetailsGrid").editRow(newRowId);
                //  $("#" + newRowId + "_AppDescription").focus()
                $($("#IdTblCommissionErrorBaseDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblCommissionErrorBaseDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblCommissionErrorBaseDetailsGrid").navButtonAdd("#IdDivCommissionErrorBasePager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var rowid = $("#IdTblCommissionErrorBaseDetailsGrid").getDataIDs();
                var CommissionErrorBaseData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblCommissionErrorBaseDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        var CommissionErrorBaseName = $("#IdTblCommissionErrorBaseDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblCommissionErrorBaseDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var CommissionErrorBaseIc = 0;
                        if (IsUpdate) {
                            CommissionErrorBaseIc = $("#IdTblCommissionErrorBaseDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            CommissionErrorBaseIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0
                        if (CommissionErrorBaseName != "") {

                            var ApplicationValues =
                                {
                                    Commission_ErrorBaseId: CommissionErrorBaseIc,
                                    Commission_ErrorBaseName: CommissionErrorBaseName,
                                    IsActive: isActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No Remarks"

                                }

                            CommissionErrorBaseData.push(ApplicationValues)
                            IsValidateBit = 0
                        }
                        else {

                            IsValidateBit++
                        }

                    }
                    CommissionErrorBaseName == "" ? $("#IdTblCommissionErrorBaseDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblCommissionErrorBaseDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }

                CommissionErrorBaseDuplicateList = CommissionErrorBaseData

                for (var i = 0; i < CommissionErrorBaseData.length; i++) {
                    for (var j = 0; j < CommissionErrorBaseDuplicateList.length; j++) {
                        if (i != j) {
                            if (CommissionErrorBaseData[i].Commission_ErrorBaseName.toString().toUpperCase().trim() == CommissionErrorBaseDuplicateList[j].Commission_ErrorBaseName.toString().toUpperCase().trim())
                            {
                                isDuplicaterow++
                            }
                        }
                        else {
                        }
                    }
                }

                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("CommissionErrorBaseData", JSON.stringify(CommissionErrorBaseData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertCommissionErrorBaseDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblCommissionErrorBaseDetailsGrid").jqGrid("GridUnload");
                                    $.CommissionErrorBase();
                                    CommissionErrorBaseList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblCommissionErrorBaseDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblCommissionErrorBaseDetailsGrid");
        })

        $(document).on('click', "#IdTblCommissionErrorBaseDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblCommissionErrorBaseDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblCommissionErrorBaseDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblCommissionErrorBaseDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblCommissionErrorBaseDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblCommissionErrorBaseDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );


    }

    //=========================================  Installation Protocol Check Points   ===============================

    $.InstallationProtocolCheckPoints = function ()
    {
        var InstallationProtocolCheckPointsList = [];
        var InstallationProtocolCheckPointsDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetInstallationProtocolCheckPointListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    InstallationProtocolCheckPointsList = resp
                    console.log(resp)
                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblInstallationProtocolCheckPointsDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivInstallationProtocolCheckPointsDetailsPager'></div >")
        $("#IdTblInstallationProtocolCheckPointsDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetInstallationProtocolCheckPointDetails"),
         //   caption: "Installation Protocol Check Points",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width: 500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "InstallationProtocolCheckPointsName",
            sortorder: "desc",
            pager: "#IdDivInstallationProtocolCheckPointsDetailsPager",
            loadonce: true,
            colNames: ['Edit', ' InstallationProtocolCheckPointsId', ' Installation Protocol CheckPoints', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {

                    name: 'InstallationProtocolCheckPointsId', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    name: 'InstallationProtocolCheckPointsName', width: 250, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < InstallationProtocolCheckPointsList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == InstallationProtocolCheckPointsList[i].InstallationProtocolCheckPointsName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Installation Protocol Check Points Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },

                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'IsUpdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblInstallationProtocolCheckPointsDetailsGrid").navGrid("#IdDivInstallationProtocolCheckPointsDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblInstallationProtocolCheckPointsDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });

        $("#IdTblInstallationProtocolCheckPointsDetailsGrid").navButtonAdd("#IdDivInstallationProtocolCheckPointsDetailsPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblInstallationProtocolCheckPointsDetailsGrid").jqGrid("GridUnload");;
                $.InstallationProtocolCheckPoints();
            }
        })

        var testingrow = 0;
        var counter = 0;

        $("#IdTblInstallationProtocolCheckPointsDetailsGrid").navButtonAdd("#IdDivInstallationProtocolCheckPointsDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblInstallationProtocolCheckPointsDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblInstallationProtocolCheckPointsDetailsGrid").addRowData(newRowId, {});
                $("#IdTblInstallationProtocolCheckPointsDetailsGrid").editRow(newRowId);
                $($("#IdTblInstallationProtocolCheckPointsDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdTblInstallationProtocolCheckPointsDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
        $("#IdTblInstallationProtocolCheckPointsDetailsGrid").navButtonAdd("#IdDivInstallationProtocolCheckPointsDetailsPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var rowid = $("#IdTblInstallationProtocolCheckPointsDetailsGrid").getDataIDs();
                var InstallationProtocolCheckPointsData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#IdTblInstallationProtocolCheckPointsDetailsGrid tr")[i]).attr("editable") == 1)
                    {
                        Rowselected++
                        var InstallationProtocolCheckPointsName = $("#IdTblInstallationProtocolCheckPointsDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdTblInstallationProtocolCheckPointsDetailsGrid tr").eq(i).children().eq(5).children().val()
                        var ApplicationIc = 0;
                        if (IsUpdate) {
                            ApplicationIc = $("#IdTblInstallationProtocolCheckPointsDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            ApplicationIc = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0
                        if (InstallationProtocolCheckPointsName != "")
                        {
                            var ApplicationValues =
                                {
                                    InstallationProtocolCheckPointsId: ApplicationIc,
                                    InstallationProtocolCheckPointsName: InstallationProtocolCheckPointsName,
                                    IsActive: isActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No Remarks"
                                }

                            InstallationProtocolCheckPointsData.push(ApplicationValues)
                            IsValidateBit = 0
                        }
                        else {

                            IsValidateBit++
                        }

                    }
                    InstallationProtocolCheckPointsName == "" ? $("#IdTblInstallationProtocolCheckPointsDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblInstallationProtocolCheckPointsDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }

                InstallationProtocolCheckPointsDuplicateList = InstallationProtocolCheckPointsData;

                for (var i = 0; i < InstallationProtocolCheckPointsData.length; i++) {
                    for (var j = 0; j < InstallationProtocolCheckPointsDuplicateList.length; j++) {
                        if (i != j) {
                            if (InstallationProtocolCheckPointsData[i].InstallationProtocolCheckPointsName.toString().toUpperCase().trim() == InstallationProtocolCheckPointsDuplicateList[j].InstallationProtocolCheckPointsName.toString().toUpperCase().trim()) {
                                isDuplicaterow++
                            }
                        }
                        else {
                        }
                    }
                }


                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("InstallationProtocolCheckPointsData", JSON.stringify(InstallationProtocolCheckPointsData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/Insert_InstallationProtocolCheckPointDetails"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblInstallationProtocolCheckPointsDetailsGrid").jqGrid("GridUnload");
                                    InstallationProtocolCheckPointsList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblInstallationProtocolCheckPointsDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblInstallationProtocolCheckPointsDetailsGrid");
        })

        $(document).on('click', "#IdTblInstallationProtocolCheckPointsDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblInstallationProtocolCheckPointsDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblInstallationProtocolCheckPointsDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblInstallationProtocolCheckPointsDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblInstallationProtocolCheckPointsDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblInstallationProtocolCheckPointsDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );


    }


    //=========================================================  Installation Protocol Specs   =========================================================================//

    var InstallationProtocolSpecsUpdate = false;
    var InstallationProtocolSpecsId = 0;
    var InstallationProtocolSpecsIsActive = 0;
    var ModelIDarray = [];
    var SelectedFile1, SelectedFile2, SelectedFile3;
    var FileName1, FileName2, FileName3;
    var aid = 0;
    $.InstallationProtocolSpecs = function ()
    {
        var InstallationProtocolSpecsList = [];
        var InstallationProtocolSpecsDuplicateList = [];
        var appdisccount = 0;

        $.ajaxfunc = function ()
        {
            $.ajax({
                url: AbsolutePath("/Settings/GetInstallationProtocolSpecsListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp)
                {
                    InstallationProtocolSpecsList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblInstallationProtocolSpecsDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivInstallationProtocolSpecsDetailsPager'></div >")
        $("#IdTblInstallationProtocolSpecsDetailsGrid").jqGrid
            ({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetInstallationProtocolSpecsDetails"),
         //   caption: "Installation Protocol Specs Details",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width:1000,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "Models_Name",
            sortorder: "desc",
            pager: "#IdDivInstallationProtocolSpecsDetailsPager",
            loadonce: true,
            colNames: ['Edit', ' Installation Protocol SpecsId', 'Model_ID', ' Model', 'Attachment1', 'Attachement1 Description', 'Attachment2', 'Attachement2 Description', 'Attachment3', 'Attachement3 Description', 'SpecPoints1', 'SpecPoints2', 'SpecPoints3', 'Is Active ?', 'Number of Attachements', 'IsUpdate', 'Remarks'],
            colModel: [

                {
                    name: "Edit", align: "center", width: 50, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                        return "<i class='glyphicon glyphicon-pencil'  title='Click Here For Edit'  onclick='EditInstallationProtocolSpecsDetails(" + b.rowId + ")' id='IdInstallationProtocolSpecs" + b.rowId + "'></span>";
                    }
                },
                {
                    name: 'CS_InstallationProtocolSpecsIC', width: 90, align: 'center', editable: true, hidden: true
                },
                { name: 'Model_ID', width: 20, align: 'left', search: false, hidden: true },
                { name: 'Models_Name', width: 100, align: 'left', search: true },
                { name: 'Attachment1', width: 100, align: 'left', search: false },
                { name: 'Attachement1Desc', width: 100, align: 'left', search: false, hidden: true },
                { name: 'Attachment2', width: 100, align: 'left', search: false },
                { name: 'Attachment2Desc', width: 100, align: 'left', search: false, hidden: true },
                { name: 'Attachment3', width: 100, align: 'left', search: false },
                { name: 'Attachment3Desc', width: 100, align: 'left', search: false, hidden: true },
                { name: 'SpecPoints1', width: 100, align: 'left', search: false },
                { name: 'SpecPoints2', width: 100, align: 'left', search: false },
                { name: 'SpecPoints3', width: 100, align: 'left', search: false },
                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'NumberofAttachements', width: 50, align: 'left', search: true, hidden: true },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
                { name: 'Remarks', width: 100, align: 'left', search: true, hidden: true },
            ],
            multiselect: false,
            jsonReader:
                {
                    root: "root",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            loadComplete: function (data)
            {
                aid = aid + 1;                             
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');          
                if (data.page == 1 && aid == 1)
                {
                    ModelIDarray = [];
                    for (var i = 0; i < data.root.length; i++)
                    {
                        ModelIDarray.push(data.root[i].Model_ID);
                    }
                }                
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblInstallationProtocolSpecsDetailsGrid").navGrid("#IdDivInstallationProtocolSpecsDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblInstallationProtocolSpecsDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblInstallationProtocolSpecsDetailsGrid").navButtonAdd("#IdDivInstallationProtocolSpecsDetailsPager",
            {
                title: 'Refresh',
                caption: "",
                buttonicon: 'ui-icon-refresh',
                onClickButton: function () {
                    $("#IdTblInstallationProtocolSpecsDetailsGrid").jqGrid("GridUnload");;
                    $.InstallationProtocolSpecs();
                }
            });
        var testingrow = 0;
        var counter = 0;

        $("#IdTblInstallationProtocolSpecsDetailsGrid").navButtonAdd("#IdDivInstallationProtocolSpecsDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function ()
            {
                BindModal();
                $.Checkpoints();
                InstallationProtocolSpecsId = 0;

                $("#IdIsActiveIPS").prop("checked", false)
                $("#IdModelName").val(0);             
                $("#IdTxtSpecPoint1").val("");
                $("#IdTxtSpecPoint2").val("");
                $("#IdTxtSpecPoint3").val("");
                $("#IdTxtDescription1").val("");
                $("#IdTxtDescription2").val("");
                $("#IdTxtDescription3").val("");

                $("#DivAttachment1Img1").html(" ");
                $("#DivAttachment2Img1").html(" ");
                $("#DivAttachment3Img1").html(" ");

                $('#Attachment1Img1').attr('src',"");
                $('#Attachment1Img2').attr('src',"");
                $('#Attachment1Img3').attr('src', "");

                $("#IdDivModelViewForAttachment").modal(
                {
                    backdrop: false
                });
                Update = false;
                $("#IdNumberfoAttachments").val("0")


                $("#IdNumberfoAttachments").trigger("change");
                SelectedFile1 = null, SelectedFile2 = null, SelectedFile3 = null;
                FileName1 = null, FileName2 = null, FileName3 = null;
            }
        });

        var isDuplicaterow = 0
        $(document).on('click', '#IdTblInstallationProtocolSpecsDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblInstallationProtocolSpecsDetailsGrid");
        })

        $(document).on('click', "#IdTblInstallationProtocolSpecsDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblInstallationProtocolSpecsDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblInstallationProtocolSpecsDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblInstallationProtocolSpecsDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblInstallationProtocolSpecsDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblInstallationProtocolSpecsDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );

    };
   
    $.Checkpoints = function (id)
    {
        var rowdata = $("#IdTblInstallationProtocolSpecsDetailsGrid").getRowData(id);
        InstallationProtocolSpecsId = rowdata.CS_InstallationProtocolSpecsIC;

        $("#DivCheckpointsGrid").html("");
        $("#DivCheckpointsGrid").append("<table id='IdTblDivCheckpointsGridGrid'></table>")
        $("#DivCheckpointsGrid").append("<div id='IdDivDivCheckpointsGridPager'></div >")
        $("#IdTblDivCheckpointsGridGrid").jqGrid
            ({
                ignoreCase: true,
                url: AbsolutePath("/Settings/GetInstallationProtocolCheckPoints?HeaderId=" + InstallationProtocolSpecsId),
           // caption: "Installation Protocol Specs Details",
            datatype: "Json",
            //data: "local",
            mtype: "GET",
            height: 'auto',
            width: 800,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "CS_InstallationProtocolChekPointsDetailerIC",
            sortorder: "desc",
            pager: "#IdDivDivCheckpointsGridPager",
            loadonce: true,
            colNames: ['Edit', ' CS_InstallationProtocolChekPointsDetailerIC', 'CS_InstallationProtocolSpecsIC', 'Check Points', 'Remarks', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },               
                { name: 'CS_InstallationProtocolChekPointsDetailerIC', width: 50, align: 'center', search: true, hidden: true },
                { name: 'CS_InstallationProtocolSpecsIC', width: 50, align: 'center', search: true, hidden: true },
                { name: 'Description', width: 300, align: 'left', editable: true, search: true },
                { name: 'Remarks', width: 100, align: 'left', editable: true, search: true, },
                { name: 'Is_Active', width: 100, align: 'center', formatter: IsActiveforCheckPoints, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, hidden: true},
            ],
            multiselect: false,
            jsonReader:
                {
                    root: "root",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');             
                console.log(data)
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        })
        $("#IdTblDivCheckpointsGridGrid").navGrid("#IdDivDivCheckpointsGridPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblDivCheckpointsGridGrid').jqGrid('filterToolbar',
                   {
                       stringResult: true,
                       searchOnEnter: true,
                       searchOperators: false,
                       //enableClear: false,
                       //clearSearch: false,
                       defaultSearch: "cn",
                       sopt: ["cn", "eq", "neq"],
                       resetIcon: "",
                       ignoreCase: true
                   });
        $("#IdTblDivCheckpointsGridGrid").navButtonAdd("#IdDivDivCheckpointsGridPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblDivCheckpointsGridGrid").jqGrid("GridUnload");;
                $.Checkpoints();
            }
        })

        var counter = 0;

        $("#IdTblDivCheckpointsGridGrid").navButtonAdd("#IdDivDivCheckpointsGridPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function ()
            {
                rowidtoAdd = $("#IdTblDivCheckpointsGridGrid").getDataIDs();
                if (rowidtoAdd == "")
                {
                    var newRowId = 1
                }
                else {
                    //  var newRowId = parseInt(rowidtoAdd[(rowidtoAdd.length - 1)]) + 1;
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdTblDivCheckpointsGridGrid").addRowData(newRowId, {});
                $("#IdTblDivCheckpointsGridGrid").editRow(newRowId);
                //  $("#" + newRowId + "_AppDescription").focus()
                $($("#IdTblDivCheckpointsGridGrid tr")[newRowId]).children().eq(7).text("false")
                $($("#IdTblDivCheckpointsGridGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                InstallationProtocolSpecsUpdate = false;
                counter++;
              
            }
        });





        $(document).on('click', '#IdTblDivCheckpointsGridGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblDivCheckpointsGridGrid");
        })

        $(document).on('click', "#IdTblDivCheckpointsGridGrid .ClsDelContactDetails", function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblDivCheckpointsGridGrid").delRowData(rowid1);
        });

        $(document).on('click', "#IdTblDivCheckpointsGridGrid .ClsRefContactDetails", function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblDivCheckpointsGridGrid tr").eq(RefRowid).children().eq(4).children().val()

            if (IsactveCheck == "true") {
                $($(".IDISActiveClassforcheck")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClassforcheck")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblDivCheckpointsGridGrid").restoreRow(RefRowid);
            $($(".IDISActiveClassforcheck")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblDivCheckpointsGridGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        });

    };

    $("#IdDivAttachment1").show();
    $("#IdDivAttachment2").hide();
    $("#IdDivAttachment3").hide();

    $("#IdNumberfoAttachments").change(function ()
    {
        var attachmentDropdowenvalue = $("#IdNumberfoAttachments").val();

        if (attachmentDropdowenvalue == 3) {
            $("#IdDivAttachment1").show();
            $("#IdDivAttachment2").show();
            $("#IdDivAttachment3").show();
        }
        else if (attachmentDropdowenvalue == 2) {
            $("#IdDivAttachment1").show();
            $("#IdDivAttachment2").show();
            $("#IdDivAttachment3").hide();
        }
        else if (attachmentDropdowenvalue == 1) {
            $("#IdDivAttachment1").show();
            $("#IdDivAttachment2").hide();
            $("#IdDivAttachment3").hide();
        }
        else
        {
            $("#IdDivAttachment1").hide();
            $("#IdDivAttachment2").hide();
            $("#IdDivAttachment3").hide();
        }
    });

    function BindModal()
    {
        $.ajax({
            url: AbsolutePath('/Settings/GetModalNameList'),
            type: 'GET',
            cache: false,
            async: false,
            success: function (resp) {
                $("#IdModelName").val(0);
                $("#IdModelName").empty();
                $("#IdModelName").append(resp);                
            }
        });
    }
   
    function EditInstallationProtocolSpecsDetails(Id)
    {
        debugger
        if ($($(".IDISActiveClass")[Id - 1]).prop("checked") == true)
        {
            $("#IdIsActiveIPS").prop("checked", true)
        }
        else {
            $("#IdIsActiveIPS").prop("checked", false)
        }

        $("#IdDivModelViewForAttachment").modal
       ({
           backdrop: false
       });
        BindModal();
        $.Checkpoints(Id);
        InstallationProtocolSpecsUpdate = true;
        var rowdata = $("#IdTblInstallationProtocolSpecsDetailsGrid").getRowData(Id);       
        InstallationProtocolSpecsId = rowdata.CS_InstallationProtocolSpecsIC;       
        $("#IdModelName").val(rowdata.Model_ID);
        $("#IdNumberfoAttachments").val(rowdata.NumberofAttachements);
        $("#IdTxtSpecPoint1").val(rowdata.SpecPoints1);
        $("#IdTxtSpecPoint2").val(rowdata.SpecPoints2);
        $("#IdTxtSpecPoint3").val(rowdata.SpecPoints3);
        $("#IdTxtDescription1").val(rowdata.Attachement1Desc);
        $("#IdTxtDescription2").val(rowdata.Attachment2Desc);
        $("#IdTxtDescription3").val(rowdata.Attachment3Desc);
        if (rowdata.NumberofAttachements == 3)
        {
            $("#IdDivAttachment1").show();
            $("#IdDivAttachment2").show();
            $("#IdDivAttachment3").show();
        }
        else if (rowdata.NumberofAttachements == 2)
        {
            $("#IdDivAttachment1").show();
            $("#IdDivAttachment2").show();
            $("#IdDivAttachment3").hide();
        }
        else if (rowdata.NumberofAttachements == 1)
        {
            $("#IdDivAttachment1").show();
            $("#IdDivAttachment2").hide();
            $("#IdDivAttachment3").hide();
        }

        else
        {
            $("#IdDivAttachment1").hide();
            $("#IdDivAttachment2").hide();
            $("#IdDivAttachment3").hide();
        }
        $("#DivAttachment1Img1").html("");
        $("#DivAttachment2Img1").html("");
        $("#DivAttachment3Img1").html("");

        $("#DivAttachment1Img1").append("<img src=" + AbsolutePath("/UploadedFiles/" + rowdata.Attachment1) + " style='height: 100%; width: 100%' id='Attachment1Img1'/>")
        $("#DivAttachment2Img1").append("<img src=" + AbsolutePath("/UploadedFiles/" + rowdata.Attachment2) + " style='height: 100%; width: 100%' id='Attachment2Img1'/>")
        $("#DivAttachment3Img1").append("<img src=" + AbsolutePath("/UploadedFiles/" + rowdata.Attachment3) + " style='height: 100%; width: 100%' id='Attachment3Img1'/>")

        //$('#Attachment1Img1').attr('src', AbsolutePath("/UploadedFiles/" + rowdata.Attachment1));
        //$('#Attachment2Img1').attr('src', AbsolutePath("/UploadedFiles/" + rowdata.Attachment2));
        //$('#Attachment3Img1').attr('src', AbsolutePath("/UploadedFiles/" + rowdata.Attachment3) );

        FileName1 = rowdata.Attachment1;
        FileName2 = rowdata.Attachment2;
        FileName3 = rowdata.Attachment3;
    };

    $("#IdModelName").change(function ()
    {
        for (var i = 0; i < ModelIDarray.length; i++)
        {
            debugger
            if (ModelIDarray[i] == $("#IdModelName").val())
            {           
                AlertMessage("Model name already exists", "warning");
                $("#IdModelName").val(0);
            }
            
        }      

    })


    function InsertInstallationProtocolSpecs(InstallationProtocolSpecsId)
    {
        debugger
        var ModelNameId = $("#IdModelName").val();
        var Model_Name = $("#IdModelName").text();

        if ($("#IdIsActiveIPS").prop("checked") == true) {
            InstallationProtocolSpecsIsActive = 1;
        }
        else {
            InstallationProtocolSpecsIsActive = 0;
        }

        var SpecPoint1 = $("#IdTxtSpecPoint1").val();
        var SpecPoint2 = $("#IdTxtSpecPoint2").val();
        var SpecPoint3 = $("#IdTxtSpecPoint3").val();
        var Remarks = $("#IdTxtAttachmentRemark").val();

        Attachement1Desc = $("#IdTxtDescription1").val();
        Attachement2Desc = $("#IdTxtDescription2").val();
        Attachement3Desc = $("#IdTxtDescription3").val();

        var _formData = new FormData();

        if (FileName1 != null || FileName2 != null || FileName3 != null) {
            if (SelectedFile1 != null || SelectedFile2 != null || SelectedFile3 != null)
            {                
                if (SelectedFile1 != null) {
                    _formData.append("Attachment1", "");
                    FileName1 = "";
                    _formData.append("Attachment1", SelectedFile1[0]);
                    FileName1 = SelectedFile1[0].name;
                }

                if (SelectedFile2 != null)
                {
                    _formData.append("Attachment2", "");
                    FileName2 = "";
                    _formData.append("Attachment2", SelectedFile2[0]);
                    FileName2 = SelectedFile2[0].name;
                }

                if (SelectedFile3 != null) {
                    _formData.append("Attachment3", "");

                    FileName3 = "";
                    _formData.append("Attachment3", SelectedFile3[0]);
                    FileName3 = SelectedFile3[0].name;
                }

            }
        }
        var NumberOfAttachments = $("#IdNumberfoAttachments").val();


        var InstallationProtocolSpecsarr = [];
        var InstallationProtocolSpecsList =
            {
                CS_InstallationProtocolSpecsIC: InstallationProtocolSpecsId,
                Model_ID: ModelNameId,
                Models_Name: Model_Name,
                Attachment1: FileName1,
                Attachment2: FileName2,
                Attachment3: FileName3,
                Attachement1Desc: Attachement1Desc,
                Attachment2Desc: Attachement2Desc,
                Attachment3Desc: Attachement3Desc,
                SpecPoints1: SpecPoint1,
                SpecPoints2: SpecPoint2,
                SpecPoints3: SpecPoint3,
                Isupdate: InstallationProtocolSpecsUpdate,
                NumberofAttachements: NumberOfAttachments,
                IsActive: InstallationProtocolSpecsIsActive,
                Remarks: Remarks
            }
        InstallationProtocolSpecsarr.push(InstallationProtocolSpecsList);
        //---------------------------------------------Detailer------------------------------------------------------

        var rowid = $("#IdTblDivCheckpointsGridGrid").getDataIDs();
        var CheckpointsGridlist = [];
        var ToAvoidFirstentry = 0;
        var Rowselected = 0;

        for (i = 0; i <= rowid.length; i++) {
            debugger
            if ($($("#IdTblDivCheckpointsGridGrid tr")[i]).attr("editable") == 1) {
                Rowselected++
                var Description = $("#IdTblDivCheckpointsGridGrid tr").eq(i).children().eq(4).children().val()
                var Remarks = $("#IdTblDivCheckpointsGridGrid tr").eq(i).children().eq(5).children().val()
                var Is_Active = 0
                if ($($(".IDISActiveClassforcheck")[i - 1]).prop("checked") == true) {
                    Is_Active = 1;
                }
                else {
                    Is_Active = 0;
                }
                var IsUpdate = $("#IdTblDivCheckpointsGridGrid tr").eq(i).children().eq(7).text()

                var CS_InstallationProtocolChekPointsDetailerIC = 0;
                if (IsUpdate == "true") {

                    CS_InstallationProtocolChekPointsDetailerIC = $("#IdTblDivCheckpointsGridGrid tr").eq(i).children().eq(2).text()
                }
                else {
                    CS_InstallationProtocolChekPointsDetailerIC = 0;
                }

                var CS_InstallationProtocolSpecsIC = 0;
                if (IsUpdate == "true") {
                    CS_InstallationProtocolSpecsIC = $("#IdTblDivCheckpointsGridGrid tr").eq(i).children().eq(3).text()
                }
                else {
                    CS_InstallationProtocolSpecsIC = 0;
                }

                var IsValidateBit = 0
                if (Description != "") {
                    var CheckpointsValues =
                        {
                            Description: Description,
                            CS_InstallationProtocolChekPointsDetailerIC: CS_InstallationProtocolChekPointsDetailerIC,
                            CS_InstallationProtocolSpecsIC: CS_InstallationProtocolSpecsIC,
                            Is_Active: Is_Active,
                            IsUpdate: IsUpdate,
                            Remarks: Remarks
                        }

                    CheckpointsGridlist.push(CheckpointsValues)
                    IsValidateBit = 0
                }
                else {

                    IsValidateBit++
                }

            }
            Description == "" ? $("#IdTblDivCheckpointsGridGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(i).children().eq(3).css("border", "")
        }
        //if (ModelNameId != 0 && FileName1 != "" && SpecPoint1 != "")
        if (ModelNameId != 0) {
            $.ajax({
                url: AbsolutePath("/Settings/Insert_InstallationProtocolSpecsDetails"),
                type: 'POST',
                datatype: "JSON",
                data:
                    {
                        "InstallationProtocolSpecsDetails": JSON.stringify(InstallationProtocolSpecsarr),
                        "CheckpointsGridlistData": JSON.stringify(CheckpointsGridlist)
                    },
                success: function (resp) {
                    $.ajax({
                        url: AbsolutePath("/Settings/SaveFileInFolder"),
                        type: "POST",
                        async: false,
                        cache: false,
                        processData: false,
                        contentType: false,
                        data: _formData,
                        success: function (data, status, xhr) {
                            AlertMessage("Records Saved Successfully", "success")
                            $("#IdDivModelViewForAttachment").modal("hide");
                            $.InstallationProtocolSpecs();
                            $.Checkpoints();
                        },
                        error: function (xhr, status, error) {
                            Uservalue = [];
                        }
                    });
                },
                error: function (resp) {
                    AlertMessage("File Not Saved", "warning")

                },
            });
        }
        else {
            if (ModelNameId == 0) {
                AlertMessage("Please fill mandatory field(s)", "warning");
                $("#IdModelName").addClass("valdation");
            }
        }
    }



    $(document).on('click', '#IdBtnSubmit', function ()
    {

        InsertInstallationProtocolSpecs(InstallationProtocolSpecsId);

    })

   
    debugger

    var Attachement1Desc, Attachement2Desc, Attachement3Desc;

    $("#IdTxtAttachment1").change(function (e)
    {
        $("#DivAttachment1Img1").html("");
        var reader = new FileReader();
        reader.onload = function (e)
        {
            $("#DivAttachment1Img1").append("<img src=" + e.target.result + " style='height: 100%; width: 100%' id='Attachment1Img1'/>")          
        }                
        reader.readAsDataURL(this.files[0]);
        SelectedFile1 = $(this).prop("files");
        FileName1 = SelectedFile1[0].name;
    });

    $("#IdTxtAttachment2").change(function (e)
    {
        $("#DivAttachment2Img1").html("");
        var reader = new FileReader();
        reader.onload = function (e) {
            $("#DivAttachment2Img1").append( "<img src="+e.target.result +" style='height: 100%; width: 100%' id='Attachment2Img1'/>")
            //$('#Attachment2Img1').attr('src', );
        }
        reader.readAsDataURL(this.files[0]);
        SelectedFile2 = $(this).prop("files");
        FileName2 = SelectedFile2[0].name;
    });


    $("#IdTxtAttachment3").change(function (e)
    {
        $("#DivAttachment3Img1").html("");
        var reader = new FileReader();
        reader.onload = function (e) {
            $("#DivAttachment3Img1").append("<img src=" + e.target.result + " style='height: 100%; width: 100%' id='Attachment3Img1'/>")
           // $('#Attachment3Img1').attr('src', e.target.result);
        }
        reader.readAsDataURL(this.files[0]);
        SelectedFile3 = $(this).prop("files");
        FileName3 = SelectedFile3[0].name;
    });

 
  

    $("#IdModelName").change(function ()
    {
        $("#IdModelName").removeClass("valdation");
    })

    //=======================================@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@=====(END)====@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@=========================//


    //============================================================ ElectricalCommissioningChecklist ================================//

    $.ElectricalCommissioningCheck = function ()
    {

        var ElectricalCommissioningCheckList = [];
        var ElectricalCommissioningChecklistDuplicateList = [];
        var appdisccount = 0;

        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetElectricalCommissioningChecklistForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    ElectricalCommissioningCheckList = resp

                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblElectricalCommissioningCheckListGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivElectricalCommissioningCheckListPager'></div >")
        $("#IdTblElectricalCommissioningCheckListGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetElectricalCommissioningChecklistDetails"),
           // caption: "Electrical Commissioning",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "ElectricalCommissioningChecklistIC",
            sortorder: "asc",
            pager: "#IdDivElectricalCommissioningCheckListPager",
            loadonce: true,
            colNames: ['Edit', ' ElectricalCommissioningChecklistIC', 'Description', 'Is applicable for electrical commissioning check-up', 'Is applicableFor electrical pre-commissioning check-up', 'Is applicable for electrical installation check-up', 'Is Active ?', 'Remarks', 'IsUpdate'],
            colModel: [                
                     { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },             
                {
                    name: 'ElectricalCommissioningChecklistIC', width: 90, align: 'center', editable: true, hidden: true

                },
                { name: 'Description', width: 250, align: 'center', editable: true, search: true },
                { name: 'IS_ApplicableFor_Electrical_CommissioningCheckUP', width: 300, align: 'center', formatter: IS_ApplicableFor_Electrical_CommissioningCheckUP, search: false },
                { name: 'IS_ApplicableFor_Electrical_PreCommissioningCheckUP', width: 300, align: 'center', formatter: IS_ApplicableFor_Electrical_PreCommissioningCheckUP, search: false },
                { name: 'IS_ApplicableFor_Electrical_InstallationCheckUP', width: 300, align: 'center', formatter: IS_ApplicableFor_Electrical_InstallationCheckUP, search: false },
                { name: 'Is_Active', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Remarks', width: 100, align: 'center', editable: true, search: true,hidden:true },
                { name: 'Isupdate', width: 150, align: 'left', search: false,hidden:true}
            ],
            multiselect: false,
            jsonReader:
                {
                    root: "root",
                    page: "page",
                    total: "total",
                    records: "records",
                    repeatitems: false,
                },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblElectricalCommissioningCheckListGrid").navGrid("#IdDivElectricalCommissioningCheckListPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblElectricalCommissioningCheckListGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblElectricalCommissioningCheckListGrid").navButtonAdd("#IdDivElectricalCommissioningCheckListPager",
            {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblElectricalCommissioningCheckListGrid").jqGrid("GridUnload");
                $.ElectricalCommissioningCheck();
            }
           })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblElectricalCommissioningCheckListGrid").navButtonAdd("#IdDivElectricalCommissioningCheckListPager",
            {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblElectricalCommissioningCheckListGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }
                testingrow = newRowId
                $("#IdTblElectricalCommissioningCheckListGrid").addRowData(newRowId, {});
                $("#IdTblElectricalCommissioningCheckListGrid").editRow(newRowId);
                $($("#IdTblElectricalCommissioningCheckListGrid tr")[newRowId]).children().eq(9).text("false")
                $($("#IdTblElectricalCommissioningCheckListGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
            });

        var isDuplicaterow = 0
        $("#IdTblElectricalCommissioningCheckListGrid").navButtonAdd("#IdDivElectricalCommissioningCheckListPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var rowid = $("#IdTblElectricalCommissioningCheckListGrid").getDataIDs();
                var ElectricalCommissioningChecklist = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                debugger

                for (i = 0; i <= rowid.length; i++)
                {
                    if ($($("#IdTblElectricalCommissioningCheckListGrid tr")[i]).attr("editable") == 1)
                    {
                        Rowselected++
                        var Description = $("#IdTblElectricalCommissioningCheckListGrid tr").eq(i).children().eq(3).children().val()
                        var isActive = 0                      
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1;
                        }
                        else {
                            isActive = 0;
                        }

                        var IS_ApplicableFor_Electrical_CommissioningCheckUP = 0;
                        if ($($(".ISApplicableForElectricalCommissioningCheckUP")[i - 1]).prop("checked") == true)
                        {
                            IS_ApplicableFor_Electrical_CommissioningCheckUP = 1;
                        }
                        else {
                            IS_ApplicableFor_Electrical_CommissioningCheckUP = 0;
                        }

                        var IS_ApplicableFor_Electrical_PreCommissioningCheckUP = 0;
                        if ($($(".ISApplicableForElectricalPreCommissioningCheckUP")[i - 1]).prop("checked") == true) 
                        {
                            IS_ApplicableFor_Electrical_PreCommissioningCheckUP = 1;
                        }
                        else {
                            IS_ApplicableFor_Electrical_PreCommissioningCheckUP = 0;
                        }

                        var IS_ApplicableFor_Electrical_InstallationCheckUP = 0;
                        if ($($(".ISApplicableForElectricalInstallationCheckUP")[i - 1]).prop("checked") == true)
                        {
                            IS_ApplicableFor_Electrical_InstallationCheckUP = 1;
                        }
                        else {
                            IS_ApplicableFor_Electrical_InstallationCheckUP = 0;
                        }

                        var IsUpdate = $("#IdTblElectricalCommissioningCheckListGrid tr").eq(i).children().eq(9).text()

                        var ElectricalCommissioningChecklistIC = 0;
                        if (IsUpdate)
                        {
                            ElectricalCommissioningChecklistIC = $("#IdTblElectricalCommissioningCheckListGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            ElectricalCommissioningChecklistIC = 0;
                        }

                        var IsValidateBit = 0
                        if (Description != "") {

                            var ElectricalCommissioningChecklistValues =
                                {
                                    ElectricalCommissioningChecklistIC: ElectricalCommissioningChecklistIC,
                                    Description: Description,                                    
                                    IS_ApplicableFor_Electrical_CommissioningCheckUP: IS_ApplicableFor_Electrical_CommissioningCheckUP,
                                    IS_ApplicableFor_Electrical_PreCommissioningCheckUP: IS_ApplicableFor_Electrical_PreCommissioningCheckUP,
                                    IS_ApplicableFor_Electrical_InstallationCheckUP:IS_ApplicableFor_Electrical_InstallationCheckUP,
                                    IsActive: IsActive,
                                    IsUpdate: IsUpdate,
                                    Remarks: "No remark"
                                }

                            ElectricalCommissioningChecklist.push(ElectricalCommissioningChecklistValues)
                            IsValidateBit = 0
                        }
                        else
                        {

                            IsValidateBit++
                        }

                    }
                    Description == "" ? $("#IdTblElectricalCommissioningCheckListGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblZeroDateMileStoneDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }
                ElectricalCommissioningChecklistDuplicateList = ElectricalCommissioningChecklist

                for (var i = 0; i < ElectricalCommissioningChecklist.length; i++) {
                    for (var j = 0; j < ElectricalCommissioningChecklistDuplicateList.length; j++) {
                        if (i != j) {
                            if (ElectricalCommissioningChecklistDuplicateList[i].Description.toString().toUpperCase().trim() == ElectricalCommissioningChecklist[j].Description.toString().toUpperCase().trim()) {
                                isDuplicaterow++
                            }

                        }
                        else {


                        }
                    }
                }


                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("ElectricalCommissioningChecklistData", JSON.stringify(ElectricalCommissioningChecklist));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/Insert_ElectricalCommissioningChecklist"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp)
                                {
                                    AlertMessage("Records Saved Successfully", "success")
                                    $("#IdTblElectricalCommissioningCheckListGrid").jqGrid("GridUnload");
                                    $.ElectricalCommissioningCheck();
                                    ZeroDateMileStoneList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")

                }

            }
        });

        $(document).on('click', '#IdTblElectricalCommissioningCheckListGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblElectricalCommissioningCheckListGrid");
        })

        $(document).on('click', "#IdTblElectricalCommissioningCheckListGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblElectricalCommissioningCheckListGrid").delRowData(rowid1);}));

        $(document).on('click', "#IdTblElectricalCommissioningCheckListGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblElectricalCommissioningCheckListGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else
            {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblElectricalCommissioningCheckListGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblElectricalCommissioningCheckListGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        }))
    };
    //----------------------Contractor Details--------------------------
    var isUpdate = 0
    var IsEdit = 0
    var Contractoric = 0
    var isActivechecking = ""
    $.ContractorGrid = function ()
    {
        debugger
        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdTblContractorDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdDivContractorDetailsPager'></div>")
        $("#IdTblContractorDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetContractorDetails"),
          //  caption: "Contractor Details",
            datatype: "Json",
           // data: "local",
            mtype: "GET",
            height: 'auto',
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "ContractorName",
            sortorder: "asc",
            pager: "#IdDivContractorDetailsPager",
            loadonce: true,
            colNames: ['Edit', ' ContractorIC', ' Contractor', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'ContractorIC', width: 90, align: 'center', editable: true, hidden: true

                },

                {
                    name: 'ContractorName', width: 400, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < PalntTypeList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == PalntTypeList[i].Plant_typeName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Contractor Details Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },
                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, editable: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function ()
            {
               // console.log(PalntTypeList);
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblContractorDetailsGrid").navGrid("#IdDivContractorDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblContractorDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblContractorDetailsGrid").navButtonAdd("#IdDivContractorDetailsPager",
            {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblContractorDetailsGrid").jqGrid("GridUnload");;
                $.ContractorGrid();
            }
        })
        $("#IdTblContractorDetailsGrid").navButtonAdd("#IdDivContractorDetailsPager",
            {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function ()
            {
                $("#IdTxtContractorName").removeClass("valdation");
                rowidtoAdd = $("#IdTblContractorDetailsGrid").getDataIDs();
                if (rowidtoAdd == "")
                {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length
                }
               // var name = $($("#IdTblContractorDetailsGrid tr")[newRowId]).children().eq(3).text()

                $("#IdDivModelContractor").modal({ backdrop: false });
                $.SiteContractor(0)
                $("#IdTxtContractorName").val(" ")
                $("#IdIsActiveContractor").prop("checked", true)
            }
            });
       
        $(document).on("click", "#IdTblContractorDetailsGrid .ClsEditContactDetails", function ()
        {         
            $("#IdDivModelContractor").modal({ backdrop: false })
            Contractoric =$(this).parent().next().text()
            var Name = $(this).parent().next().next().text()
           
            var isactive = $(this).parent().next().next().next().children().eq(0).val()
            isActivechecking = isactive;
            var name = $("#IdTxtContractorName").val(Name)

            if (isactive == "true") {

                $("#IdIsActiveContractor").prop("checked", true)
            }
            else {
                $("#IdIsActiveContractor").prop("checked", false)
            }
            isUpdate = 1
            IsEdit = 1
            $.SiteContractor(Contractoric)
        })
        var isDuplicaterow = 0

    }


    $.Contractor = function ()
    {
        var PalntTypeList = [];
        var PalntTypeDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetPlantTypeListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    PalntTypeList = resp
                    console.log(resp)
                },
                error: function (resp)
                {
                    AlertMessage("Failed", "error");
                },
        })
        }
        $.ajaxfunc()             
        $("#IdTblContractorDetailsGrid").jqGrid("GridUnload");
        $.ContractorGrid();
       var testingrow = 0;
        var counter = 0;        
    }
    var checkedornot = 0
    var IsClicked=0
    $(document).on('click', '#IdIsActiveContractor', function () {
        //$("#Public_Web").click(function() {
        IsClicked++
        if (this.checked) {
            checkedornot = 1
        }
        else {
            checkedornot = 0
        }
    });
    var SiteContractorIC = 0
    var Iupdate = 0
    var isDuplicaterow = 0

    $(document).on('click', '#txtContractDetails', function () {
        debugger
       
        if (IsEdit == 0) {
            name = $("#IdTxtContractorName").val()
            var isactive = checkedornot
            var maxic = 0
            var a = []
            if (name != " ") {
                $("#IdTxtContractorName").removeClass("valdation")
                var Values =
                {
                    ContractorIC: Contractoric,
                    ContractorName: name,
                    IsActive: 1,
                    //Isupdate: false,
                    Remarks: "",
                }
                a.push(Values)

                $.ajax({
                    url: AbsolutePath("/Settings/UpdateContractorRecords"), //UploadFiles
                    type: "POST",
                    datatype: "JSON",
                    async: false,
                    //processData: false,
                    //contentType: false,
                    data: { pd: JSON.stringify(a) },
                    success: function (resp) {
                      


                        AlertMessage("Records Saved Successfully", "success");
                        //$.ContractorGrid();
                        //PalntTypeList = ""
                        //$.ajaxfunc()
                        //$("#IdDivModelContractor").modal("hide");


                        maxic = resp
                        var rowid = $("#IdTblSiteContractorGrid").getDataIDs();
                        var PlanttypeIdData = [];
                        var ToAvoidFirstentry = 0;
                        var Rowselected = 0;
                        var sicrow=0
                        //var eee = (testingrow - counter);
                        for (i = 0; i <= rowid.length; i++) {
                            if ($($("#IdTblSiteContractorGrid tr")[i]).attr("editable") == 1) {
                                Rowselected++
                                sicrow++
                                //var SiteContractorIC = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(2).children().val()
                                var ContractorIC = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(3).children().val()
                                var Name = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(4).children().val()
                                var MobileNo = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(5).children().val()
                                var IsUpdate = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(7).children().val()
                                var PlantTypeIc = 0;
                                if (IsUpdate) {
                                    SiteContractorIC = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(2).children().val()
                                }
                                else {
                                    SiteContractorIC = 0;
                                }
                                var isActive = 0
                                if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                                    isActive = 1

                                }
                                else {
                                    isActive = 0

                                }
                                var IsValidateBit = 0
                                if (Name != "") {

                                 
                                    var PlanttypeValues =
                                        {
                                            ContractorSitePersonDetailsIC: SiteContractorIC,
                                            ContractorIC: maxic,
                                            Name: Name,
                                            MobileNumber: MobileNo,
                                            isActive: isActive,
                                            IsUpdate: IsUpdate,
                                            Remarks: "No Remarks",
                                        }

                                    PlanttypeIdData.push(PlanttypeValues)
                                    IsValidateBit = 0
                                }
                                else {
                                    IsValidateBit++
                                }
                            }
                            Name == "" ? $("#IdTblSiteContractorGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblSiteContractorGrid tr").eq(i).children().eq(3).css("border", "")
                        }
                        debugger
                        PalntTypeDuplicateList = PlanttypeIdData

                        //$(".Colsemodal").trigger("click");
                       // $('#IdTblSiteContractorGrid').jqGrid('trigger', 'reloadGrid')
                        // $.Contractor()

                        if (Rowselected != 0) {
                            var _formData = new FormData();
                            _formData.append("ScData", JSON.stringify(PlanttypeIdData));
                            if (IsValidateBit == 0) {
                                if (isDuplicaterow == 0) {
                                    $.ajax({
                                        url: AbsolutePath("/Settings/InsertToSiteContractorDetails"), //UploadFiles
                                        type: "POST",
                                        datatype: "JSON",
                                        processData: false,
                                        contentType: false,
                                        data: _formData,
                                        success: function (resp) {
                                            $(".Colsemodal").trigger("click");
                                           // $("#IdTblSiteContractorGrid").jqGrid("GridUnload");$.Contractor();
                                            AlertMessage("Records Saved Successfully", "success"); $('#IdTblSiteContractorGrid').jqGrid('trigger', 'reloadGrid')
                                            PalntTypeList = ""
                                            $.ajaxfunc()
                                        },
                                        error: function (resp) { AlertMessage("Failed", "error"); },
                                    })

                                    $(".Colsemodal").trigger("click");
                                    $.Contractor()
                                    counter = 0;
                                }
                                else {
                                    AlertMessage("Duplicate Records Exists ", "warning")
                                    isDuplicaterow = 0

                                }
                            }
                            else {
                                AlertMessage("Please Fill Mandatory Field(s)", "warning")

                            }
                        }
                        //else {
                        //    AlertMessage("Please Select Or Add Record", "warning")
                        //}
                        if (sicrow == 0) {
                            $(".Colsemodal").trigger("click");
                            $.Contractor()
                        }



                        if (isUpdate == 1) {





                        }
                        else {


                        }


                    },
                    error: function (resp) { AlertMessage("Failed", "error"); },
                })
                $('#IdTblSiteContractorGrid').jqGrid('trigger', 'reloadGrid')
            }
            else {

                AlertMessage("Please Fill Contractor Name", "warning"); 
                $("#IdTxtContractorName").addClass("valdation")
            }
        }

        else {
            name = $("#IdTxtContractorName").val()
            var isactive=""
            if (IsClicked > 0) { isactive = checkedornot; IsClicked=0 } else { isactive = isActivechecking == "true" ? 1 : 0 }
                    
            var maxic = 0
            var a = []
            if (name != "") {
                var Values = {

                    ContractorIC: Contractoric,
                    ContractorName: name,
                    IsActive: isactive,
                    //Isupdate: false,
                    Remarks: "No Remarks",


                }
                a.push(Values)
                $.ajax({
                    url: AbsolutePath("/Settings/UpdateContractorRecords"), //UploadFiles
                    type: "POST",
                    datatype: "JSON",
                    async: false,
                    //processData: false,
                    //contentType: false,
                    data: { pd: JSON.stringify(a) },
                    success: function (resp) {
                        
                        AlertMessage("Records Saved Successfully", "success")
                        PalntTypeList = ""
                        $.ajaxfunc()

                        maxic = resp

                        var rowid = $("#IdTblSiteContractorGrid").getDataIDs();


                        var PlanttypeIdData = [];
                        var ToAvoidFirstentry = 0;
                        var Rowselected = 0;
                        //var eee = (testingrow - counter);
                        for (i = 0; i <= rowid.length; i++) {
                            if ($($("#IdTblSiteContractorGrid tr")[i]).attr("editable") == 1) {
                                Rowselected++
                                var SiteContractorIC = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(2).children().val()
                                var ContractorIC = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(3).children().val()
                                var Name = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(4).children().val()
                                var MobileNo = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(5).children().val()
                                var IsUpdate = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(7).children().val()
                                var PlantTypeIc = 0;
                                if (IsUpdate) {
                                    SiteContractorIC = $("#IdTblSiteContractorGrid tr").eq(i).children().eq(2).children().val()
                                }
                                else {
                                    SiteContractorIC = 0;
                                }
                                var isActive = 0
                                if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                                    isActive = 1

                                }
                                else {
                                    isActive = 0

                                }
                                var IsValidateBit = 0
                                if (Name != "") {

                                  
                                    var PlanttypeValues =
                                        {
                                            ContractorSitePersonDetailsIC: SiteContractorIC,
                                            ContractorIC: Contractoric,
                                            Name: Name,
                                            MobileNumber: MobileNo,
                                            isActive: isActive,
                                            IsUpdate: IsUpdate,
                                            Remarks: "No Remarks",
                                        }

                                    PlanttypeIdData.push(PlanttypeValues)
                                    IsValidateBit = 0
                                }
                                else {
                                    IsValidateBit++
                                }
                            }
                            Name == "" ? $("#IdTblSiteContractorGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdTblSiteContractorGrid tr").eq(i).children().eq(3).css("border", "")
                        }
                        debugger

                        PalntTypeDuplicateList = PlanttypeIdData

                        if (Rowselected != 0) {
                            var _formData = new FormData();
                            _formData.append("ScData", JSON.stringify(PlanttypeIdData));
                            if (IsValidateBit == 0) {
                                if (isDuplicaterow == 0) {
                                    $.ajax({
                                        url: AbsolutePath("/Settings/InsertToSiteContractorDetails"), //UploadFiles
                                        type: "POST",
                                        datatype: "JSON",
                                        processData: false,
                                        contentType: false,
                                        data: _formData,
                                        success: function (resp) {
                                            $.Contractor()
                                            $("#IdTblSiteContractorGrid").jqGrid("GridUnload");
                                            AlertMessage("Records Saved Successfully", "success")
                                            PalntTypeList = ""
                                            $.ajaxfunc()
                                            $(".Colsemodal").trigger("click");
                                        },
                                        error: function (resp) { AlertMessage("Failed", "error"); },
                                    })

                                    counter = 0;
                                }
                                else {
                                    AlertMessage("Duplicate Records Exists ", "warning")
                                    isDuplicaterow = 0

                                }
                            }
                            else {
                                AlertMessage("Please Fill Mandatory Field(s)", "warning")

                            }
                        }
                        else {
                            //AlertMessage("Please Select Or Add Record", "warning")
                        }







                    },
                    error: function (resp) { AlertMessage("Failed", "error"); },
                })
            }
            else {
                AlertMessage("Please Fill Contractor ", "warning")
            }


            $.Contractor()
            $("#IdTblSiteContractorGrid").jqGrid("GridUnload");

            $(".Colsemodal").trigger("click");
        }     

    })

    //----------------------------Site Contractor-----------
    $.SiteContractor = function (Contractorid) {
        var PalntTypeList = [];
        var PalntTypeDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetPlantTypeListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    PalntTypeList = resp
                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#IdDivGridCheckSheet").html("");
        $("#IdDivGridCheckSheet").append("<table id='IdTblSiteContractorGrid'></table>")
        $("#IdDivGridCheckSheet").append("<div id='IdTblSiteContractorPager'></div>")
        $("#IdTblSiteContractorGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetsiteContractorDetails?contractorid=" + Contractorid),
            //caption: "Plant Type Details",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "Name",
            sortorder: "desc",
            pager: "#IdTblSiteContractorPager",            
            loadonce: true,
            colNames: ['Edit', ' ContractorSitePersonDetailsIC', 'ContractorIC', 'Name', 'Mobile Number', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'ContractorSitePersonDetailsIC', width: 90, align: 'center', editable: true, hidden:true

                },
                 {
                     name: 'ContractorIC', width: 90, align: 'center', editable: true, hidden: true

                 },

                {
                    name: 'Name', width: 200, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < PalntTypeList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == PalntTypeList[i].Plant_typeName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Site Contractor Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },
                 { name: 'MobileNumber', width: 100, align: 'left', editable: true, },
                { name: 'is_Active', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'IsUpdate', width: 150, align: 'left', search: true,editable:true, hidden:true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                //console.log(PalntTypeList);
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdTblSiteContractorGrid").navGrid("#IdTblSiteContractorPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdTblSiteContractorGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdTblSiteContractorGrid").navButtonAdd("#IdTblSiteContractorPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblPlantTypeDetailsGrid").jqGrid("GridUnload");
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdTblSiteContractorGrid").navButtonAdd("#IdTblSiteContractorPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdTblSiteContractorGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }
                testingrow = newRowId
                $("#IdTblSiteContractorGrid").addRowData(newRowId, {});
                $("#IdTblSiteContractorGrid").editRow(newRowId);
                $($("#IdTblSiteContractorGrid tr")[newRowId]).children().eq(7).text("false")
                $($("#IdTblSiteContractorGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0

       
        $(document).on('click', '#IdTblSiteContractorGrid .ClsEditContactDetails', function () {

            EditFunction(this, "IdTblSiteContractorGrid");
        })

        $(document).on('click', "#IdTblSiteContractorGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdTblSiteContractorGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdTblSiteContractorGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdTblSiteContractorGrid tr").eq(RefRowid).children().eq(6).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdTblSiteContractorGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdTblSiteContractorGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );
    }

    //----------------------ItemTypes---------------------

    $.Itemtypes = function () {
        var PalntTypeList = [];
        var IRDDataDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetPlantTypeListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp) {
                    PalntTypeList = resp
                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='ItemTypesDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='ItemTypesDetailsDetailsPager'></div >")
        $("#ItemTypesDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetItemTypesDetails"),
          //  caption: "Idle Report Designation",
            datatype: "Json",
           // data: "local",
            mtype: "GET",
            height: 'auto',            
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "ItemTypeName",
            sortorder: "asc",
            pager: "#ItemTypesDetailsDetailsPager",
            loadonce: true,
            colNames: ['Edit', ' SIC_ItemsTypesIC', 'Item Types', 'ISApplicable for NWS', 'ISApplicable for NWR', 'ISApplicable For HYB',
            'ISApplicable for MOD', 'ISApplicable For TRACK', 'ISApplicable For AC', 'ISApplicable For HRC', 'ISApplicable for RWK',
            'ISApplicable for Unit', 'ISApplicable for other', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'SIC_ItemsTypesIC', width: 90, align: 'center', hidden: true

                },

                {
                    name: 'ItemTypeName', width: 150, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < PalntTypeList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == PalntTypeList[i].Plant_typeName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Item Types Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },
                { name: 'ISApplicablefor_NWS', width: 150, align: 'center', formatter: ISApplicableforNWS, search: false },
            { name: 'ISApplicablefor_NWR', width: 150, align: 'center', formatter: ISApplicableforNWR, search: false },
    { name: 'ISApplicableFor_HYB', width: 150, align: 'center', formatter: ISApplicableforHYB, search: false },
    { name: 'ISApplicablefor_MOD', width: 150, align: 'center', formatter: ISApplicableforMOD, search: false },
    { name: 'ISApplicableFor_TRACK', width: 150, align: 'center', formatter: ISApplicableforTRACK, search: false },
        { name: 'ISApplicableFor_AC', width: 150, align: 'center', formatter: ISApplicableforAC, search: false },
            { name: 'ISApplicableFor_HRC', width: 150, align: 'center', formatter: ISApplicableforHRC, search: false },
    { name: 'ISApplicable_RWK', width: 150, align: 'center', formatter: ISApplicableforRWK, search: false },
    { name: 'ISApplicablefor_Unit', width: 150, align: 'center', formatter: ISApplicableforUnit, search: false },
     { name: 'ISApplicablefor_other', width: 150, align: 'center', formatter: ISApplicableforother, search: false },
           { name: 'IsActive', width: 150, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true,hidden:true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function (data) {
                console.log(data);
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#ItemTypesDetailsGrid").navGrid("#ItemTypesDetailsDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#ItemTypesDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#ItemTypesDetailsGrid").navButtonAdd("#ItemTypesDetailsDetailsPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#ItemTypesDetailsGrid").jqGrid("GridUnload");
                $.Itemtypes();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#ItemTypesDetailsGrid").navButtonAdd("#ItemTypesDetailsDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#ItemTypesDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#ItemTypesDetailsGrid").addRowData(newRowId, {});
                $("#ItemTypesDetailsGrid").editRow(newRowId);
                $($("#ItemTypesDetailsGrid tr")[newRowId]).children().eq(15).text("false")
                $($("#ItemTypesDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0

        $("#ItemTypesDetailsGrid").navButtonAdd("#ItemTypesDetailsDetailsPager", {
            title: 'd',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {
                debugger
                var rowid = $("#ItemTypesDetailsGrid").getDataIDs();
                var IRDData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++) {
                    if ($($("#ItemTypesDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        //var IRDIC = $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(2).children().val()
                        var Name = $("#ItemTypesDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#ItemTypesDetailsGrid tr").eq(i).children().eq(15).text()
                        var IRDIC = 0;
                        var NWS = 0
                        if ($($(".ISApplicableforNWSClass")[i - 1]).prop("checked") == true) {
                            NWS = 1

                        }
                        else {
                            NWS = 0

                        }
                        var NWr = 0
                        if ($($(".ISApplicableforNWRClass")[i - 1]).prop("checked") == true) {
                            NWr = 1

                        }
                        else {
                            NWr = 0

                        }
                        var HYB = 0
                        if ($($(".ISApplicableforHYBClass")[i - 1]).prop("checked") == true) {
                            HYB = 1
                        }
                        else {
                            HYB = 0
                        }
                        var MOD = 0
                        if ($($(".ISApplicableforMODClass")[i - 1]).prop("checked") == true) {
                            MOD = 1
                        }
                        else {
                            MOD = 0
                        }
                        var TRACK = 0
                        if ($($(".ISApplicableforTRACKClass")[i - 1]).prop("checked") == true) {
                            TRACK = 1
                        }
                        else {
                            TRACK = 0
                        }
                        var AC = 0
                        if ($($(".ISApplicableforACClass")[i - 1]).prop("checked") == true) {
                            AC = 1
                        }
                        else {
                            AC = 0
                        }

                        var HRC = 0
                        if ($($(".ISApplicableforHRCClass")[i - 1]).prop("checked") == true) {
                            HRC = 1
                        }
                        else {
                            HRC = 0
                        }
                        var RWK = 0
                        if ($($(".ISApplicableforRWKClass")[i - 1]).prop("checked") == true) {
                            RWK = 1
                        }
                        else {
                            RWK = 0
                        }
                        var Unit = 0
                        if ($($(".ISApplicableforUnitClass")[i - 1]).prop("checked") == true) {
                            Unit = 1
                        }
                        else {
                            Unit = 0
                        }
                        var other = 0
                        if ($($(".ISApplicableforotherClass")[i - 1]).prop("checked") == true) {
                            other = 1
                        }
                        else {
                            other = 0
                        }


                        if (IsUpdate == "true") {
                            ItemTypeIC = $("#ItemTypesDetailsGrid tr").eq(i).children().eq(2).text()
                        }
                        else {
                            ItemTypeIC = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0                       
                        if (Name != "") {
                   
                            var IRDtypeValues =
                                {
                                    SIC_ItemsTypesIC: ItemTypeIC,
                                    ItemTypeName: Name,
                                    ISApplicablefor_NWS: NWS,
                                    ISApplicablefor_NWR: NWr,
                                    ISApplicableFor_HYB: HYB,
                                    ISApplicablefor_MOD: MOD,
                                    ISApplicableFor_TRACK: TRACK,
                                    ISApplicableFor_AC: AC,
                                    ISApplicableFor_HRC: HRC,
                                    ISApplicable_RWK: RWK,
                                    ISApplicablefor_Unit: Unit,
                                    ISApplicablefor_other: other,
                                    IsActive: isActive,
                                    Isupdate: IsUpdate,
                                    Remarks: "No Remarks",
                                }

                            IRDData.push(IRDtypeValues)
                            IsValidateBit = 0
                        }
                        else {
                            IsValidateBit++
                        }
                    }
                    Name == "" ? $("#ItemTypesDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#ItemTypesDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }
                IRDDataDuplicateList = IRDData;

                for (var i = 0; i < IRDData.length; i++) {
                    for (var j = 0; j < IRDDataDuplicateList.length; j++) {
                        if (i != j) {
                            if (IRDData[i].Name.toString().toUpperCase().trim() == IRDDataDuplicateList[j].Name.toString().toUpperCase().trim()) {
                                isDuplicaterow++
                            }

                        }
                        else {


                        }
                    }
                }



                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("IRDData", JSON.stringify(IRDData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertToItemTypes"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    $("#ItemTypesDetailsGrid").jqGrid("GridUnload");
                                    $.Itemtypes();
                                    AlertMessage("Records Saved Successfully", "success")
                                    PalntTypeList = ""
                                    $.ajaxfunc()
                                    $.Itemtypes()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")
                }

            }
        });

        $(document).on('click', '#ItemTypesDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "ItemTypesDetailsGrid");
        })

        $(document).on('click', "#ItemTypesDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#ItemTypesDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#ItemTypesDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#ItemTypesDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#ItemTypesDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#ItemTypesDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );
    }

    //----------------------IdleReportDesignation---------------------
    $.IdleReportDesignation = function ()
    {
        var PalntTypeList = [];
        var IdlereportdesignationDetailsDuplicateList = [];
        var appdisccount = 0;
        $.ajaxfunc = function ()
        {
            $.ajax({
                url: AbsolutePath("/Settings/GetPlantTypeListForValidation"), //UploadFiles
                type: "Get",
                success: function (resp)
                {
                    PalntTypeList = resp
                    console.log(resp)
                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()

        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='IdlereportdesignationDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='IdlereportdesignationDetailsPager'></div >")
        $("#IdlereportdesignationDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetIdleReportDesignationDetails"),
          //  caption: "Idle Report Designation",
            datatype: "Json",
            data: "local",
            mtype: "GET",
            height: 'auto',
            width:500,
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "IdleReportDesignation_Name",
            sortorder: "desc",
            pager: "#IdlereportdesignationDetailsPager",
            loadonce: true,
            colNames: ['Edit', ' IdleReportDesignationIC', ' Designation', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'IdleReportDesignationIC', width: 90, align: 'center', hidden: true

                },

                {
                    name: 'IdleReportDesignation_Name', width: 150, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                                type: "focus",
                                fn: function () {
                                    $(this).css("border", "");

                                },
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    var appdisc = $(this).val()

                                    for (i = 0; i < PalntTypeList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == PalntTypeList[i].Plant_typeName.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("IdleReport Designation Already Exists", "warning")
                                    }
                                },
                            },
                        ]
                    }

                },
                { name: 'ISActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true, hidden: true },
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function ()
            {                
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#IdlereportdesignationDetailsGrid").navGrid("#IdlereportdesignationDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#IdlereportdesignationDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,                
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#IdlereportdesignationDetailsGrid").navButtonAdd("#IdlereportdesignationDetailsPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdlereportdesignationDetailsGrid").jqGrid("GridUnload");;
                $.IdleReportDesignation();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#IdlereportdesignationDetailsGrid").navButtonAdd("#IdlereportdesignationDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function () {
                rowidtoAdd = $("#IdlereportdesignationDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#IdlereportdesignationDetailsGrid").addRowData(newRowId, {});
                $("#IdlereportdesignationDetailsGrid").editRow(newRowId);
                $($("#IdlereportdesignationDetailsGrid tr")[newRowId]).children().eq(5).text("false")
                $($("#IdlereportdesignationDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                $(".enable").prop("enable", true)
                counter++
            }
        });

        var isDuplicaterow = 0

        $("#IdlereportdesignationDetailsGrid").navButtonAdd("#IdlereportdesignationDetailsPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var rowid = $("#IdlereportdesignationDetailsGrid").getDataIDs();
                var IdlereportdesignationDetailsData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++)
                {
                    if ($($("#IdlereportdesignationDetailsGrid tr")[i]).attr("editable") == 1) {
                        Rowselected++
                        //var IRDIC = $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(2).children().val()
                        var IRDName = $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var IsUpdate = $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(5).text()
                        var IRDIC = 0;
                        if (IsUpdate == "true") {
                            IRDIC = $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(2).text()
                        }
                        else {
                            IRDIC = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0

                        if (IRDName != "") {
    
                            var IRDtypeValues =
                                {
                                    IdleReportDesignationIC: IRDIC,
                                    IdleReportDesignation_Name: IRDName,
                                    ISActive: isActive,
                                    Isupdate: IsUpdate,
                                    Remarks: "No Remarks",
                                }

                            IdlereportdesignationDetailsData.push(IRDtypeValues)
                            IsValidateBit = 0
                        }
                        else {
                            IsValidateBit++
                        }
                    }
                    IRDName == "" ? $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }

                IdlereportdesignationDetailsDuplicateList = IdlereportdesignationDetailsData;
                for (var i = 0; i < IdlereportdesignationDetailsData.length; i++) {
                    for (var j = 0; j < IdlereportdesignationDetailsDuplicateList.length; j++) {
                        if (i != j) {
                            if (IdlereportdesignationDetailsData[i].IdleReportDesignation_Name.toString().toUpperCase().trim() == IdlereportdesignationDetailsDuplicateList[j].IdleReportDesignation_Name.toString().toUpperCase().trim())
                            {
                                isDuplicaterow++
                            }
                        }
                        else {
                        }
                    }
                }


                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("IRDData", JSON.stringify(IdlereportdesignationDetailsData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertToIdleReportDedignation"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    $("#IdlereportdesignationDetailsGrid").jqGrid("GridUnload");;
                                    $.IdleReportDesignation();
                                    AlertMessage("Records Saved Successfully", "success")
                                    PalntTypeList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else {
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {
                    AlertMessage("Please Select Or Add Record", "warning")
                }

            }
        });

        $(document).on('click', '#IdlereportdesignationDetailsGrid .ClsEditContactDetails', function ()
        {
            EditFunction(this, "IdlereportdesignationDetailsGrid");
        })

        $(document).on('click', "#IdlereportdesignationDetailsGrid .ClsDelContactDetails", (function ()
        {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#IdlereportdesignationDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#IdlereportdesignationDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#IdlereportdesignationDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#IdlereportdesignationDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#IdlereportdesignationDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );
    }

    //----------------------Models---------------------
    $.Models = function () {
        var PalntTypeList = [];
        var ModelsDetailsList = [];
        var appdisccount = 0;
        $.ajaxfunc = function () {
            $.ajax({
                url: AbsolutePath("/Settings/GetModelListforValidation"), //UploadFiles
                type: "Get",
                async: false,
                success: function (resp) {
                    PalntTypeList = resp
                    console.log(resp)

                },
                error: function (resp) { AlertMessage("Failed", "error"); },
            })
        }
        $.ajaxfunc()
        var valuetesting=''
        $("#DivMasterGrid").html("");
        $("#DivMasterGrid").append("<table id='ModelsDetailsGrid'></table>")
        $("#DivMasterGrid").append("<div id='ModelsDetailsPager'></div >")
        $("#ModelsDetailsGrid").jqGrid({
            ignoreCase: true,
            url: AbsolutePath("/Settings/GetModelDetails"),
           // caption: "Models",
            datatype: "Json",
           // data: "local",
            mtype: "GET",
            height: 'auto',
            viewrecords: true,
            rowList: [15, 20, 50, 100],
            rowNum: 20,
            rownumbers: true,
            sortname: "SIC_Models_Name",
            sortorder: "asc",
            pager: "#ModelsDetailsPager",
            loadonce: true,
            colNames: ['Edit', 'ModelsIC', 'Models', 'Item Type', 'Is Active ?', 'IsUpdate'],
            colModel: [
                { name: 'Edit', width: 40, align: 'center', formatter: view, search: false },
                {
                    name: 'SIC_ModelsIC', width: 90, align: 'center', hidden: true,editable: true,focus:true

                },

                {
                    name: 'SIC_Models_Name', width: 150, align: 'left', editable: true,
                    editoptions: {

                        dataEvents: [
                            {
                               
                                type: "blur",
                                fn: function () {
                                    $(this).css("border", "");
                                    debugger
                                   
                                    var appdisc = $(this).val()

                                    for (i = 0; i < PalntTypeList.length; i++) {
                                        if (appdisc.toString().toUpperCase().trim() == PalntTypeList[i].SIC_Models_Name.toString().toUpperCase().trim()) {

                                            appdisccount++;
                                        }
                                    }
                                    if (appdisccount != 0) {
                                        this.value = "";
                                        appdisccount = 0;
                                        AlertMessage("Model Details Already Exists", "warning")
                                        valuetesting=""
                                    }
                                },

                            
                            },

                        ]
                    }

                },
                       {
                           name: 'ItemTypeName', width: 150, align: 'left',
                           search: true,
                           editable: true,
                           edittype: 'select',
                           editoptions: {
                               class: '',
                               dataUrl: AbsolutePath('/Settings/GetItemList'),
                               dataEvents: [
                                   {
                                       type: "focus",
                                       fn: function () {
                                           $(this).css("border", "");

                                       }

                                   }]
                           }

                       },
                { name: 'IsActive', width: 100, align: 'center', formatter: IsActive, search: false },
                { name: 'Isupdate', width: 150, align: 'left', search: true,hidden:true},
            ],
            multiselect: false,
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                //console.log(PalntTypeList);
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
        });
        $("#ModelsDetailsGrid").navGrid("#ModelsDetailsPager", { add: false, del: false, edit: false, refresh: false, search: false });
        $('#ModelsDetailsGrid').jqGrid('filterToolbar',
            {
                stringResult: true,
                searchOnEnter: true,
                searchOperators: false,
                //enableClear: false,
                //clearSearch: false,
                defaultSearch: "cn",
                sopt: ["cn", "eq", "neq"],
                resetIcon: "",
                ignoreCase: true
            });
        $("#ModelsDetailsGrid").navButtonAdd("#ModelsDetailsPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#ModelsDetailsGrid").jqGrid("GridUnload");;
                $.Models();
            }
        })
        var testingrow = 0;
        var counter = 0;

        $("#ModelsDetailsGrid").navButtonAdd("#ModelsDetailsPager", {
            title: 'Add',
            caption: "",
            buttonicon: 'ui-icon-plus',
            onClickButton: function ()
            {
                rowidtoAdd = $("#ModelsDetailsGrid").getDataIDs();
                if (rowidtoAdd == "") {
                    var newRowId = 1
                }
                else {
                    var newRowId = rowidtoAdd.length + 1
                }

                testingrow = newRowId
                $("#ModelsDetailsGrid").addRowData(newRowId, {});
                $("#ModelsDetailsGrid").editRow(newRowId);
                $($("#ModelsDetailsGrid tr")[newRowId]).children().eq(6).text("false")
                $($("#ModelsDetailsGrid tr")[newRowId]).children().eq(1).children().removeClass("glyphicon-pencil ClsEditContactDetails").addClass("glyphicon-minus ClsDelContactDetails");
                counter++
            }
        });

        var isDuplicaterow = 0
      //  $self.jqGrid("editRow", rowid, { focusField: e.target });
        $("#ModelsDetailsGrid").navButtonAdd("#ModelsDetailsPager", {
            title: 'Save',
            caption: "",
            buttonicon: 'ui-icon-disk',
            onClickButton: function () {

                var selectedid = [];
                var rowid = $("#ModelsDetailsGrid").getDataIDs();
                var ModelsDetailsData = [];
                var ToAvoidFirstentry = 0;
                var Rowselected = 0;
                var eee = (testingrow - counter);
                for (i = 0; i <= rowid.length; i++)
                {
                    if ($($("#ModelsDetailsGrid tr")[i]).attr("editable") == 1)
                    {
                        selectedid.push(i)
                        debugger
                        Rowselected++
                        //var IRDIC = $("#IdlereportdesignationDetailsGrid tr").eq(i).children().eq(2).children().val()
                        // var ModelIC = $("#ModelsDetailsGrid tr").eq(i).children().eq(2).children().val()
                        var ModelName = $("#ModelsDetailsGrid tr").eq(i).children().eq(3).children().val()
                        var itemTypeName = $("#ModelsDetailsGrid tr").eq(i).children().eq(4).children().find("option:selected").text()
                        var itemTypeIc = $("#ModelsDetailsGrid tr").eq(i).children().eq(4).children().val()
                        var IsUpdate = $("#ModelsDetailsGrid tr").eq(i).children().eq(6).text()

                        var ModelIC = 0;
                        if (IsUpdate == "true") {
                            ModelIC = $("#ModelsDetailsGrid tr").eq(i).children().eq(2).children().val()
                        }
                        else {
                            ModelIC = 0;
                        }
                        var isActive = 0
                        if ($($(".IDISActiveClass")[i - 1]).prop("checked") == true) {
                            isActive = 1

                        }
                        else {
                            isActive = 0

                        }
                        var IsValidateBit = 0

                        if (ModelName != "" && itemTypeIc != 0) {
                        
                            var IRDtypeValues =
                                {
                                    SIC_ModelsIC: ModelIC,
                                    SIC_Models_Name: ModelName,
                                    ItemTypeName: itemTypeName,
                                    SIC_ItemsTypesIC: itemTypeIc,
                                    IsActive: isActive,
                                    Isupdate: IsUpdate,
                                    Remarks: "No Remarks",
                                }

                            ModelsDetailsData.push(IRDtypeValues)
                            IsValidateBit = 0
                        }
                        else
                        {
                            IsValidateBit++
                        }
                    }
                    ModelName == "" ? $("#ModelsDetailsGrid tr").eq(i).children().eq(3).children().css("border", "solid red 1px") : $("#ModelsDetailsGrid tr").eq(i).children().eq(3).css("border", "")
                }
               
                ModelsDetailsList = ModelsDetailsData;

                for (var i = 0; i < ModelsDetailsData.length; i++) {
                    for (var j = 0; j < ModelsDetailsList.length; j++) {
                        if (i != j) {
                            if (ModelsDetailsData[i].ModelName.toString().toUpperCase().trim() == ModelsDetailsList[j].ModelName.toString().toUpperCase().trim()) {
                                isDuplicaterow++
                            }
                        }
                        else {
                        }
                    }
                }

                if (Rowselected != 0) {
                    var _formData = new FormData();
                    _formData.append("ModelData", JSON.stringify(ModelsDetailsData));
                    if (IsValidateBit == 0) {
                        if (isDuplicaterow == 0) {
                            $.ajax({
                                url: AbsolutePath("/Settings/InsertToModels"), //UploadFiles
                                type: "POST",
                                datatype: "JSON",
                                processData: false,
                                contentType: false,
                                data: _formData,
                                success: function (resp) {
                                    $("#ModelsDetailsGrid").jqGrid("GridUnload");;
                                    $.Models();
                                    AlertMessage("Records Saved Successfully", "success");
                                    PalntTypeList = ""
                                    $.ajaxfunc()
                                },
                                error: function (resp) { AlertMessage("Failed", "error"); },
                            })

                            counter = 0;
                        }
                        else {
                            AlertMessage("Duplicate Records Exists ", "warning")
                            isDuplicaterow = 0

                        }
                    }
                    else
                    {
                      
                        for (var m = 0; m < selectedid.length; m++)
                        {
                            $("#" + selectedid[m] + "_ItemTypeName").css("border", "solid red 1px");
                        }
                        AlertMessage("Please Fill Mandatory Field(s)", "warning")

                    }
                }
                else {                 
                    AlertMessage("Please Select Or Add Record", "warning")
                }

            }
        });

        $(document).on('click', '#ModelsDetailsGrid .ClsEditContactDetails', function () {

            EditFunction(this, "ModelsDetailsGrid");
        })

        $(document).on('click', "#ModelsDetailsGrid .ClsDelContactDetails", (function () {
            var rowid1 = ($(this).parent().parent().attr('id'));
            $("#ModelsDetailsGrid").delRowData(rowid1);
        })
        );

        $(document).on('click', "#ModelsDetailsGrid .ClsRefContactDetails", (function () {
            var RefRowid = ($(this).parent().parent().attr('id'));
            var IsactveCheck = $("#ModelsDetailsGrid tr").eq(RefRowid).children().eq(4).children().val()

            //if (Ischeckedornotbeforeresave == 1) {
            if (IsactveCheck == "true") {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", true)
            }
            else {
                $($(".IDISActiveClass")[RefRowid - 1]).prop("checked", false)
            }
            $("#ModelsDetailsGrid").restoreRow(RefRowid);
            $($(".IDISActiveClass")[RefRowid - 1]).attr("disabled", true)
            $($("#ModelsDetailsGrid tr")[RefRowid]).children().eq(1).children().removeClass("glyphicon-repeat ClsRefContactDetails").addClass("glyphicon-pencil  ClsEditContactDetails");
        })
        );
    }

</script>
