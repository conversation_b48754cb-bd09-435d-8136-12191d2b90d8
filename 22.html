<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Master</title>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        :root {
            /* Primary Colors */
            --primary-teal: #2EC0CB;
            --primary-teal-dark: #23A3AD;
            --primary-blue: #0F5FDC;
            --primary-white: #FFFFFF;
            --light-teal: #A4F4FF;
            --primary-color: #2EC0CB;
            --primary-color-dark-hover: #00d6c6;
            --primary-color-hover: #009e9e;
            /* Secondary Colors */
            --secondary-teal-dark: #23A3AD;
            --secondary-teal-light: #2EC0CB;
            --secondary-blue-light: #0F5FDC;
            --secondary-blue-extra-light: #8CC8FA;
            --secondary-gray-light: #DCE6F0;
            /* Tertiary Colors */
            --tertiary-navy: #000032;
            --tertiary-gray-light: #ECF3F8;
            --tertiary-black: #000000;
            --card-bg: #f7f7fc;
            /* Neutral & Text Colors */
            --text-primary: #000032;
            --text-secondary: #3a4a6b;
            --text-tertiary: #222222;
            --text-link: #0F5FDC;
            --text-link-hover: #0F5FDC;
            --text-white: #FFFFFF;
            --text-accent: #23A3AD;
            --text-accent-hover: #2EC0CB;
            --text-label: #6a7ba3;
            --text-error: #e53e3e;
            --text-error-alt: #e53935;
            --text-error-light: #e57373;
            --text-link-active: #0056d6;
            --text-sidebar-cart: #005e6a;
            --text-logo-sub-default: #1a237e;
            --text-feature-category: #181818;
            --logo-main-color-default: #23A3AD;
            --text-color-dark-1: #2d3748;
            --text-color-dark-2: #4a5568;
            --text-color-light-1: #718096;
            --text-color-light-2: #a0aec0;
            --text-disabled: #bdbdbd;
            --topbar-icon: #222b45;
            --filter-text-color: #006080;
            /* Background Colors */
            --background-primary: #ECF3F8;
            --background-secondary: #FFFFFF;
            --background-overlay: #00000099;
            --background-accent-light: #e0f7fa;
            --background-hover-light: #e0f4fa;
            --background-overlay-dark: #00000080;
            --background-overlay-light: #0000002E;
            --background-sidebar-overlay: #0020402E;
            --background-accent-light-alt: #e6fafd;
            --background-accent-dark-alt: #f7fafc;
            --background-overlay-cart-drag: #007BFF1A;
            --background-cart-hover: #edf2f7;
            --background-subscription-selected: #ebf8ff;
            --background-contact-phone: #2EC0CB1A;
            --background-contact-phone-hover: #2EC0CB26;
            --avatar-bg: #eef2ff;
            --avatar-color: #4338ca;
            --background-toggle-view: #f2f7fd;
            --background-toggle-view-hover: #e0eafc;
            --background-scrollbar-track: #f1f1f1;
            --background-scrollbar-thumb: #c1c1c1;
            --background-scrollbar-thumb-hover: #a8a8a8;
            --background-industry-header: #F5FAFFFA;
            --background-feature-category: #f3f8fc;
            --partner-logo-bg-color: #1a9ba8;
            /* Border Colors */
            --border-color: #DCE6F0;
            --border-color-accent: #2EC0CB;
            --border-color-dark: #23A3AD;
            --border-color-button: #b2e0ea;
            --border-color-filter: #f0f2f5;
            --border-color-light: #e0e6ed;
            --border-color-light-1: #e2e8f0;
            --border-color-light-2: #cbd5e0;
            --border-color-blue: #4299e1;
            --border-color-blue-light: #007bff;
            /* Gradient Colors */
            --hcl-gradient-color: linear-gradient(269.2deg, #3C91FF 1.02%, #2EC0CB 99.5%);
            --background-gradient-about: linear-gradient(45deg, #FFFFFF 60%, #e6fafd 100%);
            --background-gradient-about-overlay: linear-gradient(120deg, #2EC0CB 0%, #23A3AD 50%, #000032 100%);
            --background-gradient-cta: linear-gradient(90deg, #2EC0CB, #0F5FDC);
            --background-gradient-cta-hover: linear-gradient(90deg, #0F5FDC, #2EC0CB);
            --background-overlay-about-shine: linear-gradient(120deg, #2EC0CB1F 0%, #0F5FDC2E 100%);
            --border-image-gradient-cta: linear-gradient(90deg, #2EC0CB, #0F5FDC) 1;
            --border-image-gradient-cta-hover: linear-gradient(90deg, #0F5FDC, #2EC0CB) 1;
            --gradient-submit: linear-gradient(135deg, #2EC0CB 0%, #009e9e 100%);
            --gradient-submit-hover: linear-gradient(135deg, #009e9e 0%, #2EC0CB 100%);
            --gradient-submit-shine: linear-gradient(90deg, transparent, #FFFFFF33, transparent);
            --gradient-hero: linear-gradient(45deg, #2EC0CB, #23A3AD, #000032);
            --gradient-marquee-right: linear-gradient(to right, #FFFFFF, transparent);
            --gradient-marquee-left: linear-gradient(to left, #FFFFFF, transparent);
            --gradient-suite-tag: linear-gradient(90deg, #2EC0CB, #0F5FDC);
            --delete-gradient: linear-gradient(135deg, #fc8181 0%, #e53e3e 100%);
            --checkout-gradient: linear-gradient(90deg, #2EC0CB 60%, #23A3AD 100%);
            --checkout-gradient-hover: linear-gradient(90deg, #23A3AD 0%, #2EC0CB 100%);
            /* Tag & Status Colors */
            --tag-color-gold: #FFD700;
            --tag-color-orange: #ff9800;
            --tag-color-yellow: #ffd600;
            --tag-color-cyan: #00b6e6;
            --tag-color-purple: #5e35b1;
            --tag-color-green: #8bc34a;
            --tag-color-violet: #7c3aed;
            --tag-color-ocean: #0080c6;
            --tag-color-teal: #00897b;
            --tag-color-amber: #ffb300;
            /* Theme Status Color Palette */
            --success-main: #4CAF50;
            --success-dark: #689f38;
            --success-light: #dcedc8;
            --warning-main: #FFD600;
            --warning-dark: #f57c00;
            --warning-light: #fff3e0;
            --error-main: #e53e3e;
            --error-dark: #b71c1c;
            --error-light: #ffcdd2;
            --error-hover-color: #b71c1c;
            --info-main: #0F5FDC;
            --info-dark: #1565c0;
            --info-light: #bbdefb;
            /* Summary Card Icon Colors */
            --total-main: #51bd17;
            --active-main: #23A3AD;
            --inactive-main: #DCE6F0;
            --expired-main: #BDBDBD;
            --paid-main: #4CAF50;
            --unpaid-main: #e53e3e;
            --partially-paid-main: #FFD600;
            --approved-main: #23A3AD;
            --pending-main: #FFD600;
            --secondary-main: #2EC0CB;
            --primary-main: #0F5FDC;
            /* Chart Colors */
            --chart-bg-1: #2EC0BBB3;
            --chart-bg-2: #17707FB3;
            --chart-bg-3: #4338CAB3;
            --chart-bg-4: #F59E0BB3;
            /* Component & Miscellaneous Colors */
            --scrollbar-thumb-color-hover: #006666;
            --sidebar-close-btn-color: #0080c6;
            --accent-color-checkbox: #00b6e6;
            --plan-icon-color-silver: #b0b0b0;
            --platform-toggle-active: #14b8a6;
            --platform-toggle-active-bg: #f0fdfa;
            --platform-toggle-active-bg-alt: #ccfbf1;
            --platform-toggle-color: #444444;
            --platform-border-color: #d1d5db;
            --pagination-hover-bg: #f3f4f6;
            --pagination-hover-border: #9ca3af;
            --search-clear-bg: #e5e7eb;
            --search-clear-text: #6b7280;
            --search-clear-text-hover: #374151;
            --success-icon-color: #48bb78;
            --checkout-hover-bg: #0056b3;
            /* Shadows */
            --shadow-xs: 0 1px 2px #2C3E500D;
            --shadow-sm: 0 2px 8px #2C3E5014;
            --shadow-md: 0 4px 16px #2EC0CB26;
            --shadow-lg: 0 8px 32px #2C3E5021;
            --shadow-xl: 0 8px 40px #2C3E502E;
            --shadow-inset: 2px 0 12px 0 #0F5FDC0A;
            --shadow-sm-alt: 0 2px 8px #2C3E500F;
            --shadow-md-alt: 0 4px 16px #2C3E501A;
            --shadow-hero: 0 2px 4px #00000033;
            --shadow-btn: 0 2px 10px #2EC0CB21;
            --shadow-sidebar: 0 2px 8px 0 #0040800A;
            --shadow-sidebar-mobile: 0 0 32px 0 #0000002E;
            --shadow-sidebar-mobile-alt: 0 2px 8px 0 #0000000F;
            --shadow-sidebar-open: 0 0 32px 0 #2C3E5014;
            --shadow-filter-sidebar: 0 4px 24px #2EC0CB1A;
            --shadow-cart-card: 0 4px 6px -1px #0000001A, 0 2px 4px -1px #0000000F;
            --shadow-cart-card-hover: 0 10px 15px -3px #2C3E501A, 0 4px 6px -2px #2C3E500D;
            --shadow-delete-hover: 0 4px 12px #E53E3E4D;
            --shadow-checkout-hover: 0 4px 12px #007BFF4D;
            --shadow-add-hover: 0 4px 12px #00000026;
            --shadow-platform-toggle: 0 1px 2px #00000008;
            --shadow-platform-toggle-active: 0 2px 8px #14B8A614;
            --shadow-contact-form: 0 20px 60px #00000014;
            --shadow-contact-phone-hover: 0 8px 25px #2EC0CB33;
            --shadow-contact-input-focus: 0 0 0 4px #2EC0CB1A;
            --shadow-contact-input-invalid: 0 0 0 4px #E53E3E1A;
            --shadow-module-card: 0 4px 15px #0000000D;
            --shadow-module-card-hover: 0 8px 30px #0000001A;
            --shadow-card-selected: 0 0 0 2px #2EC0CB;
            --shadow-none: none;
            /* Sizing - Font */
            --font-family-base: 'Roboto', sans-serif !important;
            --font-weight-light: 300;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            --font-weight-extrabold: 800;
            --font-weight-heavy: 900;
            --font-size-Small: 0.80rem;
            --font-size-base: 1rem;
            --font-size-xxl: 1.5rem;
            --font-size-xxxl: 2rem;
            --font-size-hero: 3.5rem;
            --font-size-2_5-rem: 2.5rem;
            --font-size-4-rem: 4rem;
            --font-size-relative-lg: 1.1em;
            --font-size-relative-xl: 1.2em;
            --font-size-relative-xxl: 1.3em;
            --line-height-base: 1.5;
            --line-height-lg: 1.7;
            --line-height-sm: 1;
            --font-size-2_6-rem: 2.6rem;
            --font-size-2_7-rem: 2.7rem;
            --font-size-1_12-rem: 1.12rem;
            --font-size-1_15-rem: 1.15rem;
            --font-size-1_18-rem: 1.18rem;
            --font-size-0_75-rem: 0.75rem;
            --font-size-0_8-rem: 0.8rem;
            --font-size-0_9-rem: 0.9rem;
            --font-size-0_78-rem: 0.78rem;
            --font-size-0_92-rem: 0.92rem;
            --font-size-1_22-rem: 1.22rem;
            --font-size-1_4-rem: 1.4rem;
            --font-size-1_01-rem: 1.01rem;
            --font-size-0_93-rem: 0.93rem;
            --font-size-icon: 1.1rem;
            /* Sizing - Spacing & Units */
            --space-none: 0;
            --space-xxs: 4px;
            --space-xs: 8px;
            --space-sm: 12px;
            --space-md: 16px;
            --space-lg: 24px;
            --space-xl: 32px;
            --space-xld: 40px;
            --space-unit: 1rem;
            --space-unit-sm: 0.5rem;
            --space-unit-md: 1.5rem;
            --space-unit-lg: 2rem;
            --space-unit-xl: 2.5rem;
            --space-unit-xxl: 3rem;
            --space-unit-xxxl: 4rem;
            --space-0_2-rem: 0.2rem;
            --space-0_3-rem: 0.3rem;
            --space-0_4-rem: 0.4rem;
            --space-0_6-rem: 0.6rem;
            --space-0_7-rem: 0.7rem;
            --space-1_1-rem: 1.1rem;
            --space-1_2-rem: 1.2rem;
            --space-1_6-rem: 1.6rem;
            --space-0_45-rem: 0.45rem;
            --space-sidebar-padding-y: 18px;
            --space-sidebar-padding-x: 14px;
            --space-sidebar-gap: 14px;
            --space-sidebar-header-margin: 6px;
            --space-filter-group-padding: 10px;
            --space-filter-pills-gap: 6px;
            --space-filter-pill-padding-y: 6px;
            --space-filter-pill-padding-x: 14px;
            --space-filter-pill-margin-y: 1px;
            --space-filter-pill-margin-x: 2px;
            --space-filter-link-padding-y: 4px;
            --space-filter-link-padding-x: 10px;
            --space-filter-link-margin-bottom: 2px;
            --space-industry-checkbox-gap: 10px;
            --space-solution-type-gap: 7px;
            --space-sidebar-footer-gap: 18px;
            --space-icon-margin-right: 4px;
            --space-search-padding-y: 7px;
            --space-search-padding-x: 10px;
            --space-industry-list-gap: 4px;
            --space-0_32-rem: 0.32rem;
            /* Sizing - Borders & Radii */
            --border-width-sm: 1px;
            --border-width-md: 1.5px;
            --border-width-lg: 2px;
            --border-width-xl: 3px;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 22px;
            --radius-xxl: 50px;
            --radius-full: 50%;
            --radius-sidebar: 14px;
            --radius-button-sm: 24px;
            --radius-filter-sidebar: 18px;
            --radius-card: 16px;
            /* Transitions & Animations */
            --transition-duration-fast: 0.18s;
            --transition-duration-base: 0.22s;
            --transition-duration-slow: 0.32s;
            --transition-duration-filter-sidebar: 0.35s;
            --transition-opacity-filter-sidebar: 0.25s;
            --transition-easing-ease: ease;
            --transition-easing-cubic: cubic-bezier(0.4, 0, 0.2, 1);
            --transition-easing-in-out: cubic-bezier(0.4, 0, 0.2, 1);
            --animation-duration: 0.7s;
            --animation-duration-faq: 1s;
            --transition-duration-color: 0.2s;
            --fab-delay: 0ms;
            --animation-duration-long: 15s;
            --animation-duration-medium: 1.2s;
            --animation-duration-short: 0.9s;
            --animation-delay-sm: 0.1s;
            --animation-delay-md: 0.2s;
            --animation-delay-lg: 0.4s;
            --animation-duration-hero: 15s;
            --animation-duration-marquee: 60s;
            --animation-transition-slider: 0.4s;
            /* Z-Index */
            --z-index-default: 1;
            --z-index-low: 10;
            --z-index-low-mid: 10;
            --z-index-mid: 20;
            --z-index-mid-high: 30;
            --z-index-high: 2000;
            --z-index-modal: 1000;
            --z-index-header: 2000;
            --z-index-sidebar: 2102;
            --z-index-fab: 1200;
            --z-index-sidebar-overlay: 1001;
            --z-index-sidebar-advanced: 1002;
            --z-index-appbar: 100;
            /* Layout */
            --sidebar-width: 260px;
            --sidebar-width-collapsed: 70px;
            --sidebar-min-width: 220px;
            --sidebar-max-width: 270px;
            --sidebar-top: 80px;
            --sidebar-mobile-min-width: 80vw;
            --sidebar-mobile-max-width: 90vw;
            --auth-modal-width: 960px;
            --auth-modal-height: 600px;
            --auth-modal-min-width: 320px;
            --industry-image-thumb-size: 40px;
            --industry-image-thumb-size-sm: 32px;
            --industry-checkbox-min-height: 48px;
            --industry-icon-size: 18px;
            --industry-card-height: 140px;
            --industry-list-max-height: 245px;
            --min-height-sidebar: 400px;
            --breakpoint-md: 900px;
            --breakpoint-lg: 1400px;
            --max-width-ui: 1280px;
            --max-width-ui-lg: 1440px;
            --fab-icon-size: 36px;
            --filter-group-col-min-width: 180px;
            /* Miscellaneous (Non-Color) */
            --transform-btn-hover: translateY(-2px);
            --transform-sidebar-closed: translateX(-110%);
            --transform-sidebar-closed-alt: translateX(-120%);
            --transform-sidebar-open: translateX(0);
            --transform-fab-action: translateY(20px);
            --letter-spacing-normal: 0;
            --letter-spacing-logo-main: 0.01em;
            --letter-spacing-logo-sub: 0.08em;
            --letter-spacing-filter-label: 0.2px;
            --password-strength-width: 0%;
            --opacity-medium: 0.5;
            --logo-sub-margin-top: -0.2em;
            --transform-sidebar-mobile-closed: translateX(-100%);
            --transition-modal: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-modal-overlay: opacity 0.4s ease-in-out;
        }

        body {
            font-family: var(--font-family-base);
            background-color: var(--background-primary);
            color: var(--text-primary);
            overflow-y: auto !important;
        }

        body.modal-open-no-scroll {
            overflow: hidden;
        }

        /* Navbar and Header Styling */
        .navbar {
            box-shadow: var(--shadow-sm);
        }

        .navbar-brand {
            font-family: 'Roboto', sans-serif;
            font-weight: 700;
        }

        .main-content {
            padding: var(--space-unit-lg) 0;
        }

        /* Dashboard Metric Card Styling */
        .metric-card {
            border: none;
            border-radius: var(--radius-sm);
            background-color: var(--background-secondary);
            padding: var(--space-xxs);
            text-align: center;
            box-shadow: var(--shadow-sm);
        }

        .metric-card h5 {
            color: var(--text-label);
            font-size: var(--font-size-0_8-rem);
            margin-bottom: var(--space-0_2-rem);
        }

        .metric-card h3 {
            font-size: var(--font-size-xxl);
            font-weight: var(--font-weight-bold);
            font-family: 'Roboto', sans-serif;
        }

        .BtnAdd,
        .btnFilter,
        .btnAdvanceSearch {
            background-color: var(--primary-teal);
            color: var(--primary-white);
            transition: background-color 0.3s ease;
        }

        .BtnAdd:hover,
        .btnFilter:hover,
        .btnAdvanceSearch:hover,
        .btn-toggle-metrics:hover {
            background-color: var(--primary-teal-dark);
            color: var(--primary-white);
        }

        /* New style for the toggle metrics button */
        .btn-toggle-metrics {
            background-color: var(--primary-teal);
            color: var(--primary-white);
        }

        /* Custom CSS to handle the collapse animation without Bootstrap's JS */
        #metrics-panel {
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
            max-height: 200px;
        }

        #metrics-panel.collapsed {
            max-height: 0;
        }

        /* Search and Filter Bar Styling */
        .filter-bar {
            background-color: var(--background-secondary);
            padding: var(--space-unit-md);
            border-radius: var(--radius-card);
            box-shadow: var(--shadow-md);
        }

        /* Asset Card Styling */
        .asset-card,
        .list-card {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-card);
            background-color: var(--background-secondary);
            box-shadow: var(--shadow-xs);
            transition: transform var(--transition-duration-fast) var(--transition-easing-ease), box-shadow var(--transition-duration-fast) var(--transition-easing-ease);
            height: 100%;
            overflow: hidden;
        }

        .asset-card:hover,
        .list-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-sm-alt);
        }

        .asset-status {
            font-weight: var(--font-weight-semibold);
            padding: var(--space-0_2-rem) var(--space-0_6-rem);
            border-radius: var(--radius-sm);
        }

        .status-active {
            background-color: var(--success-light);
            color: var(--success-dark);
        }

        .status-idle {
            background-color: var(--warning-light);
            color: var(--warning-dark);
        }

        .status-retired {
            background-color: var(--error-light);
            color: var(--error-dark);
        }

        .card-header-custom {
            display: flex;
            align-items: center;
            gap: var(--space-unit);
            padding: var(--space-unit);
            border-bottom: 1px solid var(--border-color);
            background-color: var(--background-accent-light-alt);
        }

        .card-header-avatar {
            width: 4rem;
            height: 4rem;
            background-color: var(--primary-teal);
            color: var(--primary-white);
            border-radius: var(--radius-full);
            justify-content: center;
            align-items: center;
            font-size: var(--font-size-xxl);
            font-weight: var(--font-weight-bold);
            font-family: 'Roboto', sans-serif;
            align-content: center;
            text-align: center;
        }

        .card-body-custom {
            padding: var(--space-unit);
            border-bottom: 1px solid var(--border-color);
            font-size: var(--font-size-Small);
        }

        .card-footer-custom {
            padding: var(--space-unit);
            background-color: var(--background-secondary);
            font-size: var(--font-size-medium);
        }

        .info-row {
            padding: var(--space-unit-sm) 0;
        }

        .skill-badge {
            font-size: var(--font-size-0_8-rem);
            font-weight: var(--font-weight-medium);
            padding: var(--space-unit-sm) var(--space-0_75-rem);
            border-radius: var(--radius-sm);
            background-color: var(--secondary-gray-light);
            color: var(--text-secondary);
            white-space: nowrap;
        }

        .avatar-img {
            width: 100%;
            height: auto;
        }

        .form-control {
            max-width: 100% !important;
        }

        .btn.active {
            color: var(--primary-white);
            background-color: var(--primary-teal);
            border-color: var(--primary-teal);
        }

        /* Message Box Styling */
        .message-box {
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #28a745;
            color: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .message-box.show {
            display: block;
        }

        .message-close-btn {
            position: absolute;
            top: 5px;
            right: 10px;
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
        }

        /* Styles for the Bill of Materials Tree */
        .bom-tree ul {
            list-style: none;
            padding-left: 1.5rem;
        }

        .bom-tree li {
            position: relative;
            padding-left: 1rem;
            margin-bottom: 0.5rem;
        }

        .bom-tree .bom-item-icon {
            color: var(--primary-teal-dark);
            margin-right: 0.5rem;
        }

        .bom-tree .bom-toggle-btn {
            background: none;
            border: none;
            padding: 0;
            font-size: inherit;
            color: inherit;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            text-align: left;
        }

        .bom-tree .bom-toggle-btn .bi-chevron-right {
            transition: transform 0.2s ease-in-out;
        }

        .bom-tree .bom-toggle-btn[aria-expanded="true"] .bi-chevron-right {
            transform: rotate(90deg);
        }

        .text-teal {
            color: var(--primary-teal);
        }

        .nav-tabs .nav-link:not(.active) {
            color: var(--primary-teal);
        }

        /* --- New styles for the slide-in detail modal --- */
        .modal-container {
            position: fixed;
            top: 0;
            right: -100%;
            /* Start off-screen */
            width: 90%;
            /* Responsive width for mobile */
            max-width: 500px;
            /* Max width for desktop */
            height: 100%;
            background-color: white;
            box-shadow: var(--shadow-lg);
            transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2000;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .modal-container.show {
            right: 0;
            /* Slide in */
        }

        .modal-container.fullscreen {
            width: 100% !important;
            max-width: 100% !important;
            height: 100%;
            right: 0;
            border-radius: 0;
            box-shadow: none;
        }

        /* Fix for fullscreen header width */
        .modal-container.fullscreen .modal-header-custom {
            padding-left: 0;
            padding-right: 0;
        }

        .modal-body-custom {
            padding: 1.5rem;
            overflow-y: auto;
            /* Enable vertical scrolling */
            flex-grow: 1;
            /* Allow this section to grow and take up remaining space */
            display: flex;
            flex-direction: column;
        }

        .modal-body-fixed-content {
            flex-shrink: 0;
        }

        .modal-body-scrollable-content {
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .modal-container.fullscreen .modal-body-custom {
            display: flex;
            flex-direction: row;
            /* Change to row layout in fullscreen */
            align-items: stretch;
            flex-wrap: wrap;
        }

        .modal-container.fullscreen .modal-body-fixed-content {
            flex-grow: 1;
            padding: 2rem;
        }

        .modal-container.fullscreen .modal-body-scrollable-content {
            flex-grow: 2;
            padding: 2rem;
            overflow-y: auto;
        }



        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1999;
            opacity: 0;
            transition: opacity 0.4s ease-in-out;
            display: none;
        }

        .modal-overlay.show {
            display: block;
        }

        .modal-header-custom {
            padding: var(--space-unit-sm) var(--space-unit-lg);
            border-bottom: 1px solid var(--border-color);
            justify-content: space-between;
            align-items: center;
            background-color: var(--primary-teal);
            color: var(--primary-white);
        }

        .modal-body-custom {
            padding: 0;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .modal-body-fixed-content {
            padding: var(--space-unit-lg);
        }

        .modal-body-scrollable-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: 0 var(--space-unit-lg);
        }

        /* Responsive image fixes */
        .modal-preview-img {
            width: 100%;
            height: auto;
            max-height: 25vh;
            /* Sets a max height based on viewport height */
            object-fit: contain;
            /* Ensures the image fits without being distorted */
            border-radius: var(--radius-md);
            margin-bottom: var(--space-unit-md);
        }

        /* Fullscreen image styles. */
        .modal-container.fullscreen .modal-preview-img {
            max-height: 100%;
            max-width: 100%;
            object-fit: contain;
        }

        .modal-close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
        }

        .modal-info-row strong {
            display: inline-block;
            min-width: 120px;
        }



        #asset-detail-modal {
            position: fixed;
            top: 0;
            right: -500px;
            /* hidden offscreen by default */
            width: 500px;
            /* adjust width */
            height: 100vh;
            background: #fff;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            z-index: 1050;
            transition: right 0.3s ease-in-out;
        }


        #asset-detail-modal.show {
            right: 0;
            /* slide into view */
        }

        #asset-detail-modal.fullscreen {
            width: 100%;
        }

        .metric-card-icon {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .total-assets-icon {
            color: #0F5FDC;
        }

        .active-assets-icon {
            color: #2EC0CB;
        }

        .maintenance-icon {
            color: #f57c00;
        }

        .retired-icon {
            color: #e53e3e;
        }

        .collapse-smooth {
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
            max-height: 500px;
        }

        .collapse-smooth.collapsed {
            max-height: 0;
        }

        /* Custom modal for editing - regular popup modal */
        .modal-edit-container {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            width: 50%;
            transform: translateX(100%);
            background-color: var(--background-secondary);
            color: var(--text-color-primary);
            transition: transform 0.3s ease-in-out;
            z-index: 1051;
            display: flex;
            flex-direction: column;
        }

        .modal-edit-container.show {
            transform: translateX(0);
        }

        .modal-edit-content {
            background-color: var(--background-secondary);
            margin: 1% auto;
            padding: 0;
            border: 1px solid var(--border-color);
            width: 90%;
            max-width: 90%;
            height: 90vh;
            /* This sets a fixed height relative to the viewport height */
            border-radius: var(--radius-card);
            box-shadow: var(--shadow-lg);
            display: flex;
            /* Add this line */
            flex-direction: column;
            /* Add this line */
        }

        .modal-edit-header-custom {
            padding: var(--space-unit-lg);
            background-color: var(--primary-teal);
            border-top-left-radius: var(--radius-card);
            border-top-right-radius: var(--radius-card);
            color: var(--primary-white);
            position: relative;
        }

        .modal-edit-body-custom {
            flex-grow: 1;
            overflow-y: auto;
            /* Enable vertical scrolling */
            padding: 1rem 0;
        }

        .modal-edit-footer-custom {
            padding: var(--space-unit-md);
            background-color: var(--background-secondary);
            border-top: 1px solid var(--border-color);
            border-bottom-left-radius: var(--radius-card);
            border-bottom-right-radius: var(--radius-card);
        }

        /* New styles for the image preview modal */
        .image-preview-modal {
            display: none;
            /* Hidden by default */
            position: fixed;
            z-index: 3000;
            /* Higher z-index to appear on top of other modals */
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.9);
            align-items: center;
            justify-content: center;
        }

        .image-preview-modal.show {
            display: block;
        }

        .image-preview-content {
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90vh;
        }

        .image-preview-close {
            position: absolute;
            top: 20px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            transition: 0.3s;
            cursor: pointer;
        }

        .edit-avatar {
            cursor: pointer;
            /* Makes the image clickable */
            width: 5rem;
            height: 5rem;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid white;
        }

        #edit-avatar-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* New Styles for List View */
        .list-card {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .list-card .card-header-avatar {
            width: 3rem;
            height: 3rem;
            font-size: 1.25rem;
            flex-shrink: 0;
        }

        .list-card .asset-info {
            flex-grow: 1;
            padding-left: 1rem;
            text-align: left;
        }

        .list-card .asset-actions {
            flex-shrink: 0;
            display: flex;
            align-items: center;
        }


        /* New Styles for Table View */
        .table-view-container {
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            background-color: var(--background-secondary);
        }

        .table-view-container table {
            margin-bottom: 0;
        }

        .table-view-container th {
            background-color: var(--background-accent-light-alt);
            font-weight: 500;
        }

        .table-view-container tbody tr:hover {
            background-color: var(--background-hover-light);
            cursor: pointer;
        }

        .table-view-container .asset-actions {
            white-space: nowrap;
        }

        .table-view-container .badge {
            font-size: 0.75rem;
        }

        .category-dropdown-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .category-dropdown {
            width: 200px;
        }

        /* =================================== */
        /* Kanban View Styles         */
        /* =================================== */

        .kanban-board {
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            /* Allows horizontal scrolling for columns */
            padding-bottom: 1rem;
            /* Provides space for the scrollbar */
            min-height: 75vh;
            /* Ensures the board takes up significant screen height */
        }

        .kanban-column {
            flex: 0 0 320px;
            /* Prevents columns from shrinking/growing, sets a fixed width */
            max-width: 320px;
            background-color: var(--tertiary-gray-light);
            border-radius: var(--radius-md);
            display: flex;
            flex-direction: column;
            box-shadow: var(--shadow-xs);
            border: 1px solid var(--border-color);
        }

        .kanban-column-header {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--background-secondary);
            border-top-left-radius: var(--radius-md);
            border-top-right-radius: var(--radius-md);
        }

        .kanban-column-title {
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            margin-bottom: 0;
        }

        .kanban-item-count {
            background-color: var(--secondary-gray-light);
            color: var(--text-label);
            font-size: 0.8rem;
            font-weight: var(--font-weight-bold);
            padding: 0.2rem 0.6rem;
            border-radius: var(--radius-xxl);
        }

        .kanban-column-body {
            flex-grow: 1;
            padding: 1rem;
            overflow-y: auto;
            /* Allows vertical scrolling for cards within a column */
            display: flex;
            flex-direction: column;
            gap: 1rem;
            /* Creates space between asset cards */
        }

        /* Re-purpose your existing asset card for the Kanban view */
        .kanban-column-body .asset-card {
            width: 100%;
            margin-bottom: 0;
            /* Use gap from parent instead of margin */
            cursor: grab;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        .add-new-option-btn {
            background: none;
            border: none;
            color: var(--primary-teal);
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0;
        }

        /* New styles for the add asset modal header */
        .add-asset-modal-header {
            background-color: var(--tertiary-navy);
            color: var(--primary-white);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* New Styles for the four-section layout */
        .add-asset-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .grid-section {
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }

        .grid-section-header {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--tertiary-navy);
            margin-bottom: 1rem;
        }

        

        /* New style for the dynamic add option modal */
        #add-option-modal {
            position: fixed;
            top: 0;
            right: -100%;
            width: 400px;
            max-width: 90vw;
            height: 100vh;
            background-color: white;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
            transition: right 0.3s ease-in-out;
            z-index: 2100; /* Higher z-index to overlay addAssetModal */
        }

        #add-option-modal.show {
            right: 0;
        }

        .add-option-modal-header {
            background-color: var(--tertiary-navy);
            color: var(--primary-white);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-option-modal-body {
            padding: 1.5rem;
        }

        .add-option-modal-footer {
            padding: 1rem;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>
</head>
<body>

    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">Asset Master</a>
        </div>
    </nav>

    <div class="main-content container-fluid">
        <div id="metrics-panel" class="collapsed">
            <div class="row g-3 mb-4">
                <div class="col-6 col-md-3 col-lg-3 col-xl-3">
                    <div class="metric-card">
                        <i class="bi bi-box-seam metric-card-icon total-assets-icon"></i>
                        <h5>Total Assets</h5>
                        <h3 id="total-assets-count">0</h3>
                    </div>
                </div>
                <div class="col-6 col-md-3 col-lg-3 col-xl-3">
                    <div class="metric-card">
                        <i class="bi bi-check-circle-fill metric-card-icon active-assets-icon"></i>
                        <h5>Active Assets</h5>
                        <h3 id="active-assets-count">0</h3>
                    </div>
                </div>
                <div class="col-6 col-md-3 col-lg-3 col-xl-3">
                    <div class="metric-card">
                        <i class="bi bi-tools metric-card-icon maintenance-icon"></i>
                        <h5>In Maintenance</h5>
                        <h3 id="maintenance-assets-count">0</h3>
                    </div>
                </div>
                <div class="col-6 col-md-3 col-lg-3 col-xl-3">
                    <div class="metric-card">
                        <i class="bi bi-geo-alt-fill metric-card-icon total-assets-icon"></i>
                        <h5>GPS Tracking</h5>
                        <h3 id="gps-tracking-count">0</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="filter-bar mb-4">
            <div class="row g-3 align-items-center">
                <div class="col-12 col-md-6 col-lg-5 col-xl-5">
                    <div class="input-group">
                        <span class="input-group-text" id="search-addon" style="background-color: var(--background-secondary); border-right: none;"><i class="bi bi-search" title="Search assets"></i></span>
                        <input type="text" class="form-control" placeholder="Search by name, ID, or category..." aria-label="Search" aria-describedby="search-addon" style="border-left: none;" id="dynamic-search-input">
                        <span class="input-group-text" id="mic-addon" style="background-color: var(--background-secondary); border-left: none; cursor: pointer;"><i class="bi bi-mic" title="Search by voice"></i></span>

                        <button class="btn btn-outline-secondary" type="button" id="advanced-search-btn"
                                data-bs-toggle="offcanvas" data-bs-target="#advancedSearchModal" aria-controls="advancedSearchModal"
                                title="Advanced Search">
                            <i class="bi bi-sliders"></i>
                        </button>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-7 col-xl-7">
                    <div class="row justify-content-end g-2">
                        <div class="col-auto d-none" id="add-category-btn-container">
                            <button class="btn btn-sm btn-primary" id="add-new-category-btn" title="Add new category">
                                <i class="bi bi-plus-circle"></i> Add New Category
                            </button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-sm btnFilter" id="add-asset-btn" data-bs-toggle="modal" data-bs-target="#addAssetModal">
                                <i class="bi bi-plus-circle me-2"></i>Add Asset
                            </button>
                        </div>

                        <div class="col-auto">
                            <button class="btn btn-sm btnFilter" id="dashboard-toggle-btn" title="Dashboard">
                                <i class="bi bi-speedometer"></i> Dashboard
                            </button>
                        </div>

                        <div class="col-auto">
                            <div class="btn-group view-toggle-buttons" role="group">
                                <button type="button" class="btn btn-sm btn-outline-secondary view-toggle-btn active" id="card-view-btn" data-view="card" title="Card view">
                                    <i class="bi bi-grid-3x3-gap-fill"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary view-toggle-btn" id="table-view-btn" data-view="grid" title="Table view">
                                    <i class="bi bi-table"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary view-toggle-btn" id="list-view-btn" data-view="list" title="List view">
                                    <i class="bi bi-list-ul"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary view-toggle-btn" id="kanban-view-btn" data-view="kanban" title="Kanban view">
                                    <i class="bi bi-kanban"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="main-view-container">
            <div id="dashboard-view" class="row g-4" style="display: none;">
                <div class="col-12">
                    <div class="dashboard-card">
                        <h4 class="fw-bold">Asset Status Overview</h4>
                        <canvas id="asset-status-chart"></canvas>
                    </div>
                </div>
                <div class="col-12">
                    <div class="dashboard-card">
                        <h4 class="fw-bold">Assets by Category</h4>
                        <canvas id="asset-category-chart"></canvas>
                    </div>
                </div>
            </div>
            <div class="row g-4" id="asset-view-container">
            </div>

            <div id="kanban-view-container" class="kanban-board" style="display: none;">
            </div>

            <div class="row g-4" id="asset-view-container">
            </div>
        </div>
    </div>

    <footer class="bg-dark text-white text-center py-4 mt-4">
        <div class="container">
            <p class="mb-0">&copy; 2025 Asset Master. All Rights Reserved.</p>
        </div>
    </footer>

    <div class="message-box" id="messageBox">
        <span class="message-close-btn" id="messageClose">&times;</span>
        <p>The 'Add New Asset' action was triggered!</p>
    </div>

    <div id="asset-detail-modal" class="modal-container">
        <div class="modal-header-custom container-fluid">
            <div class="row w-100 align-items-center">
                <div class="col-xs-8 col-sm-8 col-md-9 col-lg-10 col-xl-10 col-xxl-10">
                    <h5 class="modal-title">Asset Details</h5>
                </div>

                <div class="col-xs-4 col-sm-4 col-md-3 col-lg-2 col-xl-2 col-xxl-2 text-end d-flex justify-content-end">
                    <button id="fullscreen-toggle-btn" class="btn btn-sm btn-outline-light d-flex align-items-center me-2">
                        <i class="bi bi-arrows-fullscreen me-2" id="fullscreen-icon"></i> <span id="fullscreen-text">Fullscreen</span>
                    </button>
                    <button id="modal-close-btn" class="modal-close-btn" aria-label="Close">&times;</button>
                </div>
            </div>
        </div>

        <div id="modal-body-content" class="modal-body-custom">
        </div>

        <div class="modal-edit-footer-custom px-4 d-flex justify-content-end">
            <div class="row gx-2">
                <div class="col-auto">
                    <button class="btn btn-outline-secondary" id="edit-asset-detail-btn" title="Edit Asset">Edit Asset</button>
                </div>
                <div class="col-auto">
                    <button class="btn btn-danger" id="delete-asset-btn" title="Delete">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <div id="asset-edit-modal" class="modal-edit-container">
        <div class="modal-edit-content container-fluid g-0">
            <div class="row modal-edit-header-custom g-0">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 px-4">
                    <div class="row align-items-center mb-3">
                        <div class="col-xs-auto col-sm-auto col-md-auto col-lg-auto col-xl-auto edit-avatar me-3">
                            <img id="edit-avatar-img" src="" alt="Asset Image">
                        </div>
                        <div class="col-xs col-sm col-md col-lg col-xl">
                            <h4 id="edit-asset-name" class="mb-0 fw-bold"></h4>
                            <p id="edit-asset-subtitle" class="mb-0 fw-light"></p>
                        </div>
                        <div class="col-xs-auto col-sm-auto col-md-auto col-lg-auto col-xl-auto">
                            <button id="edit-modal-close-btn" class="btn-close btn-close-white" aria-label="Close"></button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                            <p class="mb-0 fw-bold">Asset ID:</p>
                            <p id="edit-asset-id" class="mb-0"></p>
                        </div>
                        <div class="col-xs-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                            <p class="mb-0 fw-bold">Category:</p>
                            <p id="edit-asset-category" class="mb-0"></p>
                        </div>
                        <div class="col-xs-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                            <p class="mb-0 fw-bold">Brand:</p>
                            <p id="edit-asset-brand" class="mb-0"></p>
                        </div>
                        <div class="col-xs-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                            <p class="mb-0 fw-bold">Model:</p>
                            <p id="edit-asset-model" class="mb-0"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row modal-edit-body-custom g-0">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 px-4">
                    <ul class="nav nav-tabs" id="asset-edit-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="edit-info-tab" data-bs-toggle="tab" data-bs-target="#edit-info" type="button" role="tab" aria-controls="edit-info" aria-selected="true">Basic Info</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="edit-location-tab" data-bs-toggle="tab" data-bs-target="#edit-location" type="button" role="tab" aria-controls="edit-location" aria-selected="false">Location</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="edit-maintenance-tab" data-bs-toggle="tab" data-bs-target="#edit-maintenance" type="button" role="tab" aria-controls="edit-maintenance" aria-selected="false">Maintenance</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-4" id="asset-edit-tabs-content">
                        <div class="tab-pane fade show active" id="edit-info" role="tabpanel" aria-labelledby="edit-info-tab">
                            <form>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <label for="edit-asset-description" class="form-label">Part Description</label>
                                        <input type="text" class="form-control" id="edit-asset-description">
                                    </div>
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <label for="edit-assigned-to" class="form-label">Assigned To</label>
                                        <input type="text" class="form-control" id="edit-assigned-to">
                                    </div>
                                </div>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                                        <label for="edit-purpose" class="form-label">Purpose</label>
                                        <textarea class="form-control" id="edit-purpose" rows="3"></textarea>
                                    </div>
                                </div>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                                        <label for="edit-remarks" class="form-label">Remarks</label>
                                        <textarea class="form-control" id="edit-remarks" rows="3"></textarea>
                                    </div>
                                </div>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                                        <label for="edit-image-url" class="form-label">Image URL</label>
                                        <input type="text" class="form-control" id="edit-image-url">
                                    </div>
                                </div>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="edit-has-serial-number">
                                            <label class="form-check-label" for="edit-has-serial-number">Has Serial Number</label>
                                        </div>
                                    </div>
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <label for="edit-quantity" class="form-label">Quantity</label>
                                        <input type="number" class="form-control" id="edit-quantity">
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="edit-location" role="tabpanel" aria-labelledby="edit-location-tab">
                            <form>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <label for="edit-assigned-location" class="form-label">Assigned Location</label>
                                        <input type="text" class="form-control" id="edit-assigned-location">
                                    </div>
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <label for="edit-last-known-location" class="form-label">Last Known Location</label>
                                        <input type="text" class="form-control" id="edit-last-known-location">
                                    </div>
                                </div>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="edit-gps-tracking">
                                            <label class="form-check-label" for="edit-gps-tracking">GPS Tracking Enabled</label>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="edit-maintenance" role="tabpanel" aria-labelledby="edit-maintenance-tab">
                            <form>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <label for="edit-warranty-tracking" class="form-label">Warranty Tracking</label>
                                        <input type="text" class="form-control" id="edit-warranty-tracking">
                                    </div>
                                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                                        <label for="edit-service-providers" class="form-label">Service Providers</label>
                                        <input type="text" class="form-control" id="edit-service-providers">
                                    </div>
                                </div>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                                        <label for="edit-scheduled-maintenance" class="form-label">Scheduled Maintenance</label>
                                        <textarea class="form-control" id="edit-scheduled-maintenance" rows="3" disabled></textarea>
                                    </div>
                                </div>
                                <div class="mb-3 row g-3">
                                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                                        <label for="edit-maintenance-history" class="form-label">Maintenance History</label>
                                        <textarea class="form-control" id="edit-maintenance-history" rows="3" disabled></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row modal-edit-footer-custom g-0">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 text-end px-4">
                    <button class="btn btn-outline-secondary" id="cancel-edit-btn">Cancel</button>
                    <button class="btn btn-success" id="save-edit-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="purchaseModal" tabindex="-1" aria-labelledby="purchaseModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="purchaseModalLabel">Purchase Asset</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="purchaseForm">
                        <div class="mb-3">
                            <label for="purchase-vendor" class="form-label">Vendor</label>
                            <input type="text" class="form-control" id="purchase-vendor" required>
                        </div>
                        <div class="mb-3">
                            <label for="purchase-date" class="form-label">Purchase Date</label>
                            <input type="date" class="form-control" id="purchase-date" required>
                        </div>
                        <div class="mb-3">
                            <label for="purchase-cost" class="form-label">Total Cost</label>
                            <input type="number" class="form-control" id="purchase-cost" required>
                        </div>
                        <div class="mb-3">
                            <label for="purchase-quantity" class="form-label">Quantity</label>
                            <input type="number" class="form-control" id="purchase-quantity" required>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="purchase-has-serial">
                                <label class="form-check-label" for="purchase-has-serial">
                                    Single item (serialized)
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="purchaseForm" class="btn btn-primary">Complete Purchase</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="issueOutModal" tabindex="-1" aria-labelledby="issueOutModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="issueOutModalLabel">Issue Out Asset</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="issueOutForm">
                        <div class="mb-3">
                            <label for="issue-recipient" class="form-label">Recipient</label>
                            <input type="text" class="form-control" id="issue-recipient" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Asset Status</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="issueStatus" id="returnable" value="returnable" checked>
                                <label class="form-check-label" for="returnable">Returnable (Loan)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="issueStatus" id="unreturnable" value="unreturnable">
                                <label class="form-check-label" for="unreturnable">Unreturnable (Sold)</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="issueOutForm" class="btn btn-primary">Issue Out</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="scrapModal" tabindex="-1" aria-labelledby="scrapModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scrapModalLabel">Scrap Asset</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="scrapForm">
                        <div class="mb-3">
                            <label for="scrap-reason" class="form-label">Reason for Scrap</label>
                            <textarea class="form-control" id="scrap-reason" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="scrapForm" class="btn btn-danger">Scrap Asset</button>
                </div>
            </div>
        </div>
    </div>

    <div class="offcanvas offcanvas-start" tabindex="-1" id="advancedSearchModal" aria-labelledby="advancedSearchModalLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="advancedSearchModalLabel"><i class="bi bi-sliders me-2"></i>Advanced Search</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <form id="advancedSearchForm">
                <div class="mb-3">
                    <label for="filter-status" class="form-label">Status</label>
                    <select class="form-select" id="filter-status">
                        <option selected>Any Status</option>
                        <option value="Active">Active</option>
                        <option value="Idle">Idle</option>
                        <option value="Under Maintenance">Under Maintenance</option>
                        <option value="Retired">Retired</option>
                    </select>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="filter-purchase-date-start" class="form-label">Purchase Date From</label>
                        <input type="date" class="form-control" id="filter-purchase-date-start">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="filter-purchase-date-end" class="form-label">To</label>
                        <input type="date" class="form-control" id="filter-purchase-date-end">
                    </div>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" value="" id="filter-has-gps">
                    <label class="form-check-label" for="filter-has-gps">
                        Only show assets with GPS Tracking
                    </label>
                </div>

                <div class="mt-4">
                    <button type="submit" form="advancedSearchForm" class="btn btn-primary w-100">Apply Filters</button>
                    <button type="button" class="btn btn-outline-secondary w-100 mt-2" data-bs-dismiss="offcanvas">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <div id="image-preview-modal" class="image-preview-modal" onclick="this.classList.remove('show')">
        <span class="image-preview-close" title="Close Modal">&times;</span>
        <div class="image-preview-wrapper">
            <img class="image-preview-content" id="full-image" alt="Full size preview">
        </div>
    </div>

    <!-- Add New Asset Modal -->
    <div class="modal fade" id="addAssetModal" tabindex="-1" aria-labelledby="addAssetModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" style="max-width: 90vw;">
            <div class="modal-content">
                <div class="add-asset-modal-header">
                    <h5 class="modal-title" id="addAssetModalLabel">Add New Tool</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addAssetForm">
                    <div class="modal-body overflow-auto">
                        <div class="add-asset-grid">
                            <!-- Section 1 -->
                            <div class="grid-section">
                                <div class="mb-3">
                                    <label for="add-asset-part-id" class="form-label">Part #</label>
                                    <input type="text" class="form-control" id="add-asset-part-id" placeholder="Part #" required>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <label for="add-asset-category" class="form-label">Tools Category</label>
                                        <button type="button" class="add-new-option-btn" data-target-select="add-asset-category" title="Add new category"><i class="bi bi-plus-circle"></i></button>
                                    </div>
                                    <select id="add-asset-category" class="form-select" required>
                                        <option selected disabled value="">-Select-</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <label for="add-asset-uom" class="form-label">UOM</label>
                                        <button type="button" class="add-new-option-btn" data-target-select="add-asset-uom" title="Add new unit of measure"><i class="bi bi-plus-circle"></i></button>
                                    </div>
                                    <select id="add-asset-uom" class="form-select">
                                        <option selected disabled value="">-Select-</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="add-asset-dimension" class="form-label">Dimension</label>
                                    <input type="text" class="form-control" id="add-asset-dimension" placeholder="Dimension">
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <label for="add-asset-productType" class="form-label">Product Type</label>
                                        <button type="button" class="add-new-option-btn" data-target-select="add-asset-productType" title="Add new product type"><i class="bi bi-plus-circle"></i></button>
                                    </div>
                                    <select id="add-asset-productType" class="form-select">
                                        <option selected disabled value="">-Select-</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="add-asset-dep-rate" class="form-label">Annual Depreciation Rate (%)</label>
                                    <input type="number" class="form-control" id="add-asset-dep-rate" step="0.1" placeholder="% factor per year">
                                </div>
                            </div>
                            <!-- Section 2 -->
                            <div class="grid-section">
                                <div class="mb-3">
                                    <label for="add-asset-description" class="form-label">Part Description</label>
                                    <input type="text" class="form-control" id="add-asset-description" placeholder="Part Description" required>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <label for="add-asset-functional-group" class="form-label">Functional Group</label>
                                        <button type="button" class="add-new-option-btn" data-target-select="add-asset-functional-group" title="Add new functional group"><i class="bi bi-plus-circle"></i></button>
                                    </div>
                                    <select id="add-asset-functional-group" class="form-select">
                                        <option selected disabled value="">-Select-</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="add-asset-weight" class="form-label">Weight (KG)</label>
                                    <input type="number" class="form-control" id="add-asset-weight" step="0.1" placeholder="Weight in KG">
                                </div>
                                <div class="mb-3">
                                    <label for="add-asset-brand" class="form-label">Brand</label>
                                    <input type="text" class="form-control" id="add-asset-brand" placeholder="Brand">
                                </div>
                                <div class="mb-3">
                                    <label for="add-asset-model" class="form-label">Model</label>
                                    <input type="text" class="form-control" id="add-asset-model" placeholder="Model">
                                </div>
                                <div class="mb-3">
                                    <label for="add-asset-dep-years" class="form-label">Years to Depreciate</label>
                                    <input type="number" class="form-control" id="add-asset-dep-years" step="1" placeholder="# of years to Depreciate">
                                </div>
                            </div>
                            <!-- Section 3 -->
                            <div class="grid-section">
                                <div class="mb-3 form-check">
                                    <input class="form-check-input" type="checkbox" id="add-asset-maintenance">
                                    <label class="form-check-label" for="add-asset-maintenance">With Maintenance?</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input class="form-check-input" type="checkbox" id="add-asset-asset-tag" checked>
                                    <label class="form-check-label" for="add-asset-asset-tag">With Asset Tag?</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input class="form-check-input" type="checkbox" id="add-asset-serial">
                                    <label class="form-check-label" for="add-asset-serial">With Serial #?</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input class="form-check-input" type="checkbox" id="add-asset-purchase-lock">
                                    <label class="form-check-label" for="add-asset-purchase-lock">Lock for Purchase?</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input class="form-check-input" type="checkbox" id="add-asset-issue-lock">
                                    <label class="form-check-label" for="add-asset-issue-lock">Lock for Issue?</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input class="form-check-input" type="checkbox" id="add-asset-is-active" checked>
                                    <label class="form-check-label" for="add-asset-is-active">Is Active?</label>
                                </div>
                            </div>
                            <!-- Section 4 -->
                            <div class="grid-section">
                                <div class="mb-3">
                                    <label for="add-asset-purpose" class="form-label">Purpose</label>
                                    <textarea class="form-control" id="add-asset-purpose" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="add-asset-remarks" class="form-label">Remarks</label>
                                    <textarea class="form-control" id="add-asset-remarks" rows="3"></textarea>
                                </div>
                                <div class="text-center mt-4">
                                    <label for="add-asset-image" class="btn btn-outline-secondary" id="upload-image-btn">Upload Tool Image</label>
                                    <input type="file" id="add-asset-image" accept="image/*" class="d-none">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" form="addAssetForm" class="btn btn-primary">Save Asset</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Dynamic Add Option Modal -->
    <div id="add-option-modal" class="modal-fade">
        <div class="add-option-modal-header">
            <h5 class="modal-title" id="add-option-modal-title">Add New Option</h5>
            <button type="button" class="btn-close btn-close-white" id="add-option-close-btn"></button>
        </div>
        <div class="add-option-modal-body">
            <form id="addOptionForm">
                <div class="mb-3">
                    <label for="add-option-code" class="form-label">Code:</label>
                    <input type="text" class="form-control" id="add-option-code" required>
                </div>
                <div class="mb-3">
                    <label for="add-option-description" class="form-label">Description:</label>
                    <input type="text" class="form-control" id="add-option-description" required>
                </div>
            </form>
        </div>
        <div class="add-option-modal-footer">
            <button type="button" class="btn btn-primary" id="add-option-save-btn">Save</button>
        </div>
    </div>

    <div id="modal-overlay" class="modal-overlay"></div>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Use a self-executing anonymous function to clear scope
        (() => {
            let myChart = null;
            let myPieChart = null;
            let modalCurrentAssetId = null; // Variable to store the current asset ID for modal rendering
            let modalBody = null; // Declare modalBody in a broader scope
            let kanbanContainer = null;
            let assetsData = [];
            let depreciationRates = {};

            // Use fetch to load the JSON data
            const loadData = async () => {
                try {
                    const response = await fetch('../../Assets/Assetdata/assetsData.json');
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    const data = await response.json();
                    assetsData = data.assetsData;
                    depreciationRates = data.depreciationRates;
                    // Now that data is loaded, you can proceed with initial rendering
                    const initialBtn = document.getElementById('card-view-btn');
                    if (initialBtn) {
                        setActiveButton(initialBtn);
                        renderView(initialBtn.dataset.view);
                        updateMetricCards();
                    }
                    populateDropdowns();
                } catch (error) {
                    console.error('Failed to load asset data:', error);
                    // Handle the error gracefully, e.g., show a message to the user
                    document.getElementById('asset-view-container').innerHTML = `<p class="text-center mt-5 w-100 text-danger">Failed to load asset data. Please check the data file.</p>`;
                }
            };

            const populateDropdowns = () => {
                const categories = [...new Set(assetsData.map(asset => asset.assetCategory))].sort();
                const functionalGroups = [...new Set(assetsData.map(asset => asset.functionalGroup))].filter(Boolean).sort();
                const uoms = [...new Set(assetsData.map(asset => asset.uom))].filter(Boolean).sort();
                const productTypes = [...new Set(assetsData.map(asset => asset.productType))].filter(Boolean).sort();

                populateSelect('add-asset-category', categories);
                populateSelect('add-asset-functional-group', functionalGroups);
                populateSelect('add-asset-uom', uoms);
                populateSelect('add-asset-productType', productTypes);
            }

            const populateSelect = (elementId, options) => {
                const selectElement = document.getElementById(elementId);
                if (selectElement) {
                    const defaultOption = selectElement.querySelector('option[disabled]');
                    selectElement.innerHTML = '';
                    if (defaultOption) {
                        selectElement.appendChild(defaultOption);
                    }
                    options.forEach(option => {
                        const newOption = document.createElement('option');
                        newOption.value = option;
                        newOption.textContent = option;
                        selectElement.appendChild(newOption);
                    });
                }
            }

            const calculateDepreciation = (asset) => {
                const purchaseDate = new Date(asset.purchaseDate);
                const today = new Date();
                const diffYears = (today - purchaseDate) / (1000 * 60 * 60 * 24 * 365.25);
                const rate = asset.depreciationRate !== undefined
                    ? asset.depreciationRate
                    : (depreciationRates[asset.assetCategory] || 0);

                const depreciationAmount = asset.originalCost * rate * diffYears;
                const currentValue = Math.max(0, asset.originalCost - depreciationAmount);

                let depreciationSchedule = [];
                for (let i = 0; i <= 5; i++) {
                    const year = today.getFullYear() + i;
                    const value = Math.max(0, asset.originalCost - (asset.originalCost * rate * (diffYears + i)));
                    depreciationSchedule.push({
                        year: year,
                        value: value.toFixed(2)
                    });
                }

                return {
                    currentValue: currentValue.toFixed(2),
                    schedule: depreciationSchedule
                };
            };

            const getAvatarColor = (category) => {
                switch (category) {
                    case 'Vehicle':
                        return 'var(--primary-blue)';
                    case 'Equipment':
                        return 'var(--primary-teal-dark)';
                    case 'Parts':
                        return 'var(--primary-teal)';
                    case 'Electronics':
                        return '#4338ca';
                    default:
                        return 'var(--secondary-color)';
                }
            };

            const getStatusBadge = (status) => {
                let badgeClass = '';
                switch (status) {
                    case 'Active':
                    case 'In Stock':
                        badgeClass = 'bg-success';
                        break;
                    case 'Under Maintenance':
                        badgeClass = 'bg-warning text-dark';
                        break;
                    case 'Idle':
                        badgeClass = 'bg-info';
                        break;
                    case 'Retired':
                        badgeClass = 'bg-danger';
                        break;
                    default:
                        badgeClass = 'bg-secondary';
                }
                return `<span class="badge top-0 end-0 mt-2 me-2 text-wrap ${badgeClass}">${status}</span>`;
            };

            const getFeatureDetails = (asset) => {
                let html = '';
                const features = [];
                if (asset.withMaintenance) features.push({
                    icon: 'bi bi-wrench',
                    text: 'With Maintenance'
                });
                if (asset.withAssetTag) features.push({
                    icon: 'bi bi-tag',
                    text: 'With Asset Tag'
                });
                if (asset.withSerial) features.push({
                    icon: 'bi bi-upc-scan',
                    text: 'With Serial #'
                });
                if (asset.isLockedForIssues) {
                    features.push({
                        icon: 'bi bi-lock-fill text-danger',
                        text: 'Locked for Issues'
                    });
                } else {
                    features.push({
                        icon: 'bi bi-unlock-fill text-success',
                        text: 'Not Locked for Issues'
                    });
                }
                if (asset.isLockedForPurchase) {
                    features.push({
                        icon: 'bi bi-cart-fill text-danger',
                        text: 'Locked for Purchase'
                    });
                } else {
                    features.push({
                        icon: 'bi bi-cart-fill text-success',
                        text: 'Not Locked for Purchase'
                    });
                }
                if (asset.hasGpsTracking) {
                    features.push({
                        icon: 'bi bi-geo-alt-fill text-success',
                        text: 'GPS Tracking'
                    });
                } else {
                    features.push({
                        icon: 'bi bi-geo-alt-fill text-muted',
                        text: 'No GPS Tracking'
                    });
                }
                html += '<div class="row g-2">';
                features.forEach(feature => {
                    html += `
                <div class="col-6 d-flex align-items-center">
                    <i class="${feature.icon} me-2"></i>
                    <span>${feature.text}</span>
                </div>
            `;
                });
                html += '</div>';
                return html;
            };

            const generateBomTreeHtml = (items, parentId = '') => {
                if (!items || items.length === 0) {
                    return `<p class="text-muted">No Bill of Materials information available.</p>`;
                }
                let html = '<ul>';
                items.forEach((item, index) => {
                    const hasSubComponents = item.subComponents && item.subComponents.length > 0;
                    const itemId = parentId ? `${parentId}-${index}` : `${index}`;
                    const targetId = `bom-collapse-${itemId}`;

                    html += `<li>`;
                    if (hasSubComponents) {
                        html += `<button class="bom-toggle-btn" type="button" data-target="#${targetId}" aria-expanded="false" aria-controls="${targetId}">
                                                     <i class="bi bi-chevron-right me-2"></i><span class="bom-item-icon"><i class="bi bi-folder-fill"></i></span> <span class="bom-tree-text">${item.partName}</span>
                                                   </button>`;
                        html += `<div class="collapse-smooth collapsed" id="${targetId}">
                                                     ${generateBomTreeHtml(item.subComponents, itemId)}
                                                   </div>`;
                    } else {
                        html += `<span><i class="bi bi-tools bom-item-icon"></i> <span class="bom-tree-text">${item.partName} (Qty: ${item.quantity})</span></span>`;
                    }
                    html += '</li>';
                });
                html += '</ul>';
                return html;
            };

            const generateFinancialHtml = (asset) => {
                const depreciation = calculateDepreciation(asset);
                const tableRows = depreciation.schedule.map(s => `
                                    <tr>
                                        <td>${s.year}</td>
                                        <td>$${s.value}</td>
                                    </tr>
                            `).join('');

                return `
                                    <h6 class="text-teal fw-bold mb-3"><i class="bi bi-currency-dollar me-2"></i>Financial & Value</h6>
                                    <p><strong>Original Cost:</strong> $${asset.originalCost.toLocaleString()}</p>
                                    <p><strong>Current Book Value:</strong> $${depreciation.currentValue}</p>
                                    <hr>
                                    <h6>Depreciation Schedule (Straight-Line)</h6>
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Year</th>
                                                <th>Projected Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${tableRows}
                                        </tbody>
                                    </table>
                            `;
            };

            const generateTabPaneContentHtml = (asset) => {
                const basicInfoHtml = `
                                                 <h6 class="text-teal fw-bold mb-3"><i class="bi bi-info-circle me-2"></i>Basic Information</h6>
                                                 <div class="row g-2">
                                                     <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 col-xl-6 col-xxl-6"><p class="mb-1"><span class="fw-bold">Category:</span> ${asset.assetCategory}</p></div>
                                                     <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 col-xl-6 col-xxl-6"><p class="mb-1"><span class="fw-bold">Product Type:</span> ${asset.productType}</p></div>
                                                     <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 col-xl-6 col-xxl-6"><p class="mb-1"><span class="fw-bold">Brand:</span> ${asset.brand}</p></div>
                                                     <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 col-xl-6 col-xxl-6"><p class="mb-1"><span class="fw-bold">Model:</span> ${asset.model}</p></div>
                                                     <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 col-xl-6 col-xxl-6"><p class="mb-1"><span class="fw-bold">Assigned To:</span> ${asset.assignedTo}</p></div>
                                                 </div>
                                                `;

                const featuresHtml = `
                                                 <h6 class="text-teal fw-bold mb-3"><i class="bi bi-card-checklist me-2"></i>Features</h6>
                                                 ${getFeatureDetails(asset)}
                                                `;

                const locationHtml = `
                                                 <h6 class="text-teal fw-bold mb-3"><i class="bi bi-geo-alt me-2"></i>Location & Tracking</h6>
                                                 <div class="row g-2">
                                                     <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12"><p class="mb-1"><span class="fw-bold">Assigned Location:</span> ${asset.assignedLocation}</p></div>
                                                     <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12"><p class="mb-1"><span class="fw-bold">Last Known Location:</span> ${asset.lastKnownLocation}</p></div>
                                                     <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12"><p class="mb-1"><span class="fw-bold">GPS Tracking:</span> ${asset.hasGpsTracking ? 'Enabled' : 'Disabled'}</p></div>
                                                 </div>
                                                `;

                const maintenanceHtml = `
                                                 <h6 class="text-teal fw-bold mb-3"><i class="bi bi-tools me-2"></i>Maintenance & Service</h6>
                                                 <p class="fw-bold mb-1">Scheduled:</p>
                                                 <ul>
                                                     ${asset.maintenance?.scheduled.length > 0 ? asset.maintenance.scheduled.map(s => `<li>${s.task} on ${s.date} (${s.status})</li>`).join('') : '<li>No scheduled tasks.</li>'}
                                                 </ul>
                                                 <p class="fw-bold mb-1">History:</p>
                                                 <ul>
                                                     ${asset.maintenance?.history.length > 0 ? asset.maintenance.history.map(h => `<li>${h.date}: ${h.service} - $${h.cost}</li>`).join('') : '<li>No maintenance history.</li>'}
                                                 </ul>
                                                 <p class="fw-bold mb-1">Service Providers:</p>
                                                 <p>${asset.maintenance?.providers.join(', ') || 'N/A'}</p>
                                                 <p class="fw-bold mb-1">Warranty Tracking:</p>
                                                 <p>${asset.maintenance?.warrantyTracking || 'N/A'}</p>
                                                 <hr class="my-3">
                                                 <p class="mb-1"><span class="fw-bold">Purpose:</span> ${asset.purpose}</p>
                                                 <p class="mb-0"><span class="fw-bold">Remarks:</span> ${asset.remarks}</p>
                                                `;

                const bomHtml = `
                                                 <h6 class="text-teal fw-bold mb-3"><i class="bi bi-list-nested me-2"></i>Bill of Materials</h6>
                                                 <div class="bom-tree">
                                                     ${generateBomTreeHtml(asset.bom)}
                                                 </div>
                                                `;

                const financialsHtml = generateFinancialHtml(asset);

                return {
                    basicInfo: basicInfoHtml,
                    location: locationHtml,
                    features: featuresHtml,
                    maintenance: maintenanceHtml,
                    bom: bomHtml,
                    financials: financialsHtml
                };
            };

            const generateFullModalBodyHtml = (asset) => {
                const imageUrl = asset.imageURL || 'https://placehold.co/400x300/E6FAFD/000032?text=No+Image';
                const tabContent = generateTabPaneContentHtml(asset);

                return `
                                                  <div class="row g-0 modal-body-fixed-content">
                                                      <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12">
                                                          <img src="${imageUrl}" class="modal-preview-img" alt="${asset.partDescription}" onerror="this.onerror=null;this.src='https://placehold.co/400x300/E6FAFD/000032?text=No+Image';">
                                                          <h5 class="fw-bold">${asset.partDescription}</h5>
                                                          <p class="text-muted mb-4">Asset ID: ${asset.assetID}</p>
                                                          <ul class="nav nav-tabs" id="asset-tabs" role="tablist">
                                                              <li class="nav-item" role="presentation">
                                                                  <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab" aria-controls="basic-info" aria-selected="true">Basic Info</button>
                                                              </li>
                                                              <li class="nav-item" role="presentation">
                                                                  <button class="nav-link" id="location-tab" data-bs-toggle="tab" data-bs-target="#location-info" type="button" role="tab" aria-controls="location-info" aria-selected="false">Location</button>
                                                              </li>
                                                              <li class="nav-item" role="presentation">
                                                                  <button class="nav-link" id="features-tab" data-bs-toggle="tab" data-bs-target="#features" type="button" role="tab" aria-controls="features" aria-selected="false">Features</button>
                                                              </li>
                                                              <li class="nav-item" role="presentation">
                                                                  <button class="nav-link" id="maintenance-tab" data-bs-toggle="tab" data-bs-target="#maintenance" type="button" role="tab" aria-controls="maintenance" aria-selected="false">Others</button>
                                                              </li>
                                                              <li class="nav-item" role="presentation">
                                                                  <button class="nav-link" id="bom-tab" data-bs-toggle="tab" data-bs-target="#bom" type="button" role="tab" aria-controls="bom" aria-selected="false">BOM</button>
                                                              </li>
                                                              <li class="nav-item" role="presentation">
                                                                  <button class="nav-link" id="financials-tab" data-bs-toggle="tab" data-bs-target="#financials-info" type="button" role="tab" aria-controls="financials-info" aria-selected="false">Financials</button>
                                                              </li>
                                                          </ul>
                                                      </div>
                                                  </div>
                                                  <div class="row g-0 modal-body-scrollable-content">
                                                      <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12">
                                                          <div class="tab-content mt-3 w-90" id="asset-tabs-content">
                                                              <div class="tab-pane fade show active" id="basic-info" role="tabpanel" aria-labelledby="basic-info-tab">
                                                                  ${tabContent.basicInfo}
                                                              </div>
                                                              <div class="tab-pane fade" id="location-info" role="tabpanel" aria-labelledby="location-tab">
                                                                  ${tabContent.location}
                                                              </div>
                                                              <div class="tab-pane fade" id="features" role="tabpanel" aria-labelledby="features-tab">
                                                                  ${tabContent.features}
                                                              </div>
                                                              <div class="tab-pane fade" id="maintenance" role="tabpanel" aria-labelledby="maintenance-tab">
                                                                  ${tabContent.maintenance}
                                                              </div>
                                                              <div class="tab-pane fade" id="bom" role="tabpanel" aria-labelledby="bom-tab">
                                                                  ${tabContent.bom}
                                                              </div>
                                                              <div class="tab-pane fade" id="financials-info" role="tabpanel" aria-labelledby="financials-tab">
                                                                  ${tabContent.financials}
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                                              `;
            };

            const generateFullModalBodyHtmlFullscreen = (asset) => {
                const imageUrl = asset.imageURL || 'https://placehold.co/800x600/E6FAFD/000032?text=No+Image';
                const tabContent = generateTabPaneContentHtml(asset);

                return `
                                                  <div class="row g-0 h-100">
                                                      <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 d-flex justify-content-center align-items-center p-4">
                                                          <img src="${imageUrl}" class="modal-preview-img" alt="${asset.partDescription}" onerror="this.onerror=null;this.src='https://placehold.co/800x600/E6FAFD/000032?text=No+Image';">
                                                      </div>
                                                      <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-6 d-flex flex-column p-4">
                                                          <div class="d-flex flex-column h-100">
                                                              <div class="flex-shrink-0">
                                                                  <h5 class="fw-bold">${asset.partDescription}</h5>
                                                                  <p class="text-muted mb-4">Asset ID: ${asset.assetID}</p>
                                                                  <ul class="nav nav-tabs" id="asset-tabs" role="tablist">
                                                                      <li class="nav-item" role="presentation">
                                                                          <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab" aria-controls="basic-info" aria-selected="true">Basic Info</button>
                                                                      </li>
                                                                      <li class="nav-item" role="presentation">
                                                                          <button class="nav-link" id="location-tab" data-bs-toggle="tab" data-bs-target="#location-info" type="button" role="tab" aria-controls="location-info" aria-selected="false">Location</button>
                                                                      </li>
                                                                      <li class="nav-item" role="presentation">
                                                                          <button class="nav-link" id="features-tab" data-bs-toggle="tab" data-bs-target="#features" type="button" role="tab" aria-controls="features" aria-selected="false">Features</button>
                                                                      </li>
                                                                      <li class="nav-item" role="presentation">
                                                                          <button class="nav-link" id="maintenance-tab" data-bs-toggle="tab" data-bs-target="#maintenance" type="button" role="tab" aria-controls="maintenance" aria-selected="false">Others</button>
                                                                      </li>
                                                                      <li class="nav-item" role="presentation">
                                                                          <button class="nav-link" id="bom-tab" data-bs-toggle="tab" data-bs-target="#bom" type="button" role="tab" aria-controls="bom" aria-selected="false">BOM</button>
                                                                      </li>
                                                                      <li class="nav-item" role="presentation">
                                                                          <button class="nav-link" id="financials-tab" data-bs-toggle="tab" data-bs-target="#financials-info" type="button" role="tab" aria-controls="financials-info" aria-selected="false">Financials</button>
                                                                      </li>
                                                                  </ul>
                                                              </div>
                                                              <div class="flex-grow-1 overflow-y-auto pt-3 overflow-x-hidden">
                                                                  <div class="tab-content" id="asset-tabs-content">
                                                                      <div class="tab-pane fade show active" id="basic-info" role="tabpanel" aria-labelledby="basic-info-tab">
                                                                          ${tabContent.basicInfo}
                                                                      </div>
                                                                      <div class="tab-pane fade" id="location-info" role="tabpanel" aria-labelledby="location-tab">
                                                                          ${tabContent.location}
                                                                      </div>
                                                                      <div class="tab-pane fade" id="features" role="tabpanel" aria-labelledby="features-tab">
                                                                          ${tabContent.features}
                                                                      </div>
                                                                      <div class="tab-pane fade" id="maintenance" role="tabpanel" aria-labelledby="maintenance-tab">
                                                                          ${tabContent.maintenance}
                                                                      </div>
                                                                      <div class="tab-pane fade" id="bom" role="tabpanel" aria-labelledby="bom-tab">
                                                                          ${tabContent.bom}
                                                                      </div>
                                                                      <div class="tab-pane fade" id="financials-info" role="tabpanel" aria-labelledby="financials-tab">
                                                                          ${tabContent.financials}
                                                                      </div>
                                                                  </div>
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                                              `;
            };

            const getActionButtons = (asset) => {
                let buttonsHtml = '';
                switch (asset.status) {
                    case 'Active':
                        buttonsHtml += `<button class="btn btn-sm btn-outline-danger me-2 scrap-asset-btn" data-bs-toggle="modal" data-bs-target="#scrapModal" data-asset-id="${assetsData.indexOf(asset)}"><i class="bi bi-trash"></i> Scrap</button>`;
                        buttonsHtml += `<button class="btn btn-sm btn-primary issue-out-asset-btn" data-bs-toggle="modal" data-bs-target="#issueOutModal" data-asset-id="${assetsData.indexOf(asset)}"><i class="bi bi-box-arrow-in-up"></i> Issue Out</button>`;
                        break;
                    case 'Idle':
                        buttonsHtml += `<button class="btn btn-sm btn-success me-2 purchase-asset-btn" data-bs-toggle="modal" data-bs-target="#purchaseModal" data-asset-id="${assetsData.indexOf(asset)}"><i class="bi bi-truck-flatbed"></i> Purchase</button>`;
                        buttonsHtml += `<button class="btn btn-sm btn-danger scrap-asset-btn" data-bs-toggle="modal" data-bs-target="#scrapModal" data-asset-id="${assetsData.indexOf(asset)}"><i class="bi bi-box-seam"></i> Scrap</button>`;
                        break;
                    case 'Under Maintenance':
                        buttonsHtml += `<button class="btn btn-sm btn-outline-secondary" disabled><i class="bi bi-hourglass-split"></i> In Progress</button>`;
                        break;
                    default:
                        buttonsHtml += `<button class="btn btn-sm btn-outline-secondary" disabled><i class="bi bi-info-circle"></i> View</button>`;
                        break;
                }
                return buttonsHtml;
            };

            const generateCardHtml = (asset, index) => {
                const statusBadge = getStatusBadge(asset.status);
                const featureDetails = getFeatureDetails(asset);
                const avatarColor = getAvatarColor(asset.assetCategory);
                const partPrefix = asset.partPrefix;
                const avatarContent = asset.imageURL ? `<img src="${asset.imageURL}" class="avatar-img" alt="${asset.partDescription}">` : partPrefix;
                const actionButtons = getActionButtons(asset);

                return `
                    <div class="col-xs-12 col-sm-6 col-md-6 col-lg-4 col-xl-4">
                        <div class="asset-card p-3 position-relative" data-asset-id="${index}">

                            ${statusBadge}

                            <div class="card-header-custom row g-0 align-items-center">
                                <div class="col-3">
                                    <div class="card-header-avatar" style="background-color: ${asset.imageURL ? 'transparent' : avatarColor};">
                                        ${avatarContent}
                                    </div>
                                </div>
                                <div class="col-9">
                                    <div class="row g-0">
                                        <div class="col-8">
                                            <h5 class="asset-card-title mb-0"><strong>${asset.partDescription}</strong></h5>
                                            <small class="asset-subtitle">Asset ID: ${asset.assetID}</small>
                                            <div class="asset-subtitle">Category: ${asset.assetCategory}</div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="d-flex flex-column align-items-end">
                                                <div class="mb-2">
                                                    <i class="bi bi-pencil-square me-2 edit-asset-icon" data-bs-placement="top" title="Edit Asset" data-asset-id="${index}"></i>
                                                    <i class="bi bi-clock-history me-2" data-bs-placement="top" title="Audit Trail"></i>
                                                    <i class="bi bi-file-earmark-text" data-bs-placement="top" title="Document Management"></i>
                                                </div>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body-custom">
                                <div class="row g-2 mb-2">
                                    <div class="col-6 text-start">
                                        <strong>Brand:</strong> ${asset.brand}
                                    </div>
                                    <div class="col-6 text-start">
                                        <strong>Model:</strong> ${asset.model}
                                    </div>
                                </div>
                                <div class="row g-2 mb-2">
                                    <div class="col-6 text-start">
                                        <strong>Type:</strong> ${asset.productType}
                                    </div>
                                    <div class="col-6 text-start">
                                        <strong>Class:</strong> ${asset.class}
                                    </div>
                                </div>
                                <hr class="my-2">
                                <div class="row g-2 mb-2">
                                    <div class="col-6 text-start">
                                        <strong>Assigned To:</strong> ${asset.assignedTo}
                                    </div>
                                    <div class="col-6 text-start">
                                        <strong>Location:</strong> ${asset.assignedLocation}
                                    </div>
                                </div>
                                <div class="row g-2">
                                    <div class="col-6 text-start">
                                        <strong>Purchase Date:</strong> ${asset.purchaseDate}
                                    </div>
                                    <div class="col-6 text-start">
                                        <strong>Last Serviced:</strong> ${asset.lastServiced}
                                    </div>
                                </div>
                                <div class="row g-2">
                                    <div class="col-6 text-start">
                                        <strong>Warranty Ends:</strong> ${asset.warrantyEnds}
                                    </div>
                                    <div class="col-6 text-start">
                                        <strong>Depreciation:</strong> N/A
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer-custom">
                                ${featureDetails}
                                <hr class="my-2">
                                <p class="mb-0 text-muted"><strong>Purpose:</strong> ${asset.purpose}</p>
                                <p class="mb-0 text-muted"><strong>Remarks:</strong> ${asset.remarks}</p>
                                <hr class="my-2">
                                <div class="d-flex justify-content-end">
                                    ${actionButtons}
                                </div>
                            </div>
                        </div>
                    </div>
                    `;
            };

            const generateKanbanCardHtml = (asset, index) => {
                const avatarColor = getAvatarColor(asset.assetCategory);
                const partPrefix = asset.partPrefix;
                const avatarContent = asset.imageURL ? `<img src="${asset.imageURL}" class="avatar-img" alt="${asset.partDescription}">` : partPrefix;

                return `
                    <div class="asset-card p-3" data-asset-id="${index}">
                        <div class="card-header-custom row g-0 align-items-center">
                            <div class="col-3">
                                <div class="card-header-avatar" style="background-color: ${asset.imageURL ? 'transparent' : avatarColor};">
                                    ${avatarContent}
                                </div>
                            </div>
                            <div class="col-9">
                                <h6 class="asset-card-title mb-0"><strong>${asset.partDescription}</strong></h6>
                                <small class="asset-subtitle">Asset ID: ${asset.assetID}</small>
                            </div>
                        </div>
                        <div class="card-body-custom">
                            <p class="mb-1"><strong>Brand:</strong> ${asset.brand}</p>
                            <p class="mb-0"><strong>Location:</strong> ${asset.assignedLocation}</p>
                        </div>
                    </div>
                    `;
            };

            const generateListHtml = (asset, index) => {
                const statusBadge = getStatusBadge(asset.status);
                const avatarColor = getAvatarColor(asset.assetCategory);
                const partPrefix = asset.partPrefix;
                const avatarContent = asset.imageURL ? `<img src="${asset.imageURL}" class="avatar-img rounded-circle" alt="${asset.partDescription}">` : partPrefix;
                const actionButtons = getActionButtons(asset);

                return `
                                                  <div class="col-12">
                                                      <div class="list-card d-flex flex-column flex-md-row align-items-md-center justify-content-between p-3" data-asset-id="${index}">
                                                          <div class="d-flex align-items-center mb-3 mb-md-0">
                                                              <div class="card-header-avatar me-3" style="background-color: ${asset.imageURL ? 'transparent' : avatarColor};">
                                                                  ${avatarContent}
                                                              </div>
                                                              <div class="asset-info">
                                                                  <h5 class="asset-card-title mb-0"><strong>${asset.partDescription}</strong></h5>
                                                                  <small class="text-muted">ID: ${asset.assetID} | Category: ${asset.assetCategory}</small>
                                                              </div>
                                                          </div>
                                                          <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center asset-actions">
                                                              <div class="d-flex align-items-center mb-2 mb-md-0 me-md-3">
                                                                  <div class="me-2">${statusBadge}</div>
                                                                  <i class="bi bi-pencil-square me-2 edit-asset-icon" data-bs-toggle="tooltip" title="Edit" data-asset-id="${index}"></i>
                                                                  <i class="bi bi-clock-history me-2 text-secondary" data-bs-toggle="tooltip" title="Audit"></i>
                                                                  <i class="bi bi-file-earmark-text text-info" data-bs-toggle="tooltip" title="Documents"></i>
                                                              </div>
                                                              <div class="btn-group">
                                                                  ${actionButtons}
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                                              `;
            };

            const generateGridHtml = (assets) => {
                let tableHtml = `
                                                  <div class="col-12">
                                                      <div class="table-view-container table-responsive">
                                                          <table class="table table-hover align-middle">
                                                              <thead class="table-light">
                                                                  <tr>
                                                                      <th>Asset ID</th>
                                                                      <th>Description</th>
                                                                      <th>Category</th>
                                                                      <th>Brand</th>
                                                                      <th>Model</th>
                                                                      <th>Status</th>
                                                                      <th>Location</th>
                                                                      <th class="text-center">Actions</th>
                                                                  </tr>
                                                              </thead>
                                                              <tbody>
                                              `;
                assets.forEach((asset, index) => {
                    const statusBadge = getStatusBadge(asset.status);
                    const actions = `
                                                  <div class="d-flex justify-content-center">
                                                      <i class="bi bi-pencil-square me-2 text-primary edit-asset-icon" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit" data-asset-id="${index}"></i>
                                                      <i class="bi bi-clock-history me-2 text-secondary" data-bs-toggle="tooltip" data-bs-placement="top" title="Audit Trail"></i>
                                                      <i class="bi bi-file-earmark-text text-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Documents"></i>
                                                      ${getActionButtons(asset)}
                                                  </div>
                                              `;
                    tableHtml += `
                                                  <tr data-asset-id="${index}">
                                                      <td>${asset.assetID}</td>
                                                      <td>${asset.partDescription}</td>
                                                      <td>${asset.assetCategory}</td>
                                                      <td>${asset.brand}</td>
                                                      <td>${asset.model}</td>
                                                      <td>${statusBadge}</td>
                                                      <td>${asset.assignedLocation}</td>
                                                      <td class="text-center">${actions}</td>
                                                  </tr>
                                              `;
                });
                tableHtml += `
                                                              </tbody>
                                                          </table>
                                                      </div>
                                                  </div>`;
                return tableHtml;
            };

            const kanbanStatuses = ['Active', 'Idle', 'Under Maintenance', 'Retired'];
            function initializeKanbanBoard() {
                if (!kanbanContainer) return;
                kanbanContainer.innerHTML = '';
                kanbanStatuses.forEach(status => {
                    const statusId = status.replace(/\s+/g, '-').toLowerCase();
                    const columnHTML = `
                        <div class="kanban-column" id="column-${statusId}">
                            <div class="kanban-column-header">
                                <h6 class="kanban-column-title">${status}</h6>
                                <span class="kanban-item-count" id="count-${statusId}">0</span>
                            </div>
                            <div class="kanban-column-body" id="body-${statusId}">
                                </div>
                        </div>
                    `;
                    kanbanContainer.innerHTML += columnHTML;
                });
            }

            function renderAssetsToKanban(assets) {
                initializeKanbanBoard();
                assets.forEach((asset, index) => {
                    const statusId = asset.status.replace(/\s+/g, '-').toLowerCase();
                    const columnBody = document.getElementById(`body-${statusId}`);
                    if (columnBody) {
                        const assetCard = document.createElement('div');
                        assetCard.innerHTML = generateKanbanCardHtml(asset, index)
                            .replace('<div class="col-xs-12 col-sm-6 col-md-6 col-lg-4 col-xl-4">', '')
                            .replace(/<\/div>\s*$/, '');

                        assetCard.querySelector('.asset-card').addEventListener('click', (e) => {
                            if (e.target.closest('.edit-asset-icon') || e.target.closest('.btn')) {
                                return;
                            }
                            showModal(asset, index);
                        });
                        columnBody.appendChild(assetCard);
                        const countElement = document.getElementById(`count-${statusId}`);
                        countElement.textContent = parseInt(countElement.textContent, 10) + 1;
                    }
                });
            }

            const cleanupModals = () => {
                setTimeout(() => {
                    document.body.classList.remove('modal-open-no-scroll');
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                }, 100);
            };

            const renderView = (view, dataToRender = assetsData) => {
                const listContainer = document.getElementById('asset-view-container');
                const dashboardContainer = document.getElementById('dashboard-view');
                const viewToggleButtons = document.querySelector('.view-toggle-buttons');
                const dashboardBtn = document.getElementById('dashboard-toggle-btn');
                const addCategoryBtnContainer = document.getElementById('add-category-btn-container');

                if (!listContainer || !dashboardContainer || !kanbanContainer) return;

                listContainer.style.display = 'none';
                dashboardContainer.style.display = 'none';
                kanbanContainer.style.display = 'none';
                addCategoryBtnContainer.classList.add('d-none');

                if (view === 'dashboard') {
                    dashboardContainer.style.display = 'flex';
                    viewToggleButtons.style.display = 'none';
                    dashboardBtn.innerHTML = '<i class="bi bi-arrow-left me-2"></i> Back to Assets';
                    renderDashboardCharts(dataToRender);
                } else {
                    viewToggleButtons.style.display = 'flex';
                    dashboardBtn.innerHTML = '<i class="bi bi-speedometer"></i> Dashboard';
                    if (view === 'kanban') {
                        kanbanContainer.style.display = 'flex';
                        renderAssetsToKanban(dataToRender);
                    } else {
                        listContainer.style.display = 'flex';
                        listContainer.innerHTML = '';
                        if (dataToRender.length === 0) {
                            listContainer.innerHTML = `<p class="text-center mt-5 w-100">No assets found for this search term.</p>`;
                        }
                        if (view === 'card') {
                            listContainer.classList.add('g-4');
                            dataToRender.forEach((asset, index) => {
                                listContainer.innerHTML += generateCardHtml(asset, index);
                            });
                        } else if (view === 'list') {
                            listContainer.classList.remove('g-4');
                            dataToRender.forEach((asset, index) => {
                                listContainer.innerHTML += generateListHtml(asset, index);
                            });
                        } else if (view === 'grid') {
                            listContainer.classList.remove('g-4');
                            listContainer.innerHTML = generateGridHtml(dataToRender);
                        }
                    }
                }
            };

            const filterAssets = (query) => {
                const lowerCaseQuery = query.toLowerCase();
                const filteredData = assetsData.filter(asset =>
                    asset.partDescription.toLowerCase().includes(lowerCaseQuery) ||
                    asset.assetID.toLowerCase().includes(lowerCaseQuery) ||
                    asset.assetCategory.toLowerCase().includes(lowerCaseQuery)
                );

                const existingCategories = assetsData.map(asset => asset.assetCategory.toLowerCase());
                const addCategoryBtnContainer = document.getElementById('add-category-btn-container');

                if (query.trim() !== '' && !existingCategories.includes(lowerCaseQuery)) {
                    addCategoryBtnContainer.classList.remove('d-none');
                } else {
                    addCategoryBtnContainer.classList.add('d-none');
                }

                const currentViewBtn = document.querySelector('.view-toggle-btn.active');
                const currentView = currentViewBtn ? currentViewBtn.dataset.view : 'card';
                renderView(currentView, filteredData);
            };

            const setActiveButton = (activeBtn) => {
                const buttons = document.querySelectorAll('.view-toggle-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
                activeBtn.classList.add('active');
            };

            const showModal = (asset, assetId) => {
                const modalContainer = document.getElementById('asset-detail-modal');
                modalBody.innerHTML = generateFullModalBodyHtml(asset);
                modalContainer.classList.add('show');
                document.body.classList.add('modal-open-no-scroll');
                modalCurrentAssetId = assetId;
            };

            const hideModal = () => {
                const modalContainer = document.getElementById('asset-detail-modal');
                modalContainer.classList.remove('show');
                document.body.classList.remove('modal-open-no-scroll');
                cleanupModals();
            };

            const showImagePreview = (imageUrl) => {
                const imagePreviewModal = document.getElementById('image-preview-modal');
                const fullImage = document.getElementById('full-image');
                if (imageUrl && imagePreviewModal && fullImage) {
                    fullImage.src = imageUrl;
                    imagePreviewModal.classList.add('show');
                }
            };

            const hideImagePreview = () => {
                const imagePreviewModal = document.getElementById('image-preview-modal');
                if (imagePreviewModal) {
                    imagePreviewModal.classList.remove('show');
                }
            };

            const showEditModal = (asset) => {
                const modalContainer = document.getElementById('asset-edit-modal');
                const hasSerialCheckbox = document.getElementById('edit-has-serial-number');
                const quantityInput = document.getElementById('edit-quantity');

                document.getElementById('edit-asset-name').textContent = asset.partDescription;
                document.getElementById('edit-asset-subtitle').textContent = `ID: ${asset.assetID} • Category: ${asset.assetCategory}`;
                document.getElementById('edit-asset-id').textContent = asset.assetID;
                document.getElementById('edit-asset-category').textContent = asset.assetCategory;
                document.getElementById('edit-asset-brand').textContent = asset.brand;
                document.getElementById('edit-asset-model').textContent = asset.model;
                document.getElementById('edit-avatar-img').src = asset.imageURL || 'https://placehold.co/100x100/A4F4FF/000032?text=AM';

                document.getElementById('edit-asset-description').value = asset.partDescription;
                document.getElementById('edit-assigned-to').value = asset.assignedTo;
                document.getElementById('edit-purpose').value = asset.purpose;
                document.getElementById('edit-remarks').value = asset.remarks;
                document.getElementById('edit-image-url').value = asset.imageURL;
                document.getElementById('edit-assigned-location').value = asset.assignedLocation;
                document.getElementById('edit-last-known-location').value = asset.lastKnownLocation;
                document.getElementById('edit-gps-tracking').checked = asset.hasGpsTracking;
                document.getElementById('edit-warranty-tracking').value = asset.maintenance?.warrantyTracking || 'N/A';
                document.getElementById('edit-scheduled-maintenance').value = asset.maintenance?.scheduled.map(s => `${s.task} on ${s.date}`).join('\n') || '';
                document.getElementById('edit-maintenance-history').value = asset.maintenance?.history.map(h => `${h.date}: ${h.service}`).join('\n') || '';
                document.getElementById('edit-service-providers').value = asset.maintenance?.providers.join(', ') || '';

                hasSerialCheckbox.checked = asset.withSerial;
                if (asset.withSerial) {
                    quantityInput.value = 1;
                    quantityInput.disabled = true;
                } else {
                    quantityInput.value = asset.quantity;
                    quantityInput.disabled = false;
                }
                modalContainer.classList.add('show');
                document.body.classList.add('modal-open-no-scroll');
                const editImage = document.getElementById('edit-avatar-img');
                editImage.onclick = () => showImagePreview(editImage.src);
            };

            const hideEditModal = () => {
                const modalContainer = document.getElementById('asset-edit-modal');
                modalContainer.classList.remove('show');
                document.body.classList.remove('modal-open-no-scroll');
                cleanupModals();
            };

            const updateMetricCards = (data = assetsData) => {
                const totalAssetsCount = data.length;
                let activeAssetsCount = 0;
                let maintenanceAssetsCount = 0;
                let gpsTrackingCount = 0;

                data.forEach(asset => {
                    switch (asset.status) {
                        case 'Active':
                            activeAssetsCount++;
                            break;
                        case 'Under Maintenance':
                            maintenanceAssetsCount++;
                            break;
                    }
                    if (asset.hasGpsTracking) {
                        gpsTrackingCount++;
                    }
                });

                document.getElementById('total-assets-count').textContent = totalAssetsCount;
                document.getElementById('active-assets-count').textContent = activeAssetsCount;
                document.getElementById('maintenance-assets-count').textContent = maintenanceAssetsCount;
                document.getElementById('gps-tracking-count').textContent = gpsTrackingCount;
            };

            const renderDashboardCharts = (data = assetsData) => {
                const statusCounts = {};
                data.forEach(asset => {
                    statusCounts[asset.status] = (statusCounts[asset.status] || 0) + 1;
                });

                const statusLabels = Object.keys(statusCounts);
                const statusData = Object.values(statusCounts);

                const categoryCounts = {};
                data.forEach(asset => {
                    categoryCounts[asset.assetCategory] = (categoryCounts[asset.assetCategory] || 0) + 1;
                });

                const categoryLabels = Object.keys(categoryCounts);
                const categoryData = Object.values(categoryCounts);

                const statusChartCtx = document.getElementById('asset-status-chart').getContext('2d');
                if (myChart) {
                    myChart.destroy();
                }
                myChart = new Chart(statusChartCtx, {
                    type: 'bar',
                    data: {
                        labels: statusLabels,
                        datasets: [{
                            label: 'Asset Status',
                            data: statusData,
                            backgroundColor: ['#2EC0CB', '#f57c00', '#e53e3e', '#6c757d'],
                            borderColor: ['#23A3AD', '#e65100', '#b71c1c', '#495057'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                const categoryChartCtx = document.getElementById('asset-category-chart').getContext('2d');
                if (myPieChart) {
                    myPieChart.destroy();
                }
                myPieChart = new Chart(categoryChartCtx, {
                    type: 'pie',
                    data: {
                        labels: categoryLabels,
                        datasets: [{
                            label: 'Assets by Category',
                            data: categoryData,
                            backgroundColor: ['#0F5FDC', '#2EC0CB', '#FFD600', '#4338ca'],
                            hoverOffset: 4
                        }]
                    },
                    options: {
                        responsive: true,
                    }
                });
            };

            document.addEventListener('DOMContentLoaded', () => {
                const addAssetModal = new bootstrap.Modal(document.getElementById('addAssetModal'));
                const addAssetForm = document.getElementById('addAssetForm');
                const messageBox = document.getElementById('messageBox');
                const messageClose = document.getElementById('messageClose');
                const viewButtons = document.querySelectorAll('.view-toggle-btn');
                const assetViewContainer = document.getElementById('asset-view-container');
                const modalCloseBtn = document.getElementById('modal-close-btn');
                const toggleMetricsBtn = document.getElementById('toggle-metrics-btn');
                const metricsPanel = document.getElementById('metrics-panel');
                const editModalCloseBtn = document.getElementById('edit-modal-close-btn');
                const cancelEditBtn = document.getElementById('cancel-edit-btn');
                const dashboardToggleBtn = document.getElementById('dashboard-toggle-btn');
                const fullscreenToggleBtn = document.getElementById('fullscreen-toggle-btn');
                const fullscreenIcon = document.getElementById('fullscreen-icon');
                const fullscreenText = document.getElementById('fullscreen-text');
                const assetDetailModal = document.getElementById('asset-detail-modal');
                const purchaseModal = new bootstrap.Modal(document.getElementById('purchaseModal'));
                const issueOutModal = new bootstrap.Modal(document.getElementById('issueOutModal'));
                const scrapModal = new bootstrap.Modal(document.getElementById('scrapModal'));
                const editHasSerialCheckbox = document.getElementById('edit-has-serial-number');
                const editQuantityInput = document.getElementById('edit-quantity');
                const searchInput = document.getElementById('dynamic-search-input');
                const addCategoryBtnContainer = document.getElementById('add-category-btn-container');
                const addNewCategoryBtn = document.getElementById('add-new-category-btn');
                kanbanContainer = document.getElementById('kanban-view-container');
                modalBody = document.getElementById('modal-body-content');

                const addOptionModal = document.getElementById('add-option-modal');
                const addOptionModalTitle = document.getElementById('add-option-modal-title');
                const addOptionForm = document.getElementById('addOptionForm');
                const addOptionCloseBtn = document.getElementById('add-option-close-btn');

                let currentSelectElement = null;

                const findAsset = (id) => assetsData.find(asset => asset.assetID === id);

                addOptionCloseBtn.addEventListener('click', () => {
                    addOptionModal.classList.remove('show');
                    addAssetModal._dialog.classList.remove('hidden-for-add-option');
                });

                document.querySelectorAll('.add-new-option-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const targetId = button.dataset.targetSelect;
                        currentSelectElement = document.getElementById(targetId);

                        const labelText = currentSelectElement.previousElementSibling.textContent.trim();
                        addOptionModalTitle.textContent = `Add New ${labelText}`;

                        addOptionModal.classList.add('show');
                        addAssetModal._dialog.classList.add('hidden-for-add-option'); // Hide the main modal dialog slightly
                    });
                });

                addOptionForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    const newCode = document.getElementById('add-option-code').value;
                    const newDescription = document.getElementById('add-option-description').value;

                    if (currentSelectElement && newDescription) {
                        const newOption = document.createElement('option');
                        newOption.value = newDescription;
                        newOption.textContent = newDescription;
                        currentSelectElement.appendChild(newOption);
                        currentSelectElement.value = newDescription;

                        // Dynamically update the base data if needed
                        const fieldName = currentSelectElement.id.replace('add-asset-', '');
                        if (fieldName === 'category' || fieldName === 'functional-group' || fieldName === 'uom' || fieldName === 'productType') {
                            if (fieldName === 'category') assetsData.push({ assetCategory: newDescription });
                            // This is a simple example. A real app would update the data source properly.
                        }

                        showMessage(`Added new option: ${newDescription}!`);
                        addOptionModal.classList.remove('show');
                        addAssetModal._dialog.classList.remove('hidden-for-add-option');
                    } else {
                        showMessage('Please provide a description.', 'danger');
                    }
                    addOptionForm.reset();
                });


                addAssetForm.addEventListener('submit', (e) => {
                    e.preventDefault();

                    const partId = document.getElementById('add-asset-part-id').value;
                    const category = document.getElementById('add-asset-category').value;
                    const functionalGroup = document.getElementById('add-asset-functional-group').value;
                    const uom = document.getElementById('add-asset-uom').value;
                    const dimension = document.getElementById('add-asset-dimension').value;
                    const productType = document.getElementById('add-asset-productType').value;
                    const depRate = parseFloat(document.getElementById('add-asset-dep-rate').value) || 0;
                    const withMaintenance = document.getElementById('add-asset-maintenance').checked;
                    const isLockedForPurchase = document.getElementById('add-asset-purchase-lock').checked;
                    const purpose = document.getElementById('add-asset-purpose').value;
                    const remarks = document.getElementById('add-asset-remarks').value;

                    const description = document.getElementById('add-asset-description').value;
                    const weight = parseFloat(document.getElementById('add-asset-weight').value) || 0;
                    const brand = document.getElementById('add-asset-brand').value;
                    const model = document.getElementById('add-asset-model').value;
                    const depYears = parseInt(document.getElementById('add-asset-dep-years').value, 10) || 0;
                    const withAssetTag = document.getElementById('add-asset-asset-tag').checked;
                    const withSerial = document.getElementById('add-asset-serial').checked;
                    const isLockedForIssues = document.getElementById('add-asset-issue-lock').checked;
                    const isActive = document.getElementById('add-asset-is-active').checked;

                    const newAsset = {
                        "partPrefix": category.substring(0, 2).toUpperCase(),
                        "partDescription": description,
                        "assetID": partId,
                        "assetCategory": category,
                        "functionalGroup": functionalGroup,
                        "uom": uom,
                        "weight": weight,
                        "dimension": dimension,
                        "status": isActive ? "Active" : "Idle",
                        "brand": brand,
                        "productType": productType,
                        "model": model,
                        "class": "N/A",
                        "engineType": "N/A",
                        "assignedTo": "Unassigned",
                        "assignedLocation": "Unassigned",
                        "lastKnownLocation": "Unassigned",
                        "purchaseDate": new Date().toISOString().slice(0, 10),
                        "lastServiced": "N/A",
                        "warrantyEnds": "",
                        "withMaintenance": withMaintenance,
                        "withAssetTag": withAssetTag,
                        "withSerial": withSerial,
                        "isLockedForIssues": isLockedForIssues,
                        "isLockedForPurchase": isLockedForPurchase,
                        "hasGpsTracking": false,
                        "purpose": purpose,
                        "remarks": remarks,
                        "imageURL": null,
                        "originalCost": 0,
                        "quantity": 1,
                        "depreciationRate": depRate,
                        "depreciationYears": depYears,
                        "bom": null,
                        "maintenance": {
                            "scheduled": [],
                            "history": [],
                            "providers": [],
                            "warrantyTracking": 'N/A'
                        }
                    };

                    assetsData.unshift(newAsset);
                    updateMetricCards(assetsData);
                    const currentView = document.querySelector('.view-toggle-btn.active').dataset.view || 'card';
                    renderView(currentView, assetsData);
                    addAssetModal.hide();
                    showMessage('Asset added successfully! 🚀');
                    addAssetForm.reset();
                    populateDropdowns();
                });

                const advancedSearchModal = new bootstrap.Offcanvas(document.getElementById('advancedSearchModal'));
                const advancedSearchForm = document.getElementById('advancedSearchForm');

                if (advancedSearchForm) {
                    advancedSearchForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        const statusFilter = document.getElementById('filter-status').value;
                        const startDateFilter = document.getElementById('filter-purchase-date-start').value;
                        const endDateFilter = document.getElementById('filter-purchase-date-end').value;
                        const hasGpsFilter = document.getElementById('filter-has-gps').checked;

                        let filteredAssets = assetsData.filter(asset => {
                            let isMatch = true;
                            if (statusFilter !== 'Any Status' && asset.status !== statusFilter) {
                                isMatch = false;
                            }
                            if (hasGpsFilter && !asset.hasGpsTracking) {
                                isMatch = false;
                            }
                            const purchaseDate = new Date(asset.purchaseDate);
                            if (startDateFilter && purchaseDate < new Date(startDateFilter)) {
                                isMatch = false;
                            }
                            if (endDateFilter) {
                                const endDate = new Date(endDateFilter);
                                endDate.setHours(23, 59, 59, 999);
                                if (purchaseDate > endDate) {
                                    isMatch = false;
                                }
                            }
                            return isMatch;
                        });
                        const currentView = document.querySelector('.view-toggle-btn.active').dataset.view || 'card';
                        renderView(currentView, filteredAssets);
                        advancedSearchModal.hide();
                    });
                }

                const showMessage = (message, type = 'success') => {
                    if (messageBox) {
                        messageBox.textContent = message;
                        if (type === 'success') {
                            messageBox.style.backgroundColor = '#28a745';
                        } else {
                            messageBox.style.backgroundColor = '#dc3545';
                        }
                        messageBox.classList.add('show');
                        setTimeout(() => messageBox.classList.remove('show'), 3000);
                    }
                };

                if (messageClose) messageClose.addEventListener('click', () => messageBox.classList.remove('show'));

                // Event listener to disable scrolling on modal open
                document.addEventListener('show.bs.modal', function (e) {
                    // Check if it's the main addAssetModal
                    if (e.target.id === 'addAssetModal') {
                        document.body.classList.add('modal-open-no-scroll');
                    } else if (e.target.id === 'add-option-modal') {
                        document.body.classList.add('modal-open-no-scroll');
                    }
                });

                // Event listener to re-enable scrolling on modal close
                document.addEventListener('hide.bs.modal', function () {
                    document.body.classList.remove('modal-open-no-scroll');
                });

                if (modalCloseBtn) modalCloseBtn.addEventListener('click', hideModal);
                if (editModalCloseBtn) editModalCloseBtn.addEventListener('click', hideEditModal);
                if (cancelEditBtn) cancelEditBtn.addEventListener('click', hideEditModal);

                document.getElementById('purchaseForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    purchaseModal.hide();
                    showMessage('Asset(s) purchased successfully! 🛒');
                });

                document.getElementById('issueOutForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    issueOutModal.hide();
                    showMessage('Asset(s) issued out successfully! 📦');
                });

                const uploadToolImageBtn = document.getElementById('upload-image-btn');
                if (uploadToolImageBtn) {
                    uploadToolImageBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        showMessage('Image upload feature is not yet implemented!', 'danger');
                    });
                }

                document.getElementById('scrapForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    const scrapReason = document.getElementById('scrap-reason').value;
                    const asset = assetsData[modalCurrentAssetId];
                    if (asset) {
                        asset.status = "Retired";
                        asset.remarks = `Scrapped on ${new Date().toLocaleDateString()} due to: ${scrapReason}`;
                        updateMetricCards();
                        renderView(document.querySelector('.view-toggle-btn.active').dataset.view);
                        scrapModal.hide();
                        hideModal();
                        showMessage('Asset(s) scrapped successfully! 🔥', 'danger');
                    }
                });

                const saveEditBtn = document.getElementById('save-edit-btn');
                if (saveEditBtn) {
                    saveEditBtn.addEventListener('click', () => {
                        const asset = assetsData[modalCurrentAssetId];
                        if (asset) {
                            asset.partDescription = document.getElementById('edit-asset-description').value;
                            asset.assignedTo = document.getElementById('edit-assigned-to').value;
                            asset.purpose = document.getElementById('edit-purpose').value;
                            asset.remarks = document.getElementById('edit-remarks').value;
                            asset.imageURL = document.getElementById('edit-image-url').value;
                            asset.assignedLocation = document.getElementById('edit-assigned-location').value;
                            asset.lastKnownLocation = document.getElementById('edit-last-known-location').value;
                            asset.hasGpsTracking = document.getElementById('edit-gps-tracking').checked;
                            asset.withSerial = document.getElementById('edit-has-serial-number').checked;
                            asset.quantity = parseInt(document.getElementById('edit-quantity').value, 10);
                            if (asset.withSerial) {
                                asset.quantity = 1;
                            }

                            if (asset.maintenance) {
                                asset.maintenance.warrantyTracking = document.getElementById('edit-warranty-tracking').value;
                                asset.maintenance.providers = document.getElementById('edit-service-providers').value.split(',').map(s => s.trim());
                            }

                            updateMetricCards();
                            const currentView = document.querySelector('.view-toggle-btn.active').dataset.view || 'card';
                            renderView(currentView, assetsData);
                            hideEditModal();
                            showMessage('Changes saved successfully! 🎉');
                        }
                    });
                }

                if (fullscreenToggleBtn) {
                    fullscreenToggleBtn.addEventListener('click', () => {
                        assetDetailModal.classList.toggle('fullscreen');
                        if (assetDetailModal.classList.contains('fullscreen')) {
                            fullscreenIcon.classList.remove('bi-arrows-fullscreen');
                            fullscreenIcon.classList.add('bi-fullscreen-exit');
                            fullscreenText.textContent = 'Exit Fullscreen';
                            modalBody.innerHTML = generateFullModalBodyHtmlFullscreen(assetsData[modalCurrentAssetId]);
                        } else {
                            fullscreenIcon.classList.remove('bi-fullscreen-exit');
                            fullscreenIcon.classList.add('bi-arrows-fullscreen');
                            fullscreenText.textContent = 'Fullscreen';
                            modalBody.innerHTML = generateFullModalBodyHtml(assetsData[modalCurrentAssetId]);
                        }
                    });
                }

                if (toggleMetricsBtn && metricsPanel) {
                    toggleMetricsBtn.addEventListener('click', () => {
                        if (metricsPanel.classList.contains('collapsed')) {
                            metricsPanel.classList.remove('collapsed');
                            metricsPanel.style.maxHeight = metricsPanel.scrollHeight + 'px';
                        } else {
                            metricsPanel.style.maxHeight = metricsPanel.scrollHeight + 'px';
                            setTimeout(() => {
                                metricsPanel.classList.add('collapsed');
                                metricsPanel.style.maxHeight = '0';
                            }, 10);
                        }
                    });
                }

                viewButtons.forEach(btn => {
                    btn.addEventListener('click', () => {
                        const viewType = btn.getAttribute('data-view');
                        setActiveButton(btn);
                        filterAssets(searchInput.value);
                    });
                });

                document.getElementById('asset-detail-modal').addEventListener('click', (event) => {
                    const button = event.target.closest('.bom-toggle-btn');
                    if (button) {
                        event.preventDefault();
                        const targetId = button.getAttribute('data-target');
                        const targetElement = document.querySelector(targetId);

                        if (targetElement) {
                            const isExpanded = button.getAttribute('aria-expanded') === 'true';

                            if (isExpanded) {
                                targetElement.style.maxHeight = targetElement.scrollHeight + 'px';
                                setTimeout(() => {
                                    targetElement.style.maxHeight = '0';
                                    targetElement.classList.add('collapsed');
                                    button.setAttribute('aria-expanded', 'false');
                                }, 10);
                            } else {
                                targetElement.classList.remove('collapsed');
                                targetElement.style.maxHeight = targetElement.scrollHeight + 'px';
                                button.setAttribute('aria-expanded', 'true');
                                setTimeout(() => {
                                    targetElement.style.maxHeight = '500px';
                                }, 300);
                            }
                        }
                    }
                });

                searchInput.addEventListener('input', (e) => {
                    filterAssets(e.target.value);
                });

                addNewCategoryBtn.addEventListener('click', () => {
                    const newCategoryName = searchInput.value;
                    if (newCategoryName.trim() !== '') {
                        console.log(`New category "${newCategoryName}" would be created on the server.`);
                        showMessage(`Category "${newCategoryName}" created successfully!`, 'success');
                        searchInput.value = '';
                        filterAssets('');
                    }
                });

                dashboardToggleBtn.addEventListener('click', () => {
                    const dashboardView = document.getElementById('dashboard-view');
                    if (dashboardView.style.display === 'none') {
                        renderView('dashboard');
                    } else {
                        renderView('card');
                    }
                });

                assetViewContainer.addEventListener('click', (event) => {
                    const target = event.target;
                    const card = target.closest('.asset-card, .list-card, tr');
                    const assetId = card ? card.dataset.assetId : null;

                    if (target.closest('.purchase-asset-btn')) {
                        event.stopPropagation();
                        purchaseModal.show();
                    } else if (target.closest('.issue-out-asset-btn')) {
                        event.stopPropagation();
                        issueOutModal.show();
                    } else if (target.closest('.scrap-asset-btn')) {
                        event.stopPropagation();
                        scrapModal.show();
                    } else if (target.closest('.edit-asset-icon')) {
                        event.stopPropagation();
                        const asset = assetsData[assetId];
                        if (asset) {
                            showEditModal(asset);
                        }
                    } else if (card) {
                        const asset = assetsData[assetId];
                        if (asset) {
                            showModal(asset, assetId);
                        }
                    }
                });

                document.getElementById('edit-asset-detail-btn').addEventListener('click', () => {
                    const asset = assetsData[modalCurrentAssetId];
                    if (asset) {
                        hideModal();
                        showEditModal(asset);
                    }
                });

                // Load the data initially
                loadData();
            });
        })();
    </script>

</body>
</html>
