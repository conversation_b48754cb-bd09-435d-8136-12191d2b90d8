AmCharts.translations[ "export" ][ "es" ] = {
	"fallback.save.text": "CTRL + C para copiar datos en el portapapeles.",
	"fallback.save.image": "Botón derecho -> Guardar imagen como... para guardar la imagen.",

	"capturing.delayed.menu.label": "{{duration}}",
	"capturing.delayed.menu.title": "Click para cancelar",

	"menu.label.print": "Imprimir",
	"menu.label.undo": "<PERSON><PERSON><PERSON>",
	"menu.label.redo": "<PERSON><PERSON><PERSON>",
	"menu.label.cancel": "Cancelar",

	"menu.label.save.image": "Descargar como ...",
	"menu.label.save.data": "Guardar como ...",

	"menu.label.draw": "Anotar ...",
	"menu.label.draw.change": "Cambiar ...",
	"menu.label.draw.add": "Añadir ...",
	"menu.label.draw.shapes": "Forma ...",
	"menu.label.draw.colors": "Color ...",
	"menu.label.draw.widths": "Tamaño ...",
	"menu.label.draw.opacities": "Opacidad ...",
	"menu.label.draw.text": "Texto",

	"menu.label.draw.modes": "Modo ...",
	"menu.label.draw.modes.pencil": "Lápiz",
	"menu.label.draw.modes.line": "Linea",
	"menu.label.draw.modes.arrow": "Flecha",

	"label.saved.from": "Guardar desde: "
}