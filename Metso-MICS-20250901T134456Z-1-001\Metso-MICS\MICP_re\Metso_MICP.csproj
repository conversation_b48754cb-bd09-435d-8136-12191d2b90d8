﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F49D2AB9-6111-4303-BB3A-0F7D17A0B68B}</ProjectGuid>
    <ProjectTypeGuids>{E3E379DF-F4C6-4180-9B81-6769533ABE47};{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Metso_MICP</RootNamespace>
    <AssemblyName>MICP_re</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort>44300</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
    <UseGlobalApplicationHostFile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Filterandsortgriddata">
      <HintPath>..\..\..\AshwiniData\AssemblyFilesFor Visual Studio\Filterandsortgriddata.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp">
      <HintPath>..\..\..\AshwiniData\AssemblyFilesFor Visual Studio\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="Kentor.OwinCookieSaver, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Kentor.OwinCookieSaver.1.0.0\lib\net45\Kentor.OwinCookieSaver.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocol.Extensions">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.IdentityModel.Protocol.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OpenIdConnect">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Microsoft.Owin.Security.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IdentityModel.Tokens.Jwt">
      <HintPath>..\..\..\AzureImplementaion\AzureDlls\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.DynamicData">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Entity">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.ApplicationServices">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\..\..\..\AshwiniData\AssemblyFilesFor Visual Studio\dll_files\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Extensions">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Abstractions">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Routing">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Configuration">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Services">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.EnterpriseServices">
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.4.0.20710.0\lib\net40\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.4.0.20710.0\lib\net40\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.4.0.20710.0\lib\net40\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.4.0.20710.0\lib\net40\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.2.0.20710.0\lib\net40\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.2.0.20710.0\lib\net40\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controllers\ArchiveController.cs" />
    <Compile Include="Controllers\CheckSheetController.cs" />
    <Compile Include="Controllers\CommissionningController.cs" />
    <Compile Include="Controllers\ErrorLogController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\InstalltionProtocolController.cs" />
    <Compile Include="Controllers\LogController.cs" />
    <Compile Include="Controllers\MicpApiController.cs" />
    <Compile Include="Controllers\PdfController.cs" />
    <Compile Include="Controllers\ProjectController.cs" />
    <Compile Include="Controllers\ReportsController.cs" />
    <Compile Include="Controllers\SettingsController.cs" />
    <Compile Include="Controllers\HydraAvailibilityController.cs" />
    <Compile Include="Controllers\IdleReportsController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Models\getServiceRequest1_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\getServiceRequest_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_Branch.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_Company.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_CompanyEmployee.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_Email.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_Email_h.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_EmployeeBranch.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_RefMaster.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_RefMasterDetail.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_State.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_User.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GNM_UserCompany.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\GridParams.cs" />
    <Compile Include="Models\JQGridExtensionMethods.cs" />
    <Compile Include="Models\MA_SESSION.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Ma_SIC_CheckSheetStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Ma_SIC_Itemstypes.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Ma_SIC_Models.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Metso_MICP.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Metso_MICP.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Metso_MICP.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Metso_MICP.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Metso_MICP.edmx</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_CheckSheetConstraints.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_CheckSheetSupplyStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Contractor.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_ContractorSitePersonDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_CurrentStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_CheckPointSupplyStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_CheckSheetActivityStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_CommissioningStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_Commission_Activity.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_Commission_ErrorBase.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_CS_InstallationProtocolChekPointsDetailer.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_CS_InstallationProtocolSpecs.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_DispatcherPdfCount.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_EarthingType.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_ElectricalCommissioningChecklist.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_ElectricalCommssioningRemarks.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_EngineerRemarks.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_HydraAvailabilityIdleReason.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_HydraAvailabilityStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_HydraNames.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_IdleReportDesignation.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_IdleReport_IdleReasonCategory.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_InstallationCurrentStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_InstallationProtocolApprovalStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_InstallationProtocolCheckPoints.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_InstallationProtocolCheckptsStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_InstallationProtocolStatusMachineWise.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_OrderTerms.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_PlantStageNumber.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_PlantStages.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_PlantType.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_ProjectCurrentStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_ProjectStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_MA_ProtocolStageStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_Short_SupplyStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_ma_StatesAssociation.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_SupplyStatus.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Ma_ZeroDateMileStone.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_CheckSheetDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_CommissioningDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ElectricalInstallationProtocolHeader.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_TR_ElectricalMotorData.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_HydraAvailabilityDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_IdleReportDesignationDetailer.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_IdleReportDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_InstallationProtocol.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_InstalltionProtocolDetailer.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ProjectDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ProjectElectrical_CommissioningCheckup.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ProjectElectrical_InstallationCheckup.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ProjectElectrical_PreCommissioningCheckup.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ProjectMovementDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ProjectPlantDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ProjectStageWiseMomentlog.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\SIC_Tr_ShortSupplyDetails.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\sp_helpdiagramdefinition_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\sp_helpdiagrams_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Up_SelAgingAnalysis_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Up_SelObsolensence_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\UP_SELTOP10PARTS_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_AllProjectList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_ArchiveProjectlist_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_CheckSheetDetailsList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_CommissionDetailsList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_CommissioningDetailsListForGrid_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Usp_EmployeeDetailsForProject_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_fetchCustomerNames_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_fetchVehicleandPartyDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_getAssignToRegionMetsoFC_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Usp_getContactDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USp_GetContractorDetailsForProject_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_GetContractorPersonDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Usp_GetElectricalCommissoingDataForProject_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_GetElectricalInstallationCommissioningDataForProject_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_GetElectricalMotorDataForProject_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_GetElectricalPreCommissioningDataForProject_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Usp_GetInstalltionProtocolDetailerDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Usp_GetInstalltionProtocolHeaderDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetModelDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\Usp_GetPlantdetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetProjectDetailsForCheckSheetnew_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetProjectDetailsForCheckSheet_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetProjectDetailsForCommissionning_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetProjectDetailsForHydraAndIdle1_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetProjectDetailsForHydraAndIdle_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetProjectDetailsForIdlereason_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USp_GetProjectDetailsFOrWeb_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USp_GetProjectDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USp_GetProjectMovementDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_GetRegion_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_GetShortSupplies_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_GetStageWiseMOvementHistoryForProject_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_Get_InstallationProtocolSpecsDetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_IdleReasonDesignationDetailsList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_IdleReasonDetailsList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_IdleReportdetails_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_LoginAuthentication_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_GetCheckSheetDetailsList_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_GetCommissioningDetailsList_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_GetHydraAvailabilityDetailsList_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_GetIdleReportDetails_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_GetProjectDetails_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_GetProjectPlantDetails_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_GetShortSuppyDetails_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP_ProjectRejectionSummary_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_MICP__GetMailId_API_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_NewHydraAvailbilityDetailsListForGrid_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_ParticularEmployeReportList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_ParticularRIEReportListForEngineer_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_ParticularRIEReportListForState_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_ParticularRIEReportList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_SheckSheetDetailsList_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\usp_ShortSupplieDetailsForArchive_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_SIC_LoginCheck_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_SIC_LoginCheck_Test_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="Models\USP_ToFetchServiceRequest_Result.cs">
      <DependentUpon>Metso_MICP.tt</DependentUpon>
    </Compile>
    <Compile Include="obj\Debug\TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs" />
    <Compile Include="obj\Debug\TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs" />
    <Compile Include="obj\Debug\TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs" />
    <Compile Include="obj\Release\TemporaryGeneratedFile_036C0B5B-1481-4323-8D20-8F5ADCB23D92.cs" />
    <Compile Include="obj\Release\TemporaryGeneratedFile_5937a670-0e60-4077-877b-f7221da3dda1.cs" />
    <Compile Include="obj\Release\TemporaryGeneratedFile_E7A71F73-0F8D-4B9B-B56E-8E70B10BC5D3.cs" />
    <Compile Include="App_Data\AssemblyInfo.cs" />
    <Compile Include="Startup.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Content Include="Assets\CSS\HydraAvailability.css" />
    <Content Include="Assets\CSS\_Layout.css" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\css\font-awesome.css" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\css\font-awesome.min.css" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\fonts\fontawesome-webfont.svg" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\HELP-US-OUT.txt" />
    <Content Include="Assets\Images\LeftArrow.png" />
    <Content Include="Assets\Images\Logo\favicon.ico" />
    <Content Include="Assets\Images\Logo\Metso_logo.jpg" />
    <Content Include="Assets\Images\Logo\PdfLogoImg1.jpg" />
    <Content Include="Assets\Images\Logo\PdfLogoImg2.jpg" />
    <Content Include="Assets\Images\MestoBackgroundImage.png" />
    <Content Include="Assets\Images\PasswordPicture1.png" />
    <Content Include="Assets\Images\RightArrow.png" />
    <Content Include="Assets\Images\save1.png" />
    <Content Include="Assets\Images\save2.png" />
    <Content Include="Assets\Images\suportStructure.PNG" />
    <Content Include="Assets\Jqgrid\grid.locale-en.js" />
    <Content Include="Assets\Jqgrid\jquery.jqGrid_4.7.js" />
    <Content Include="Assets\Jqgrid\ui.jqgrid.css" />
    <Content Include="Assets\Plugins\AmCharts\amcharts.js" />
    <Content Include="Assets\Plugins\AmCharts\Chart.min.js" />
    <Content Include="Assets\Plugins\AmCharts\dataloader.js" />
    <Content Include="Assets\Plugins\AmCharts\dataloader.min.js" />
    <Content Include="Assets\Plugins\AmCharts\export.config.default.js" />
    <Content Include="Assets\Plugins\AmCharts\export.css" />
    <Content Include="Assets\Plugins\AmCharts\export.js" />
    <Content Include="Assets\Plugins\AmCharts\export.min.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\blob.js\blob.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\classList.js\classList.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\classList.js\classList.min.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\fabric.js\fabric.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\fabric.js\fabric.min.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\FileSaver.js\FileSaver.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\FileSaver.js\FileSaver.min.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\jszip\jszip.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\jszip\jszip.min.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\pdfmake\pdfmake.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\pdfmake\pdfmake.min.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\pdfmake\vfs_fonts.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\xlsx\xlsx.js" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\xlsx\xlsx.min.js" />
    <Content Include="Assets\Plugins\AmCharts\funnel.js" />
    <Content Include="Assets\Plugins\AmCharts\gantt.js" />
    <Content Include="Assets\Plugins\AmCharts\gauge.js" />
    <Content Include="Assets\Plugins\AmCharts\pdf-icon.png" />
    <Content Include="Assets\Plugins\AmCharts\pie.js" />
    <Content Include="Assets\Plugins\AmCharts\radar.js" />
    <Content Include="Assets\Plugins\AmCharts\responsive.js" />
    <Content Include="Assets\Plugins\AmCharts\serial.js" />
    <Content Include="Assets\Plugins\AmCharts\xy.js" />
    <Content Include="Assets\Plugins\BarFiller\jquery.barfiller.js" />
    <Content Include="Assets\Plugins\BarFiller\style.css" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap-theme.css" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap-theme.min.css" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap.min.css" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap3.6.css" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\js\bootstrap.js" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\js\bootstrap.min.js" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\js\npm.js" />
    <Content Include="Assets\Plugins\CountryState\countries.js" />
    <Content Include="Assets\Plugins\FullCalender\fullcalendar.css" />
    <Content Include="Assets\Plugins\FullCalender\fullcalendar.js" />
    <Content Include="Assets\Plugins\FullCalender\jquery-ui.min.js" />
    <Content Include="Assets\Plugins\FullCalender\jquery.min.js" />
    <Content Include="Assets\Plugins\FullCalender\locale-all.js" />
    <Content Include="Assets\Plugins\FullCalender\moment.min.js" />
    <Content Include="Assets\Plugins\GMaps\gmap3.js" />
    <Content Include="Assets\Plugins\JQGrid\JQGrid-4.7.0\grid.locale-en.js" />
    <Content Include="Assets\Plugins\JQGrid\JQGrid-4.7.0\jquery.jqGrid.js" />
    <Content Include="Assets\Plugins\JQGrid\JQGrid-4.7.0\ui.jqgrid.css" />
    <Content Include="Assets\Plugins\JQGrid\JQGrid-5.0\grid.locale-en.js" />
    <Content Include="Assets\Plugins\JQGrid\JQGrid-5.0\jquery.jqGrid.js" />
    <Content Include="Assets\Plugins\JQGrid\JQGrid-5.0\ui.jqgrid.css" />
    <Content Include="Assets\Plugins\JQPlot\Chart.min.js" />
    <Content Include="Assets\Plugins\JQPlot\jqplot.barRenderer.js" />
    <Content Include="Assets\Plugins\JQPlot\jqplot.categoryAxisRenderer.js" />
    <Content Include="Assets\Plugins\JQPlot\jqplot.pieRenderer.js" />
    <Content Include="Assets\Plugins\JQPlot\jqplot.pointLabels.js" />
    <Content Include="Assets\Plugins\JQPlot\jquery.jqplot.css" />
    <Content Include="Assets\Plugins\JQPlot\jquery.jqplot.js" />
    <Content Include="Assets\Plugins\JQueryUI\jquery-ui-1.11.3.js" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\images\ui-icons_444444_256x240.png" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\images\ui-icons_555555_256x240.png" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\images\ui-icons_777620_256x240.png" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\images\ui-icons_777777_256x240.png" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\images\ui-icons_cc0000_256x240.png" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\images\ui-icons_ffffff_256x240.png" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui-1.12.1.css" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui-1.12.1.js" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui.min.css" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui.min.js" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui.structure.css" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui.structure.min.css" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui.theme-1.12.1.css" />
    <Content Include="Assets\Plugins\JQueryUI\JqueryUI-1.12.1\jquery-ui.theme.min.css" />
    <None Include="Assets\Plugins\JQuery\jquery-2.1.3-vsdoc.js" />
    <None Include="Assets\Plugins\JQuery\jquery-2.1.3.intellisense.js" />
    <Content Include="Assets\Plugins\JQuery\jquery-2.1.3.js" />
    <Content Include="Assets\Plugins\JQuery\jquery-todictionary.js" />
    <Content Include="Assets\Plugins\JQuery\jquery.min.js" />
    <Content Include="Assets\Plugins\JQuery\numscroller-1.0.js" />
    <Content Include="Assets\Plugins\MonthPicker\examples.css" />
    <Content Include="Assets\Plugins\MonthPicker\examples.js" />
    <Content Include="Assets\Plugins\MonthPicker\images\bg_hr.png" />
    <Content Include="Assets\Plugins\MonthPicker\images\blacktocat.png" />
    <Content Include="Assets\Plugins\MonthPicker\images\icon.gif" />
    <Content Include="Assets\Plugins\MonthPicker\images\icon_download.png" />
    <Content Include="Assets\Plugins\MonthPicker\images\jquery-ui-month-picker-01.png" />
    <Content Include="Assets\Plugins\MonthPicker\images\sprite_download.png" />
    <Content Include="Assets\Plugins\MonthPicker\index.html" />
    <Content Include="Assets\Plugins\MonthPicker\MonthPicker.min.css" />
    <Content Include="Assets\Plugins\MonthPicker\MonthPicker.min.js" />
    <Content Include="Assets\Plugins\MonthPicker\stylesheets\github-light.css" />
    <Content Include="Assets\Plugins\MonthPicker\stylesheets\normalize.css" />
    <Content Include="Assets\Plugins\MonthPicker\stylesheets\stylesheet.css" />
    <Content Include="Assets\Plugins\SumoSelect\jquery.sumoselect.js" />
    <Content Include="Assets\Plugins\SumoSelect\sumoselect.css" />
    <Content Include="Assets\Plugins\SweetAlerts\sweet-alert.js" />
    <Content Include="Assets\Plugins\SweetAlerts\sweetalert-dev.js" />
    <Content Include="Assets\Plugins\SweetAlerts\sweetalert.css" />
    <Content Include="Assets\Plugins\SweetAlerts\sweetalert.js" />
    <Content Include="Assets\Plugins\TimePicker\bootstrap-timepicker.min.css" />
    <Content Include="Assets\Plugins\TimePicker\bootstrap-timepicker.min.js" />
    <Content Include="Assets\Plugins\TimePicker\jquery.timepicker.css" />
    <Content Include="Assets\Plugins\TimePicker\jquery.timepicker.js" />
    <Content Include="Assets\Themes\cupertino\images\ui-bg_flat_15_cd0a0a_40x100.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-bg_glass_100_e4f1fb_1x400.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-bg_glass_50_3baae3_1x400.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-bg_glass_80_d7ebf9_1x400.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-icons_2694e8_256x240.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-icons_2e83ff_256x240.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-icons_3d80b3_256x240.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-icons_72a7cf_256x240.png" />
    <Content Include="Assets\Themes\cupertino\images\ui-icons_ffffff_256x240.png" />
    <Content Include="Assets\Themes\cupertino\jquery-ui.css" />
    <Content Include="Assets\Themes\cupertino\jquery-ui.min.css" />
    <Content Include="Assets\Themes\cupertino\theme.css" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-bg_flat_75_ba9217_40x100.png" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-bg_flat_75_ffffff_40x100.png" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-icons_454545_256x240.png" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-icons_88a206_256x240.png" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-icons_c02669_256x240.png" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-icons_e1e463_256x240.png" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-icons_ffeb33_256x240.png" />
    <Content Include="Assets\Themes\hot-sneaks\images\ui-icons_ffffff_256x240.png" />
    <Content Include="Assets\Themes\hot-sneaks\jquery-ui.css" />
    <Content Include="Assets\Themes\hot-sneaks\jquery-ui.min.css" />
    <Content Include="Assets\Themes\hot-sneaks\theme.css" />
    <Content Include="bin\EntityFramework.dll" />
    <Content Include="bin\EntityFramework.SqlServer.dll" />
    <Content Include="bin\Filterandsortgriddata.dll" />
    <Content Include="bin\MICP_re.dll" />
    <Content Include="bin\MICP_re.pdb" />
    <Content Include="bin\Microsoft.Web.Infrastructure.dll" />
    <Content Include="bin\System.Net.Http.Formatting.dll" />
    <Content Include="bin\System.Net.Http.Formatting.xml" />
    <Content Include="bin\System.Web.Helpers.dll" />
    <Content Include="bin\System.Web.Helpers.xml" />
    <Content Include="bin\System.Web.Http.dll" />
    <Content Include="bin\System.Web.Http.WebHost.dll" />
    <Content Include="bin\System.Web.Http.WebHost.xml" />
    <Content Include="bin\System.Web.Http.xml" />
    <Content Include="bin\System.Web.Mvc.dll" />
    <Content Include="bin\System.Web.Mvc.xml" />
    <Content Include="bin\System.Web.Razor.dll" />
    <Content Include="bin\System.Web.Razor.xml" />
    <Content Include="bin\System.Web.WebPages.Deployment.dll" />
    <Content Include="bin\System.Web.WebPages.Deployment.xml" />
    <Content Include="bin\System.Web.WebPages.dll" />
    <Content Include="bin\System.Web.WebPages.Razor.dll" />
    <Content Include="bin\System.Web.WebPages.Razor.xml" />
    <Content Include="bin\System.Web.WebPages.xml" />
    <Content Include="Content\Amcharts plugin\amcharts.js" />
    <Content Include="Content\Amcharts plugin\angular-fusioncharts.js" />
    <Content Include="Content\Amcharts plugin\funnel.js" />
    <Content Include="Content\Amcharts plugin\fusioncharts.charts.js" />
    <Content Include="Content\Amcharts plugin\fusioncharts.js" />
    <Content Include="Content\Amcharts plugin\gantt.js" />
    <Content Include="Content\Amcharts plugin\gauge.js" />
    <Content Include="Content\Amcharts plugin\images\dragIcon.gif" />
    <Content Include="Content\Amcharts plugin\images\dragIconBlack.gif" />
    <Content Include="Content\Amcharts plugin\images\dragIconH.gif" />
    <Content Include="Content\Amcharts plugin\images\dragIconHBlack.gif" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBig.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBig.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBigBlack.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBigBlack.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBigBlackH.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBigBlackH.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBigH.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectBigH.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectSmall.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectSmall.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectSmallBlack.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectSmallBlack.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectSmallH.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRectSmallH.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBig.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBig.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBigBlack.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBigBlack.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBigBlackH.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBigBlackH.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBigH.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundBigH.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundSmall.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundSmall.svg" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundSmallH.png" />
    <Content Include="Content\Amcharts plugin\images\dragIconRoundSmallH.svg" />
    <Content Include="Content\Amcharts plugin\images\eraserIcon.svg" />
    <Content Include="Content\Amcharts plugin\images\eraserIconH.svg" />
    <Content Include="Content\Amcharts plugin\images\export.png" />
    <Content Include="Content\Amcharts plugin\images\exportWhite.png" />
    <Content Include="Content\Amcharts plugin\images\lens.png" />
    <Content Include="Content\Amcharts plugin\images\lens.svg" />
    <Content Include="Content\Amcharts plugin\images\lensWhite.png" />
    <Content Include="Content\Amcharts plugin\images\lensWhite.svg" />
    <Content Include="Content\Amcharts plugin\images\lensWhite_old.png" />
    <Content Include="Content\Amcharts plugin\images\lens_old.png" />
    <Content Include="Content\Amcharts plugin\images\pencilIcon.svg" />
    <Content Include="Content\Amcharts plugin\images\pencilIconH.svg" />
    <Content Include="Content\Amcharts plugin\images\xIcon.svg" />
    <Content Include="Content\Amcharts plugin\images\xIconH.svg" />
    <Content Include="Content\Amcharts plugin\lang\az.js" />
    <Content Include="Content\Amcharts plugin\lang\bg.js" />
    <Content Include="Content\Amcharts plugin\lang\cs.js" />
    <Content Include="Content\Amcharts plugin\lang\de.js" />
    <Content Include="Content\Amcharts plugin\lang\es.js" />
    <Content Include="Content\Amcharts plugin\lang\fi.js" />
    <Content Include="Content\Amcharts plugin\lang\fo.js" />
    <Content Include="Content\Amcharts plugin\lang\fr.js" />
    <Content Include="Content\Amcharts plugin\lang\hr.js" />
    <Content Include="Content\Amcharts plugin\lang\hu.js" />
    <Content Include="Content\Amcharts plugin\lang\id.js" />
    <Content Include="Content\Amcharts plugin\lang\is.js" />
    <Content Include="Content\Amcharts plugin\lang\it.js" />
    <Content Include="Content\Amcharts plugin\lang\ja.js" />
    <Content Include="Content\Amcharts plugin\lang\ko.js" />
    <Content Include="Content\Amcharts plugin\lang\lt.js" />
    <Content Include="Content\Amcharts plugin\lang\lv.js" />
    <Content Include="Content\Amcharts plugin\lang\mk.js" />
    <Content Include="Content\Amcharts plugin\lang\mn.js" />
    <Content Include="Content\Amcharts plugin\lang\mt.js" />
    <Content Include="Content\Amcharts plugin\lang\nl.js" />
    <Content Include="Content\Amcharts plugin\lang\no.js" />
    <Content Include="Content\Amcharts plugin\lang\pl.js" />
    <Content Include="Content\Amcharts plugin\lang\pt.js" />
    <Content Include="Content\Amcharts plugin\lang\ro.js" />
    <Content Include="Content\Amcharts plugin\lang\ru.js" />
    <Content Include="Content\Amcharts plugin\lang\rw.js" />
    <Content Include="Content\Amcharts plugin\lang\sk.js" />
    <Content Include="Content\Amcharts plugin\lang\sl.js" />
    <Content Include="Content\Amcharts plugin\lang\so.js" />
    <Content Include="Content\Amcharts plugin\lang\th.js" />
    <Content Include="Content\Amcharts plugin\lang\tr.js" />
    <Content Include="Content\Amcharts plugin\lang\zh.js" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern1.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern10.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern11.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern12.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern13.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern14.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern15.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern16.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern17.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern18.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern19.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern2.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern20.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern21.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern3.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern4.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern5.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern6.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern7.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern8.png" />
    <Content Include="Content\Amcharts plugin\patterns\black\pattern9.png" />
    <Content Include="Content\Amcharts plugin\patterns\chalk\pattern1.jpg" />
    <Content Include="Content\Amcharts plugin\patterns\chalk\pattern1r.jpg" />
    <Content Include="Content\Amcharts plugin\patterns\chalk\pattern2.jpg" />
    <Content Include="Content\Amcharts plugin\patterns\chalk\pattern3.jpg" />
    <Content Include="Content\Amcharts plugin\patterns\chalk\pattern4.jpg" />
    <Content Include="Content\Amcharts plugin\patterns\chalk\pattern5.jpg" />
    <Content Include="Content\Amcharts plugin\patterns\chalk\pattern6.jpg" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern1.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern10.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern11.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern12.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern13.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern14.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern15.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern16.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern17.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern18.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern19.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern2.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern20.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern21.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern3.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern4.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern5.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern6.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern7.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern8.png" />
    <Content Include="Content\Amcharts plugin\patterns\white\pattern9.png" />
    <Content Include="Content\Amcharts plugin\pie.js" />
    <Content Include="Content\Amcharts plugin\radar.js" />
    <Content Include="Content\Amcharts plugin\serial.js" />
    <Content Include="Content\Amcharts plugin\themes\black.js" />
    <Content Include="Content\Amcharts plugin\themes\chalk.js" />
    <Content Include="Content\Amcharts plugin\themes\dark.js" />
    <Content Include="Content\Amcharts plugin\themes\fusioncharts.theme.zune.js" />
    <Content Include="Content\Amcharts plugin\themes\light.js" />
    <Content Include="Content\Amcharts plugin\themes\patterns.js" />
    <Content Include="Content\Amcharts plugin\xy.js" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap.css" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap.min.css" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\multiselect.css" />
    <Content Include="Content\bootstrap-3.3.7-dist\js\bootstrap.js" />
    <Content Include="Content\bootstrap-3.3.7-dist\js\bootstrap.min.js" />
    <Content Include="Content\bootstrap-3.3.7-dist\js\multiselect.js" />
    <Content Include="Content\bootstrap-3.3.7-dist\js\npm.js" />
    <Content Include="Content\CSS\ace.min.css" />
    <Content Include="Content\CSS\Admin.css" />
    <Content Include="Content\CSS\ButtonStyle.css" />
    <Content Include="Content\CSS\Login.css" />
    <Content Include="Content\CSS\User.css" />
    <Content Include="Content\Custom\Utility.js" />
    <Content Include="Content\Image\account_and_control.png" />
    <Content Include="Content\Image\Admin-icon.png" />
    <Content Include="Content\Image\AMC.png" />
    <Content Include="Content\Image\Archd.png" />
    <Content Include="Content\Image\archive.PNG" />
    <Content Include="Content\Image\archivee.png" />
    <Content Include="Content\Image\archive_logo.jpg" />
    <Content Include="Content\Image\AttchmntSymbol.png" />
    <Content Include="Content\Image\Cancel-icon.png" />
    <Content Include="Content\Image\Carousel1.2.png" />
    <Content Include="Content\Image\Carousel1.3.png" />
    <Content Include="Content\Image\DeliveredPO.png" />
    <Content Include="Content\Image\DetailsIcon.png" />
    <Content Include="Content\Image\DetailsIconOld.png" />
    <Content Include="Content\Image\DispatchedPO.png" />
    <Content Include="Content\Image\Document.PNG" />
    <Content Include="Content\Image\DocumentsIcon.png" />
    <Content Include="Content\Image\DownloadExcelTemplate.jpg" />
    <Content Include="Content\Image\DownloadTemplate.png" />
    <Content Include="Content\Image\EditIcon.ico" />
    <Content Include="Content\Image\EEPsCONs.png" />
    <Content Include="Content\Image\Employee.png" />
    <Content Include="Content\Image\Enquiry.png" />
    <Content Include="Content\Image\ExcelIcon.PNG" />
    <Content Include="Content\Image\exit2.png" />
    <Content Include="Content\Image\favicon.ico" />
    <Content Include="Content\Image\Feedback.png" />
    <Content Include="Content\Image\file_edit.png" />
    <Content Include="Content\Image\finance.png" />
    <Content Include="Content\Image\free-vector-head-logo_091374_Head_logo.png" />
    <Content Include="Content\Image\HeaderBackground.png" />
    <Content Include="Content\Image\Headerimgs.png" />
    <Content Include="Content\Image\Headersimg.png" />
    <Content Include="Content\Image\HR-dEPT.png" />
    <Content Include="Content\Image\HR.png" />
    <Content Include="Content\Image\HRIcon2.png" />
    <Content Include="Content\Image\HRImg.png" />
    <Content Include="Content\Image\human-resources-icon.png" />
    <Content Include="Content\Image\icon.gif" />
    <Content Include="Content\Image\iEngrenages.png" />
    <Content Include="Content\Image\ImgLeftArrow.png" />
    <Content Include="Content\Image\IT Dept.gif" />
    <Content Include="Content\Image\it-logo-1.png" />
    <Content Include="Content\Image\it.png" />
    <Content Include="Content\Image\itdept.png" />
    <Content Include="Content\Image\itsq.jpg" />
    <Content Include="Content\Image\LimitedPeriodOffers.png" />
    <Content Include="Content\Image\LoadingImg.gif" />
    <Content Include="Content\Image\loadingimg3.gif" />
    <Content Include="Content\Image\LoadingImgAMS.gif" />
    <Content Include="Content\Image\Loading_icon.gif" />
    <Content Include="Content\Image\Loading_icon.gif.gif" />
    <Content Include="Content\Image\LoginImg.png" />
    <Content Include="Content\Image\Logo.png" />
    <Content Include="Content\Image\Logo4.PNG" />
    <Content Include="Content\Image\logout-512.png" />
    <Content Include="Content\Image\logout.png" />
    <Content Include="Content\Image\mesto.PNG" />
    <Content Include="Content\Image\mesto1.png" />
    <Content Include="Content\Image\mesto1_new.png" />
    <Content Include="Content\Image\mesto1_newa.png" />
    <Content Include="Content\Image\mesto_new.png" />
    <Content Include="Content\Image\mesto_newwww.png" />
    <Content Include="Content\Image\MetsoBG.jpg" />
    <Content Include="Content\Image\MetsoBG2.jpg" />
    <Content Include="Content\Image\Metsocontinue.png" />
    <Content Include="Content\Image\Metso_LogInPage_Image.jpg" />
    <Content Include="Content\Image\MICP_Img.jpg" />
    <Content Include="Content\Image\Microsoft-Icon.PNG" />
    <Content Include="Content\Image\miuns3.jpg" />
    <Content Include="Content\Image\miusicon1.jpg" />
    <Content Include="Content\Image\miusicon2.jpg" />
    <Content Include="Content\Image\nextIcon.png" />
    <Content Include="Content\Image\Payment.png" />
    <Content Include="Content\Image\plusicon1.jpg" />
    <Content Include="Content\Image\ProceedImage.jpg" />
    <Content Include="Content\Image\PromotionsAndOffers.png" />
    <Content Include="Content\Image\PromotionsAndOffersArrow.png" />
    <Content Include="Content\Image\PurchaseOrder.png" />
    <Content Include="Content\Image\RedAdv.png" />
    <Content Include="Content\Image\Remainder.png" />
    <Content Include="Content\Image\Reports.png" />
    <Content Include="Content\Image\Report_R.png" />
    <Content Include="Content\Image\rightarrow.png" />
    <Content Include="Content\Image\rsz_sampleFilter.png" />
    <Content Include="Content\Image\search.png" />
    <Content Include="Content\Image\Shipment %282%29.png" />
    <Content Include="Content\Image\Shipment.png" />
    <Content Include="Content\Image\tata-background1.jpg" />
    <Content Include="Content\Image\th.jpg" />
    <Content Include="Content\Image\Update.png" />
    <Content Include="Content\Image\user.png" />
    <Content Include="Content\Image\ViewImage.png" />
    <Content Include="Content\Image\View_1.png" />
    <Content Include="Content\Image\YellowAdv.png" />
    <Content Include="Content\Plugin\3dcarousel.js" />
    <Content Include="Content\Plugin\Amcharts plugin\amcharts.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\ammap.css" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\ammap.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\ammap_amcharts_extension.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\arrowDown.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\arrowUp.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\export.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\homeIcon.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\homeIconWhite.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\minus.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\panDown.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\panLeft.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\panRight.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\panUp.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\plus.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\xIcon.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\xIcon.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\xIconH.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\images\xIconH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\af.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\am.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ar.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\as.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\az.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\be.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\bg.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\bn.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\bo.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\bs.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\byn.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ca.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\cs.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\cy.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\da.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\de.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\dv.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\dz.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\el.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\eo.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\es.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\et.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\eu.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\fa.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\fi.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\fil.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\fo.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\fr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\fur.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ga.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\gez.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\gl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\gsw.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\gu.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\gv.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ha.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\haw.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\he.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\hi.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\hr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\hu.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\hy.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ia.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\id.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ii.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\is.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\it.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ja.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ka.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\kk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\kl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\km.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\kn.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ko.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\kok.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ku.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\kw.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ky.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ln.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\lo.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\lt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\lv.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\mk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ml.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\mn.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\mr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ms.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\mt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\my.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\nb.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\nds.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ne.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\nl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\nn.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\oc.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\om.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\or.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\pa.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\pl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ps.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\pt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ro.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ru.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\rw.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sa.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\se.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\si.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sid.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\so.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sq.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sv.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\sw.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\syr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ta.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\te.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\tg.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\th.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\tig.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\to.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\tr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\trv.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\tt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\uk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\ur.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\uz.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\vi.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\wal.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\yo.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\zh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\lang\zu.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\light.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\afghanistanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\afghanistanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\albaniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\albaniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\algeriaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\algeriaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\americanSamoaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\andorraHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\andorraLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\angolaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\angolaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\argentinaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\argentinaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\armeniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\armeniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\arubaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\arubaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\australiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\australiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\austriaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\austriaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\azerbaijanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\azerbaijanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bahamasHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bahamasLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bahrainHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bahrainLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bangladeshHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bangladeshLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\belarusHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\belarusLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\belgiumHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\belgiumLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\belizeHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\belizeLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\beninHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\beninLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bermudaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bhutanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bhutanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\boliviaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\boliviaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\botswanaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\botswanaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\brazilHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\brazilLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bulgariaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\bulgariaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\burkinaFasoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\burkinaFasoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\burundiHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\burundiLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cambodiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cambodiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cameroonHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cameroonLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\canadaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\canadaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\capeVerdeHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\capeVerdeLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\caymanIslandsLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\chadHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\chadLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\chileHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\chileLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\chinaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\chinaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cocosIslandsHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cocosIslandsLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\colombiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\colombiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\comorosHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\comorosLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\congoDRHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\congoDRLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\congoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\congoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\continentsHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\continentsLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cookIslandsHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cookIslandsLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\costaRicaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\costaRicaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\croatiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\croatiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cubaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cubaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\curacaoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\curacaoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cyprusHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\cyprusLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\czechRepublicLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\denmarkHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\denmarkLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\djiboutiHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\djiboutiLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\dominicaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\dominicaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\eastTimorHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\eastTimorLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ecuadorHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ecuadorLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\egyptHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\egyptLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\elSalvadorHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\elSalvadorLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\eritreaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\eritreaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\estoniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\estoniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ethiopiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ethiopiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\faroeIslandsHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\fijiEastHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\fijiEastLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\fijiWestHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\fijiWestLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\finlandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\finlandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\france2016High.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\france2016Low.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\franceHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\franceLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\frenchGuianaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\frenchGuianaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\gabonHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\gabonLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\gambiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\gambiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\georgiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\georgiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\germanyHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\germanyLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ghanaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ghanaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\gibraltarHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\gibraltarLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\greeceHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\greeceLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\greenlandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\greenlandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guadeloupeHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guadeloupeLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guamHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guamLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guatemalaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guatemalaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guineaBissauHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guineaBissauLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guineaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guineaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guyanaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\guyanaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\haitiHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\haitiLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\hondurasHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\hondurasLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\hongKongHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\hongKongLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\hungaryHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\hungaryLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\icelandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\icelandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\indiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\indiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\indonesiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\indonesiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\iranHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\iranLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\iraqHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\iraqLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\irelandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\irelandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\isleOfManHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\isleOfManLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\israelHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\israelLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\italyHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\italyLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ivoryCoastHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ivoryCoastLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\jamaicaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\jamaicaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\japanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\japanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\jerseyHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\jerseyLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\jordanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\jordanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kazakhstanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kazakhstanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kenyaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kenyaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kosovoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kosovoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kuwaitHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kuwaitLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kyrgyzstanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kyrgyzstanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\laosHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\laosLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\latviaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\latviaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\lebanonHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\lebanonLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\lesothoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\lesothoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\liberiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\liberiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\libyaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\libyaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\lithuaniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\lithuaniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\luxembourgHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\luxembourgLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\macaoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\macaoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\macedoniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\macedoniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\madagascarHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\madagascarLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\malawiHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\malawiLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\malaysiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\malaysiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\maldivesHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\maldivesLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\maliHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\maliLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\maltaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\martiniqueHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\martiniqueLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mauritaniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mauritaniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mauritiusHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mauritiusLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mayotteHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mayotteLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mexicoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mexicoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\micronesiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\micronesiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\moldovaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\moldovaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\monacoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\monacoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mongoliaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mongoliaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\montenegroHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\montenegroLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\montserratLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\moroccoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\moroccoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mozambiqueHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\mozambiqueLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\myanmarHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\myanmarLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\namibiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\namibiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nauruHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nauruLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nepalHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nepalLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\netherlandsHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\netherlandsLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\newCaledoniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\newCaledoniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\newZealandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\newZealandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nicaraguaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nicaraguaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nigerHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nigeriaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nigeriaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\nigerLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\niueHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\niueLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\northKoreaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\northKoreaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\norwayHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\norwayLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\omanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\omanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\pakistanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\pakistanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\palauHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\palauLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\palestineHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\palestineLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\panamaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\panamaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\paraguayHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\paraguayLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\peruHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\peruLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\philippinesHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\philippinesLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\polandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\polandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\portugalHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\portugalLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\puertoRicoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\puertoRicoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\qatarHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\qatarLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\reunionHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\reunionLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\romaniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\romaniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\russiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\russiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\rwandaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\rwandaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\samoaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sanMarinoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sanMarinoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\saudiArabiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\saudiArabiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\senegalHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\senegalLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\serbiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\serbiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\seychellesHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\seychellesLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sierraLeoneHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sierraLeoneLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\singaporeHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\singaporeLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sintMaartenHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sintMaartenLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\slovakiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\slovakiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sloveniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sloveniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\somaliaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\somaliaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\southAfricaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\southAfricaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\southKoreaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\southKoreaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\southSudanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\southSudanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\spain2High.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\spain2Low.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\spainHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\spainLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sriLankaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sriLankaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\stHelenaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\stHelenaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\stKittsNevisHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\stKittsNevisLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\stLuciaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\stLuciaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\stVincentHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sudanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\sudanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\surinameHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\surinameLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\swazilandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\swazilandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\swedenHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\swedenLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\switzerlandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\switzerlandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\syriaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\syriaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\taiwanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\taiwanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tajikistanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tajikistanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tanzaniaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tanzaniaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\thailandHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\thailandLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tibetHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tibetLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\togoHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\togoLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tokelauHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tokelauLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tongaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tongaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tunisiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tunisiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\turkeyHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\turkeyLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\turkmenistanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\turkmenistanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tuvaluHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\tuvaluLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ugandaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ugandaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ukraineHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\ukraineLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\unitedKingdomLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\uruguayHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\uruguayLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\usa2High.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\usa2Low.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\usaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\usaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\usaMercatorHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\usaMercatorLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\uzbekistanHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\uzbekistanLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\vanuatuHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\vanuatuLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\vaticanCity.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\venezuelaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\venezuelaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\vietnamHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\vietnamLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\wallisFutunaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\wallisFutunaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\worldHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\worldIndiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\worldIndiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\worldKashmirHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\worldKashmirLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\worldLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\yemenHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\yemenLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\zambiaHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\zambiaLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\zimbabweHigh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\zimbabweLow.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\afghanistanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\albaniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\albaniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\algeriaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\algeriaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\andorraHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\andorraLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\angolaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\angolaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\argentinaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\argentinaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\armeniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\armeniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\arubaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\arubaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\australiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\australiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\austriaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\austriaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\azerbaijanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\azerbaijanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bahamasHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bahamasLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bahrainHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bahrainLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bangladeshHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bangladeshLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\belarusHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\belarusLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\belgiumHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\belgiumLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\belizeHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\belizeLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\beninHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\beninLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bermudaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bhutanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bhutanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\boliviaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\boliviaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\botswanaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\botswanaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\brazilHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\brazilLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bulgariaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\bulgariaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\burkinaFasoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\burundiHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\burundiLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cambodiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cambodiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cameroonHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cameroonLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\canadaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\canadaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\capeVerdeHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\capeVerdeLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\chadHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\chadLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\chileHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\chileLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\chinaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\chinaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\colombiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\colombiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\comorosHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\comorosLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\congoDRHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\congoDRLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\congoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\congoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\continentsHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\continentsLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cookIslandsLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\costaRicaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\costaRicaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\croatiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\croatiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cubaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cubaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\curacaoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\curacaoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cyprusHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\cyprusLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\denmarkHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\denmarkLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\djiboutiHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\djiboutiLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\dominicaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\dominicaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\eastTimorHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\eastTimorLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ecuadorHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ecuadorLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\egyptHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\egyptLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\elSalvadorHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\elSalvadorLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\eritreaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\eritreaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\estoniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\estoniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ethiopiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ethiopiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\fijiEastHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\fijiEastLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\fijiWestHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\fijiWestLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\finlandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\finlandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\france2016High.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\france2016Low.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\franceHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\franceLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\gabonHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\gabonLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\gambiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\gambiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\georgiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\georgiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\germanyHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\germanyLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ghanaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ghanaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\gibraltarHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\gibraltarLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\greeceHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\greeceLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\greenlandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\greenlandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guadeloupeHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guadeloupeLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guamHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guamLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guatemalaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guatemalaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guineaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guineaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guyanaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\guyanaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\haitiHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\haitiLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\hondurasHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\hondurasLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\hongKongHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\hongKongLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\hungaryHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\hungaryLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\icelandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\icelandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\indiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\indiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\indonesiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\indonesiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\iranHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\iranLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\iraqHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\iraqLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\irelandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\irelandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\isleOfManHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\isleOfManLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\israelHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\israelLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\italyHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\italyLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ivoryCoastHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ivoryCoastLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\jamaicaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\jamaicaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\japanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\japanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\jerseyHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\jerseyLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\jordanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\jordanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kazakhstanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kazakhstanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kenyaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kenyaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kiribatiHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kiribatiLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kosovoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kosovoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kuwaitHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kuwaitLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kyrgyzstanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\kyrgyzstanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\laosHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\laosLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\latviaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\latviaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\lebanonHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\lebanonLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\lesothoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\lesothoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\liberiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\liberiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\libyaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\libyaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\lithuaniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\lithuaniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\luxembourgHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\luxembourgLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\macaoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\macaoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\macedoniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\macedoniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\madagascarHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\madagascarLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\malawiHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\malawiLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\malaysiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\malaysiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\maldivesHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\maldivesLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\maliHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\maliLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\maltaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\martiniqueHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\martiniqueLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mauritaniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mauritaniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mauritiusHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mauritiusLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mayotteHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mayotteLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mexicoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mexicoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\micronesiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\micronesiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\moldovaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\moldovaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\monacoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\monacoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mongoliaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mongoliaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\montenegroHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\montenegroLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\montserratHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\montserratLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\moroccoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\moroccoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mozambiqueHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\mozambiqueLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\myanmarHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\myanmarLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\namibiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\namibiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nauruHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nauruLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nepalHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nepalLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\netherlandsLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\newZealandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\newZealandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nicaraguaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nicaraguaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nigerHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nigeriaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nigeriaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\nigerLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\niueHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\niueLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\northKoreaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\northKoreaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\norwayHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\norwayLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\omanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\omanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\pakistanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\pakistanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\palauHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\palauLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\palestineHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\palestineLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\panamaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\panamaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\paraguayHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\paraguayLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\peruHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\peruLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\philippinesLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\polandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\polandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\portugalHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\portugalLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\puertoRicoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\puertoRicoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\qatarHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\qatarLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\reunionHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\reunionLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\romaniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\romaniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\russiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\russiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\rwandaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\rwandaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\samoaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sanMarinoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sanMarinoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\saudiArabiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\senegalHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\senegalLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\serbiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\serbiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\seychellesHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\seychellesLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sierraLeoneLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\singaporeHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\singaporeLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sintMaartenLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\slovakiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\slovakiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sloveniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sloveniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\somaliaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\somaliaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\southAfricaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\southKoreaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\southKoreaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\southSudanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\southSudanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\spain2High.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\spain2Low.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\spainHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\spainLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sriLankaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sriLankaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\stHelenaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\stHelenaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\stLuciaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\stLuciaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\stVincentHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sudanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\sudanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\surinameHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\surinameLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\swazilandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\swazilandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\swedenHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\swedenLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\switzerlandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\syriaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\syriaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\taiwanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\taiwanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tajikistanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tajikistanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tanzaniaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tanzaniaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\thailandHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\thailandLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tibetHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tibetLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\togoHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\togoLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tokelauHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tokelauLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tongaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tongaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tunisiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tunisiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\turkeyHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\turkeyLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tuvaluHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\tuvaluLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ugandaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ugandaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ukraineHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\ukraineLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\uruguayHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\uruguayLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\usa2High.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\usa2Low.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\usaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\usaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\usaMercatorLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\uzbekistanHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\uzbekistanLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\vanuatuHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\vanuatuLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\vaticanCity.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\venezuelaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\venezuelaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\vietnamHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\vietnamLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\worldHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\worldIndiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\worldIndiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\worldLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\yemenHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\yemenLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\zambiaHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\zambiaLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\zimbabweHigh.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\svg\zimbabweLow.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern1.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern2.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern3.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern4.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern5.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern6.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern7.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern8.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\black\pattern9.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\chalk\pattern1.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\chalk\pattern2.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\chalk\pattern3.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\chalk\pattern4.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\chalk\pattern5.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\chalk\pattern6.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern1.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern2.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern3.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern4.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern5.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern6.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern7.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern8.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\patterns\white\pattern9.png" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\.gitignore" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\export.css" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\export.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\index.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\themes\black.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\themes\chalk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\themes\dark.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\themes\light.js" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\themes\patterns.js" />
    <Content Include="Content\Plugin\Amcharts plugin\angular-fusioncharts.js" />
    <Content Include="Content\Plugin\Amcharts plugin\funnel.js" />
    <Content Include="Content\Plugin\Amcharts plugin\fusioncharts.charts.js" />
    <Content Include="Content\Plugin\Amcharts plugin\fusioncharts.js" />
    <Content Include="Content\Plugin\Amcharts plugin\gantt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\gauge.js" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIcon.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconBlack.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconH.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconHBlack.gif" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBig.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBig.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBigBlack.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBigBlack.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBigBlackH.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBigBlackH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBigH.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectBigH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectSmall.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectSmall.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectSmallBlack.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectSmallBlack.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectSmallH.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRectSmallH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBig.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBig.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBigBlack.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBigBlack.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBigBlackH.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBigBlackH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBigH.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundBigH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundSmall.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundSmall.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundSmallH.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\dragIconRoundSmallH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\eraserIcon.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\eraserIconH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\export.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\exportWhite.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\lens.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\lens.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\lensWhite.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\lensWhite.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\lensWhite_old.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\lens_old.png" />
    <Content Include="Content\Plugin\Amcharts plugin\images\pencilIcon.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\pencilIconH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\xIcon.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\images\xIconH.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\az.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\bg.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\cs.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\de.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\es.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\fi.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\fo.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\fr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\hr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\hu.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\id.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\is.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\it.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\ja.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\ko.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\lt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\lv.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\mk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\mn.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\mt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\nl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\no.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\pl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\pt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\ro.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\ru.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\rw.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\sk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\sl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\so.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\th.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\tr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\lang\zh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern1.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern10.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern11.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern12.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern13.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern14.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern15.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern16.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern17.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern18.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern19.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern2.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern20.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern21.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern3.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern4.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern5.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern6.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern7.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern8.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\black\pattern9.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\chalk\pattern1.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\chalk\pattern1r.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\chalk\pattern2.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\chalk\pattern3.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\chalk\pattern4.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\chalk\pattern5.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\chalk\pattern6.jpg" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern1.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern10.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern11.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern12.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern13.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern14.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern15.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern16.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern17.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern18.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern19.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern2.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern20.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern21.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern3.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern4.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern5.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern6.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern7.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern8.png" />
    <Content Include="Content\Plugin\Amcharts plugin\patterns\white\pattern9.png" />
    <Content Include="Content\Plugin\Amcharts plugin\pie.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\animate\animate.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\animate\animate.min.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\animate\index.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\.gitignore" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\export.css" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\export.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\export.min.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\index.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\cs.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\de.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\en.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\es.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\fr.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\hu.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\it.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\ko.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\lt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\pl.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\pt.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\lang\zh.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\1.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\10.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\11.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\12.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\13.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\14.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\15.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\16.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\17.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\18.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\19.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\2.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\20.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\21.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\22.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\23.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\24.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\25.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\26.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\27.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\28.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\29.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\3.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\30.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\31.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\4.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\5.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\6.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\7.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\8.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\shapes\9.svg" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\responsive\index.js" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\responsive\license.txt" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\responsive\responsive.js" />
    <Content Include="Content\Plugin\Amcharts plugin\radar.js" />
    <Content Include="Content\Plugin\Amcharts plugin\serial.js" />
    <Content Include="Content\Plugin\Amcharts plugin\themes\black.js" />
    <Content Include="Content\Plugin\Amcharts plugin\themes\chalk.js" />
    <Content Include="Content\Plugin\Amcharts plugin\themes\dark.js" />
    <Content Include="Content\Plugin\Amcharts plugin\themes\fusioncharts.theme.zune.js" />
    <Content Include="Content\Plugin\Amcharts plugin\themes\light.js" />
    <Content Include="Content\Plugin\Amcharts plugin\themes\patterns.js" />
    <Content Include="Content\Plugin\Amcharts plugin\xy.js" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap-theme.css" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap-theme.min.css" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap.css" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap.min.css" />
    <Content Include="Content\Plugin\Bootstrap\dist\fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Content\Plugin\Bootstrap\dist\js\bootstrap.js" />
    <Content Include="Content\Plugin\Bootstrap\dist\js\bootstrap.min.js" />
    <Content Include="Content\Plugin\Bootstrap\dist\js\npm.js" />
    <Content Include="Content\Plugin\Bootstrap\docs\about.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\apple-touch-icon.png" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\brand\bootstrap-outline.svg" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\brand\bootstrap-punchout.svg" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\brand\bootstrap-solid.svg" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\css\docs.min.css" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\css\ie10-viewport-bug-workaround.css" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\css\src\docs.css" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\css\src\pygments-manni.css" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\flash\ZeroClipboard.swf" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\img\components.png" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\img\devices.png" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\img\expo-lyft.jpg" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\img\expo-newsweek.jpg" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\img\expo-riot.jpg" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\img\expo-vogue.jpg" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\img\sass-less.png" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\js\customize.min.js" />
    <Content Include="Content\Plugin\Bootstrap\docs\browser-bugs.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\components.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\css.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\customize.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\favicon.ico" />
    <Content Include="Content\Plugin\Bootstrap\docs\getting-started.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\index.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\javascript.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\migration.html" />
    <Content Include="Content\Plugin\Bootstrap\docs\robots.txt" />
    <Content Include="Content\Plugin\Bootstrap\docs\sitemap.xml" />
    <Content Include="Content\Plugin\jquery-3.2.1.min.js" />
    <Content Include="ErrorLog\ErrorLog.txt" />
    <Content Include="Global.asax" />
    <Content Include="Models\Metso_MICP.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>Metso_MICP.edmx</DependentUpon>
      <LastGenOutput>Metso_MICP.Context.cs</LastGenOutput>
    </Content>
    <Content Include="Models\Metso_MICP.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>Metso_MICP.edmx</DependentUpon>
      <LastGenOutput>Metso_MICP.cs</LastGenOutput>
    </Content>
    <Content Include="UploadedFiles\Barmac B7150-DD , Station.png" />
    <Content Include="UploadedFiles\Barmac B7150-DD.png" />
    <Content Include="UploadedFiles\C 106-7.png" />
    <Content Include="UploadedFiles\C106.png" />
    <Content Include="UploadedFiles\Crusher Base Level.png" />
    <Content Include="UploadedFiles\CVB2060-4D.png" />
    <Content Include="UploadedFiles\DS1855-4D.png" />
    <Content Include="UploadedFiles\GP220 Cone crusher.png" />
    <Content Include="UploadedFiles\GP220.png" />
    <Content Include="UploadedFiles\GP220_1.png" />
    <Content Include="UploadedFiles\GP220_2.png" />
    <Content Include="UploadedFiles\Jaw Crusher C 106.png" />
    <Content Include="UploadedFiles\NW 106.JPG" />
    <Content Include="UploadedFiles\NW106-001.png" />
    <Content Include="UploadedFiles\NW106-002.png" />
    <Content Include="UploadedFiles\NW106.png" />
    <Content Include="UploadedFiles\NW220-001.png" />
    <Content Include="UploadedFiles\NW220.png" />
    <Content Include="UploadedFiles\NW7150-001.png" />
    <Content Include="UploadedFiles\NW7150.png" />
    <Content Include="UploadedFiles\PIC-1.JPG" />
    <Content Include="UploadedFiles\ScreenTS43.png" />
    <Content Include="UploadedFiles\suportStructure.PNG" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\Projects.cshtml" />
    <Content Include="Views\Index.cshtml" />
    <Content Include="Views\CheckSheets.cshtml" />
    <Content Include="Views\HydraAvailability.cshtml" />
    <Content Include="Views\IdleReport.cshtml" />
    <Content Include="Views\Commissioning.cshtml" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap-theme.css.map" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap-theme.min.css.map" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap.css.map" />
    <Content Include="Content\bootstrap-3.3.7-dist\css\bootstrap.min.css.map" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kiribatiHigh" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\kiribatiLow" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\maps\js\montserratHigh" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\bower.json" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\LICENSE" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\package.json" />
    <Content Include="Content\Plugin\Amcharts plugin\ammap\plugins\export\README.md" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\animate\bower.json" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\animate\LICENSE" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\animate\package.json" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\animate\README.md" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\bower.json" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\LICENSE" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\package.json" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\export\README.md" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\responsive\bower.json" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\responsive\package.json" />
    <Content Include="Content\Plugin\Amcharts plugin\plugins\responsive\readme.md" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap-theme.css.map" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap-theme.min.css.map" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap.css.map" />
    <Content Include="Content\Plugin\Bootstrap\dist\css\bootstrap.min.css.map" />
    <Content Include="Content\Plugin\Bootstrap\dist\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\Plugin\Bootstrap\dist\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Content\Plugin\Bootstrap\dist\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Content\Plugin\Bootstrap\dist\fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="Content\Plugin\Bootstrap\docs\assets\css\docs.min.css.map" />
    <Content Include="Content\Plugin\Bootstrap\docs\LICENSE" />
    <EntityDeploy Include="Models\Metso_MICP.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>Metso_MICP.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <Content Include="Models\Metso_MICP.edmx.diagram">
      <DependentUpon>Metso_MICP.edmx</DependentUpon>
    </Content>
    <Content Include="Views\InstallationProtocol.cshtml" />
    <Content Include="Views\Settings.cshtml" />
    <Content Include="Views\Login.cshtml" />
    <Content Include="Views\reports.cshtml" />
    <Content Include="Views\Archive.cshtml" />
    <Content Include="Views\SessionPage.cshtml" />
    <Content Include="Views\ProtocolRejection.cshtml">
      <SubType>Code</SubType>
    </Content>
    <Content Include="Views\ErrorPage.cshtml" />
    <None Include="Properties\PublishProfiles\MetsoMICS.pubxml" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Views\_LayoutPage.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Assets\Fonts\font-awesome-4.7.0\fonts\fontawesome-webfont.eot" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\fonts\fontawesome-webfont.ttf" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\fonts\fontawesome-webfont.woff" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\fonts\fontawesome-webfont.woff2" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\fonts\FontAwesome.otf" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\animated.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\bordered-pulled.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\core.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\fixed-width.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\font-awesome.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\icons.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\larger.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\list.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\mixins.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\path.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\rotated-flipped.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\screen-reader.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\stacked.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\less\variables.less" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\font-awesome.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_animated.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_bordered-pulled.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_core.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_fixed-width.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_icons.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_larger.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_list.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_mixins.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_path.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_rotated-flipped.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_screen-reader.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_stacked.scss" />
    <Content Include="Assets\Fonts\font-awesome-4.7.0\scss\_variables.scss" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\pdfmake\pdfmake.min.js.map" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\xlsx\LICENSE" />
    <Content Include="Assets\Plugins\AmCharts\export\libs\xlsx\xlsx.min.map" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap-theme.css.map" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap-theme.min.css.map" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap.css.map" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\css\bootstrap.min.css.map" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\fonts\glyphicons-halflings-regular.woff" />
    <Content Include="Assets\Plugins\Bootstrap\bootstrap-3.3.6-dist\fonts\glyphicons-halflings-regular.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:55273/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>