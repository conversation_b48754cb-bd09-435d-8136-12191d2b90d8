/*
 FusionCharts JavaScript Library
 Copyright FusionCharts Technologies LLP
 License Information at <http://www.fusioncharts.com/license>
 FusionCharts JavaScript Library
 Copyright FusionCharts Technologies LLP
 License Information at <http://www.fusioncharts.com/license>

 @version 3.11.0
*/
FusionCharts.register("module",["private","modules.renderer.js-charts",function(){function ya(c,D){for(var h=[],a=0,p=c.length;a<p;a++)h[a]=D.call(c[a],c[a],a,c);return h}function O(c,D){var h=D?360:l;c=(c||0)%h;return 0>c?h+c:c}function Ja(c,D,h,a,p){return Oa((D-h[1]-a.top)/p,c-h[0]-a.left)}function fa(c){this.config={};this.linkedItems={chart:c}}var ea=this,U=ea.hcLib,Pa=U.hasTouch,Da=ea.window,la=Da.document,na=U.Raphael,Ga=U.BLANKSTRING,R=U.pluck,x=U.pluckNumber,Sa=U.getFirstValue,Ia=U.extend2,
ka=U.toRaphaelColor,za=U.hasSVG,Ta=U.hashify,oa="createTouch"in la,Ea=oa&&!(Da.navigator.maxTouchPoints||Da.navigator.msMaxTouchPoints),Ca=U.each,Ka=U.plotEventHandler,Ha=U.componentDispose,Aa=8===Da.document.documentMode?"visible":"",ma=Math,qa=ma.sin,ra=ma.cos,Oa=ma.atan2,ja=ma.round,sa=ma.min,ua=ma.max,va=ma.abs,wa=ma.PI,xa=ma.ceil,pa=ma.floor,a=wa/180,b=180/wa,d=Math.PI,k=d/2,l=2*d,m=d+k,f=U.getFirstColor,B=U.getFirstAlpha,q=U.graphics.getDarkColor,v=U.graphics.getLightColor,r=U.graphics.convertColor,
S=U.POSITION_BOTTOM,P=U.POSITION_RIGHT,e=U.chartAPI,n=U.COMMASTRING,H=U.ZEROSTRING,ba=U.ONESTRING,L={},N={};e("column2d",{standaloneInit:!0,friendlyName:"Column Chart",creditLabel:!1,defaultDatasetType:"column",applicableDSList:{column:!0},singleseries:!0},e.sscartesian);e("column3d",{friendlyName:"3D Column Chart",defaultDatasetType:"column3d",applicableDSList:{column3d:!0},defaultPlotShadow:1,creditLabel:!1,is3D:!0,standaloneInit:!0,hasLegend:!1,singleseries:!0,fireGroupEvent:!0,defaultZeroPlaneHighlighted:!1},
e.sscartesian3d);e("bar2d",{friendlyName:"Bar Chart",isBar:!0,standaloneInit:!0,defaultDatasetType:"bar2d",creditLabel:!1,applicableDSList:{bar2d:!0},singleseries:!0,spaceManager:e.barbase},e.ssbarcartesian);e("bar3d",{friendlyName:"3D Bar Chart",defaultDatasetType:"bar3d",applicableDSList:{bar3d:!0},defaultPlotShadow:1,fireGroupEvent:!0,standaloneInit:!0,creditLabel:!1,is3D:!0,isBar:!0,singleseries:!0,defaultZeroPlaneHighlighted:!1},e.ssbarcartesian3d);e("line",{friendlyName:"Line Chart",standaloneInit:!0,
creditLabel:!1,defaultPlotShadow:1,singleseries:!0,axisPaddingLeft:0,axisPaddingRight:0,defaultDatasetType:"line"},e.sscartesian,{zeroplanethickness:1,zeroplanealpha:40,showzeroplaneontop:0});e("area2d",{friendlyName:"Area Chart",standaloneInit:!0,creditLabel:!1,defaultDatasetType:"area",singleseries:!0,defaultPlotShadow:0},e.sscartesian);e("pareto2d",{defaultDatasetType:"column2d",singleseries:!0,creditLabel:!1,_createDatasets:function(){var c=this.components,D=this.jsonData,h=this.is3D,a=c.numberFormatter,
p=D.data||D.dataset&&D.dataset[0]&&D.dataset[0].data,g=p&&p.length,K=D.chart,b=this.defaultDatasetType,D=new (FusionCharts.get("component",["dataset","Pareto"])),d=x(K.showcumulativeline,1),J=[],s,u;if(p){for(K=0;K<g;K++)s=p[K],u=a.getCleanValue(s.value),null!==u&&"true"!==s.vline&&!0!==s.vline&&1!==s.vline&&"1"!==s.vline&&J.push(s);this.config.categories=J;a=c.dataset||(c.dataset=[]);(p=R(b))&&p.toLowerCase();p=FusionCharts.register("component",["datasetGroup","column"]);p=c[void 0]=new p;p.chart=
this;p.init();if(g=h?FusionCharts.get("component",["dataset","Column3d"]):FusionCharts.get("component",["dataset","Column"]))(h=a[0])?(b=J.length,p=h.components.data.length,b<p&&h.removeData(b,p-b),h.JSONData={data:J},D.configure.call(h)):(h=new g,a.push(h),h.chart=this,h.index=K,p&&p.addDataSet(h,0,0),D.init(h,J,b));c=c.yAxis[1];if(d)c&&c.setAxisConfig({drawLabels:!0,drawPlotLines:!0,drawAxisName:!0,drawAxisLine:!0,drawPlotBands:!0,drawTrendLines:!0,drawTrendLabels:!0}),c.show(),g=FusionCharts.get("component",
["dataset","line"]),(h=a[1])?(b=J.length,p=h.components.data.length,b<p&&h.removeData(b,p-b),h.JSONData={data:J},D.configure.call(h)):(h=new g,a.push(h),h.chart=this,h.index=K,D.init(h,J,"line"));else{if(h=a[1])Ha.call(h),a.pop();c&&(c.setAxisConfig({drawLabels:!1,drawPlotLines:!1,drawAxisName:!1,drawAxisLine:!1,drawPlotBands:!1,drawTrendLines:!1,drawTrendLabels:!1}),c.hide())}}else this.setChartMessage()},_setCategories:function(){var c=this.components,D=this.jsonData,h=D.dataset,a=c.numberFormatter,
c=c.xAxis,D=D.data||h&&h[0].data||[],h=[],p,g=D.length,K,b={},d=0,J;for(K=0;K<g;K++){p=D[K];J=a.getCleanValue(p.value,!0);if("true"===p.vline||"1"===p.vline||1===p.vline||!0===p.vline)b[d]=p;else if(null===J)continue;else p.value=J,h.push(p);d++}h.sort(function(c,h){return h.value-c.value});for(K in b)h.splice(K,0,b[K]);c[0].setCategory(h)},standaloneInit:!0,hasLegend:!1,isPercentage:!0},e.msdybasecartesian);e("pareto3d",{standaloneInit:!0,is3D:!0,friendlyName:"3D Pareto Chart",creditLabel:!1,fireGroupEvent:!0,
defaultPlotShadow:1,singleseries:!0,hasLegend:!1,defaultDatasetType:"column3d",_createDatasets:e.pareto2d,_setCategories:e.pareto2d,use3dlineshift:1,isPercentage:!0},e.msdybasecartesian3d);e("pie2d",{friendlyName:"Pie Chart",standaloneInit:!0,defaultSeriesType:"pie",defaultPlotShadow:1,reverseLegend:1,alignCaptionWithCanvas:0,sliceOnLegendClick:!0,isSingleSeries:!0,dontShowLegendByDefault:!0,defaultDatasetType:"Pie2D",applicableDSList:{Pie2D:!0},defaultZeroPlaneHighlighted:!1,creditLabel:!1,_plotDragMove:function(c,
D,h,a,p){var g=this.data("plotItem"),K=g.chart,g=g.seriesData,b=K.components.dataset[0].config;isNaN(c)||isNaN(D)||!b.enableRotation||g.singletonCase||g.isRightClicked||(c=Ja.call(p,h,a,g.pieCenter,g.chartPosition,1),g.isRotating||(g.dragStartAngle!==c&&(g.isRotating=!0),ea.raiseEvent("RotationStart",{startingAngle:O(g.startingAngleOnDragStart,!0)},K.chartInstance)),b.startAngle+=c-g.dragStartAngle,g.dragStartAngle=c,g.moveDuration=0,b.updateInited||(b.updateInited=!0,setTimeout(K._batchRotate||(K._batchRotate=
function(){K._rotate();b.updateInited=!1}),50)))},_plotDragStart:function(c,D,h){var a=this.data("plotItem"),p=a.chart,a=a.seriesData,g=p.components.dataset[0].config,K=-g.startAngle;a.isRightClicked=Pa||0===h.button||1===h.button?!1:!0;if(g.enableRotation&&!a.isRightClicked){a.isRotating=!1;g=p.linkedItems.container;p={left:0,top:0};if(g.getBoundingClientRect)g=g.getBoundingClientRect(),p.top=g.top+(Da.pageYOffset||la.scrollTop||0)-(la.clientTop||0),p.left=g.left+(Da.pageXOffset||la.scrollLeft||
0)-(la.clientLeft||0);else for(;g;)p.left+=g.offsetLeft||0,p.top+=g.offsetTop||0,g!==la.body&&g!==la.documentElement&&(p.left-=g.scrollLeft||0,p.top-=g.scrollTop||0),g=g.offsetParent;a.chartPosition=p;c=Ja.call(h,c,D,a.pieCenter,a.chartPosition,1);a.dragStartAngle=c;a.startingAngleOnDragStart=K}},_plotDragEnd:function(c){var D=this.data("plotItem"),h=D.chart,a=D.seriesData,p=-a.startAngle;a.isRightClicked||(h.disposed||h._rotate(),!a.isRotating&&h._plotGraphicClick.call(D.graphic,c),a.isRotating&&
(setTimeout(function(){a.isRotating=!1},0),ea.raiseEvent("RotationEnd",{startingAngle:O(p,!0),changeInAngle:p-a.startingAngleOnDragStart},h.chartInstance)))},_plotRollOver:function(c){var D=this.plotItem||this.data("plotItem"),h=D.chart,a=h.components.dataset[0].config,p,g;a.isRotating||(Ka.call(this,h,c,"DataPlotRollOver"),h.onPlotHover(this,!0));a.isHovered=!0;(c=D.innerDiameter)&&(p=D.centerLabelConfig)&&(g=p.label)&&h.drawDoughnutCenterLabel(g,D.center[0],D.center[1],c,c,p,!1)},onPlotHover:function(c,
D){var h=c.data("plotItem"),a=h.center,p=h.rolloverProperties||{},g=D?p.color:h.color,K=D?p.borderWidth:h.borderWidth,b=D?p.borderColor:h.borderColor;g&&(D&&(g.cx=a[0],g.cy=a[1],g.r=h.radius),p.enabled&&h.graphic.attr({fill:ka(g),"stroke-width":K,stroke:b}))},_plotRollOut:function(c){var D=this.plotItem||this.data("plotItem"),h=D.chart,a=h.components.dataset[0].config,p,g;a.isRotating||(Ka.call(this,h,c,"DataPlotRollOut"),h.onPlotHover(this,!1));a.isHovered=!1;(c=D.innerDiameter)&&(p=a.centerLabelConfig)&&
((g=p.label)||!g)&&h.drawDoughnutCenterLabel(g,D.center[0],D.center[1],c,c,p,!1)},_rotate:function(){var c,D,h=this.components.dataset[0],a=h.config,p=h.components.data,g=this.config,K=a.slicingDistance,h=h.config,b=l/h.valueTotal,d=g.canvasLeft+.5*g.canvasWidth,g=g.canvasTop+.5*g.canvasHeight,J=a.pieMinRadius,s=.5*(a.piePlotOptions.innerSize||0),u,t,A,e,f;u=(a.startAngle||0)%l;for(f=0;f<p.length;f+=1)A=p[f].config,e=p[f].graphics,c=A.y,null!==c&&void 0!==c&&(t=u,u-=h.singletonCase?l:c*b,c=.5*(u+
t),A.angle=c,A.transX=ra(c)*K,A.transY=qa(c)*K,A.slicedTranslation="t"+ra(c)*K+n+qa(c)*K,(D=A._rotateAttrs)||(D=A._rotateAttrs={ringpath:[],transform:Ga}),c=D.ringpath,c[0]=d,c[1]=g,c[2]=J,c[3]=s,c[4]=u,c[5]=t,D.transform=A.sliced?A.slicedTranslation:Ga,e.element.attr(D));this.placeDataLabels(!0,p,a)},getPlotData:function(c,D){var h=this.components.dataset[0],a=h.components.data[c].config,h=h.config.userData||(h.config.userData=[]),p,g;if(h[c])h=h[c];else{h=h[c]={};for(g in a)"object"!==typeof(p=
a[g])&&"function"!==typeof p&&0!==g.indexOf("_")&&(h[g]=p);h.value=h.y;h.categoryLabel=h.label=h.seriesName;delete h.y;delete h.total;delete h.doNotSlice;delete h.name;delete h.seriesName;delete h.centerAngle;delete h.showInLegend;delete h.angle;delete h.endAngle;delete h.isVisible;delete h.setColor;delete h.slicedTranslation;delete h.startAngle;delete h.transX;delete h.transY;delete h.pValue}h.sliced=D;return h},_plotGraphicClick:function(c){var D,h=this.element||this,a=h.plotItem||h.data("plotItem"),
p=h.data("eventArgs")||{},g=a.chart,K=a.index,b=g.components.dataset[0],d=b.config,J=d.enableMultiSlicing,b=b.components.data[K],s=b.graphics,b=b.config,u=b.doNotSlice,t;D=b.slicedTranslation;var A=g.get("config","animationObj"),e=A.duration||200,l=A.dummyObj,f=A.animObj,A=A.animType;!d.isRotating&&Ka.call(h,g,c);if(!(d.isRotating||d.singletonCase||u||(c=!J&&g.sliceInOtherPies(K),(t=b.sliced)&&c))){if(oa&&!Ea){c=(new Date).getTime();if(a.lastSliceTimeStamp&&400>c-a.lastSliceTimeStamp)return;a.lastSliceTimeStamp=
c}c=s.element;a=s.connector;s=s.label||s.dataLabel;D="object"===typeof D?"t"+D:D;J=b.connectorPath;h=(t?-1:1)*b.transX;d=(t?-1:1)*b.transY;u=c.data("eventArgs")||c.data("eventArgs",{});ea.raiseEvent("slicingStart",{slicedState:t,dataIndex:"index"in p&&p.index,data:g.getPlotData(K,t)},g.chartInstance);c.animateWith(l,f,{transform:t?"t0,0":D},e,A,function(){ea.raiseEvent("slicingEnd",{slicedState:t,dataIndex:"index"in p&&p.index,data:g.getPlotData(K,t)},g.chartInstance)});s&&s.x&&((D=s.data("textPos"))||
(D=s.data("textPos",{x:s.x,y:s.y})),s.animateWith(l,f,{x:s.x+(t?0:h)},e,A),D.x=s.x+(t?0:h));J&&(J[1]+=h,J[2]+=d,J[4]+=h,J[6]+=h,a.animateWith(l,f,{path:J},e,A));return u.isSliced=t=b.sliced=!t}},sliceInOtherPies:function(c){var D=this.components.dataset[0],h=D.components.data,a=h.length,p=0,g;for(D.enableMultiSlicing=!0;a--;)a!==c&&(g=h[a]).config.sliced&&++p&&this._plotGraphicClick.call(g.graphics);D.enableMultiSlicing=!1;return!!p},placeDataLabels:function(){var c=function(c,h){return c.point.value-
h.point.value},D=function(c,h){return c.angle-h.angle},h=["start","start","end","end"],a=[-1,1,1,-1],p=[1,1,-1,-1];return function(g,b,e,f){var J=this.config,s=this.components.dataset[0].config,u=J.canvasLeft,t=J.canvasTop,A=J.canvasWidth,Ua=u+.5*J.canvasWidth,ta=t+.5*J.canvasHeight,n=this.linkedItems.smartLabel,q=s.dataLabelOptions,y=q.style,ha=x(xa(parseFloat(y.lineHeight)),12),G=1===b.length?J.singletonPlaceValue:!1,r=q.skipOverlapLabels,S=q.manageLabelOverflow,v=q.connectorPadding,B;B=f&&f.metrics||
[Ua,ta,2*s.pieMinRadius,s.innerSize||0];var H=B[1],I=B[0];f=.5*B[2];var w=[[],[],[],[]],s=e.labelsRadius=f+q.distance,ta=Ua=parseInt(y.fontSize,10),C=ta/2,v=[v,v,-v,-v];e=e.labelsMaxInQuadrant||(e.labelsMaxInQuadrant=pa(s/ta));var q=q.isSmartLineSlanted,z=B[3]/2,T,P,E,Q,F,M,La,V,$,da,L,X,ga,ca,W,ia,O;B=Number.POSITIVE_INFINITY;var aa,N;T=[];E=[];T=this.get("config","animationObj");var ja=g?0:T.duration||0,ba=T.dummyObj,U=T.animObj,R=T.animType;n.useEllipsesOnOverflow(J.useEllipsesWhenOverflow);g||
n.setStyle(y);if(1==b.length&&!z&&G)z=b[0],N=z.graphics,X=N.label,z.slicedTranslation=[u,t],X&&(X.attr({visibility:Aa,"text-anchor":"middle",x:0,y:0,transform:["t",I,H]}),X.x=I,X.data("textPos",{x:I,y:H})),N.connector&&N.connector.attr({path:[]});else if(G)O=z+(f-z)/2,Ca(b,function(c){aa=c.config;N=c.graphics;if(X=N.label)X.attr({transform:"t0,0"}),L=aa.angle,da=H+O*qa(L),M=I+O*ra(L),X.x=M,X._x=M,X.y=da,c.sliced&&(ia=c.slicedTranslation,ca=ia[0]-u,W=ia[1]-t,M+=ca,da+=W),X.animateWith(ba,U,{visibility:Aa,
"text-anchor":"middle",x:0,y:0,transform:["t",M,da]},ja,R)});else{Ca(b,function(c){aa=c.config;N=c.graphics;if(X=N.label)X.attr({transform:"t0,0"}),L=aa.angle%l,0>L&&(L=l+L),ga=0<=L&&L<k?1:L<d?2:L<m?3:0,w[ga].push({point:c,angle:L})});for(b=g=4;b--;){if(r&&(y=w[b].length-e,0<y))for(w[b].sort(c),G=w[b].splice(0,y),y=0,E=G.length;y<E;y+=1)z=G[y].point,N=z.graphics,N.label.attr({visibility:"hidden"}),N.connector&&N.connector.attr({visibility:"hidden"});w[b].sort(D)}b=ua(w[0].length,w[1].length,w[2].length,
w[3].length);y=ua(sa(b,e)*ta,s+ta);E=w[0].concat(w[1]);T=w[2].concat(w[3]);for(b=E.length-1;0<=b;b--)G=E[b].point.config,delete G.clearance,delete G.clearanceShift,z=va(y*qa(G.angle)),Math.abs(B-z)<2*ha&&(G.clearance=0,E[b+1].point.clearanceShift=ha/2),B=z;B=Number.POSITIVE_INFINITY;b=0;for(E=T.length;b<E;b++)G=T[b].point.config,delete G.clearance,delete G.clearanceShift,z=va(y*qa(G.angle)),Math.abs(B-z)<2*ha&&(G.clearance=0,T[b-1].point.clearanceShift=ha/2),B=z;w[1].reverse();for(w[3].reverse();g--;){G=
w[g];E=G.length;r||(ta=E>e?y/E:Ua,C=ta/2);ha=E*ta;B=y;for(b=0;b<E;b+=1,ha-=ta)z=va(y*qa(G[b].angle)),B<z?z=B:z<ha&&(z=ha),B=(G[b].oriY=z)-ta;T=h[g];E=y-(E-1)*ta;B=0;for(b=G.length-1;0<=b;--b,E+=ta)if(z=G[b].point,L=G[b].angle,aa=z.config,N=z.graphics,Q=aa.sliced,X=N.label,z=va(y*qa(L)),z<B?z=B:z>E&&(z=E),B=z+ta,ha=void 0===aa.clearance?2*xa(x(parseFloat(aa.style.border),12),12):2*xa(x(parseFloat(aa.style.border),aa.clearance)),V=(z+G[b].oriY)/2,z=I+p[g]*s*ra(ma.asin(V/y)),V*=a[g],V+=H,$=H+f*qa(L),
F=I+f*ra(L),(2>g&&z<F||1<g&&z>F)&&(z=F),M=z+v[g],da=V-C-2,La=M+v[g],X.x=La,X._x=La,S&&(P=1<g?La-J.canvasLeft:J.canvasLeft+A-La,n.setStyle(aa.style),ha=x(xa(parseFloat(aa.style.lineHeight)),12)+ha,ha=n.getSmartText(aa.displayValue,P,ha),void 0===aa.clearance&&ha.height>ta&&(V+=ta),X.attr({text:ha.text}).tooltip(ha.tooltext)),X.y=da,Q&&(ca=aa.transX,W=aa.transY,M+=ca,z+=ca,F+=ca,$+=W,La+=ca),X.attr({"text-anchor":T,vAlign:"middle"}),aa._textAttrs||(aa._textAttrs={}),aa._textAttrs.x=La,aa._textAttrs.y=
V,(ha=X.data("textPos"))?X.attr({x:ha.x,y:ha.y}).animateWith(ba,U,aa._textAttrs,ja):X.attr(aa._textAttrs),X.data("textPos",{x:La,y:V}),ha=N.connector)aa.connectorPath=z=["M",F,$,"L",q?z:F,V,M,V],(F=ha.data("connectorPath"))?ha.attr({path:F.path}).animateWith(ba,U,{path:z},ja):ha.attr({path:z}),ha.data("connectorPath",{path:z})}}}}(),_spaceManager:function(){var c=this.config,D=this.components,h=D.dataset[0],a=h.components.data,b=h.config,g=D.legend,d=D.colorManager,e=this.linkedItems.smartLabel,f=
b.dataLabelCounter,J=0,s=this.jsonData.chart,D=x(s.managelabeloverflow,0),u=x(s.slicingdistance),t=b.preSliced||s.enableslicing!==H||s.showlegend===ba&&s.interactivelegend!==H?va(x(u,20)):0,A=x(s.pieradius,0),l=(u=x(s.enablesmartlabels,s.enablesmartlabel,1))?x(s.skipoverlaplabels,s.skipoverlaplabel,1):0,k=x(s.issmartlineslanted,1),m=f?x(s.labeldistance,s.nametbdistance,5):t,n=x(s.smartlabelclearance,5),y=c.width-(c.marginRight+c.marginLeft),q=c.height-(c.marginTop+c.marginBottom),G=sa(q,y),B=R(s.smartlinecolor,
d.getColor("plotFillColor")),v=x(s.smartlinealpha,100),L=x(s.smartlinethickness,.7),h=b.dataLabelOptions||(b.dataLabelOptions=h._parseDataLabelOptions()),d=h.style,d=f?x(parseInt(d.lineHeight,10),12):0,N=0===A?.15*G:A,Z=2*N,I={bottom:0,right:0},w=b.pieYScale,G=b.pieSliceDepth;h.connectorWidth=L;h.connectorPadding=x(s.connectorpadding,5);h.connectorColor=r(B,v);f&&(u&&(m=n),m+=t);n=Z+2*(d+m);n=this._manageChartMenuBar(n<q?q-n:q/2);q-=(n.top||0)+(n.bottom||0);b.showLegend&&(this.hasLegend=!0,R(s.legendposition,
S).toLowerCase()!==P?(I=g._manageLegendPosition(q/2),q-=I.bottom):(I=g._manageLegendPosition(q/2),y-=I.right));this._allocateSpace(I);e.useEllipsesOnOverflow(c.useEllipsesWhenOverflow);if(1!==f)for(;f--;)e.setStyle(a[f].config.style),c=e.getOriSize(a[f].config.displayValue),J=ua(J,c.width);0===A?N=this._stubRadius(y,J,q,m):(b.slicingDistance=t,b.pieMinRadius=N,h.distance=m);a=q-2*(N*w+d);b.managedPieSliceDepth=G>a?G-a:b.pieSliceDepth;h.isSmartLineSlanted=k;h.enableSmartLabels=u;h.skipOverlapLabels=
l;h.manageLabelOverflow=D},_stubRadius:function(c,D,h,a){var b=this.components.dataset[0],g=b.config,d=g.dataLabelCounter,e=this.jsonData.chart,f=x(e.slicingdistance),J=g.preSliced||e.enableslicing!==H||e.showlegend===ba&&e.interactivelegend!==H?va(x(f,20)):0,e=x(e.pieradius,0),s=sa(h,c),b=g.dataLabelOptions||(g.dataLabelOptions=b._parseDataLabelOptions()),u=b.style,d=d?x(parseInt(u.lineHeight,10),12):0,e=0===e?.15*s:e,s=0,s=sa(c/2-D-J,h/2-d)-a;s>=e?e=s:f||(J=a=ua(sa(a-(e-s),J),10));g.slicingDistance=
J;g.pieMinRadius=e;b.distance=a;return e},getDataSet:function(c){return this.components.dataset[c]},_startingAngle:function(c,D){var h,d=this.components.dataset[0].config,p=(h=d.startAngle)*-b+(0>-1*h?360:0);if(!isNaN(c)){if(d.singletonCase||d.isRotating)return;c+=D?p:0;d.startAngle=-c*a;this._rotate(c);p=c}return ja(100*((p%=360)+(0>p?360:0)))/100},eiMethods:{isPlotItemSliced:function(c){var D,h,a,b=this.apiInstance;return b&&b.components.dataset&&(a=b.components.dataset[0])&&(D=a.components.data)&&
D.length&&D[c]&&(h=D[c].config)&&h.sliced},addData:function(){var c,a=this.apiInstance;return a&&a.components.dataset&&(c=a.components.dataset[0])&&c.addData.apply(c,arguments)},removeData:function(){var c,a=this.apiInstance;return a&&a.components.dataset&&(c=a.components.dataset[0])&&c.removeData.apply(c,arguments)},updateData:function(){var c,a=this.apiInstance;return a&&a.components.dataset&&(c=a.components.dataset[0])&&c.updateData.apply(c,arguments)},slicePlotItem:function(c,a){var h,b,p,g,d=
this.apiInstance;return d&&d.components.dataset&&(h=d.components.dataset[0])&&(b=h.components.data)&&(g=b.length)&&b[c=h.config.reversePlotOrder?g-c-1:c]&&(p=b[c].config)&&((!!a!==p.sliced||void 0===a)&&d._plotGraphicClick.call(b[c].graphics.element)||p.sliced)},centerLabel:function(c,a){var h=this.apiInstance,b=h.components.dataset[0],p=b.config,g=p.piePlotOptions.innerSize,d=p.pieCenter,e=d[0],d=d[1],p=p.centerLabelConfig,f;if("object"!==typeof a)a=p;else for(f in p)void 0===a[f]&&(a[f]=p[f]);a.label=
c;b.centerLabelConfig=a;g&&h.drawDoughnutCenterLabel(c||"",e,d,g,g,a,!0)},startingAngle:function(c,a){return this.apiInstance._startingAngle(c,a)}}},e.guageBase,{plotborderthickness:1,alphaanimation:0,singletonPlaceValue:!0});e("pie3d",{defaultDatasetType:"Pie3D",applicableDSList:{Pie3D:!0},is3D:!0,friendlyName:"3D Pie Chart",fireGroupEvent:!0,creditLabel:!1,getPointColor:function(c){return c},_configureManager:function(){var c=this.components.dataset[0],a=c.config,h=c.components,c=h.Pie3DManager,
h=h.data;c&&c.configure(a.pieSliceDepth,1===h.length,a.use3DLighting,!1)},defaultPlotShadow:0,_preDrawCalculate:function(){var c,a,h=this.config,b=0,p=this.components.dataset[0],g=p.config;c=p.components;a=g.dataLabelOptions;var d=g.pie3DOptions=p._parsePie3DOptions(),e=R(g.startAngle,0)%l,f=g.managedPieSliceDepth,J=g.slicedOffset=d.slicedOffset,s=h.canvasWidth,u=h.canvasHeight,t=[h.canvasLeft+.5*s,h.canvasTop+.5*u-.5*f],A,k,h=c.data,m,n=sa(s,u),q,y=a.distance;A=g.pieYScale;var r=g.slicedOffsetY||
(g.slicedOffsetY=J*g.pieYScale);m=c.Pie3DManager;t.push(2*g.pieMinRadius,d.innerSize||0);t=ya(t,function(c,a){return(q=/%$/.test(c))?[s,u-f,n,n][a]*parseInt(c,10)/100:c});t[2]/=2;t[3]/=2;t.push(t[2]*A);t.push((t[2]+t[3])/2);t.push(t[5]*A);p.getX=function(c,a){k=ma.asin((c-t[1])/(t[2]+y));return t[0]+(a?-1:1)*ra(k)*(t[2]+y)};g.center=t;Ca(h,function(c){b+=c.config.y});g.labelsRadius=t[2]+y;g.labelsRadiusY=g.labelsRadius*A;g.quadrantHeight=(u-f)/2;g.quadrantWidth=s/2;p=ja(1E3*e)/1E3;d=p+l;e=x(parseInt(a.style.fontSize,
10),10)+4;g.maxLabels=pa(g.quadrantHeight/e);g.labelFontSize=e;g.connectorPadding=x(a.connectorPadding,5);g.isSmartLineSlanted=R(a.isSmartLineSlanted,!0);g.connectorWidth=x(a.connectorWidth,1);g.enableSmartLabels=a.enableSmartLabels;m||(m=c.Pie3DManager=new fa(this),this.get("graphics","datasetGroup").trackTooltip(!0));this._configureManager();for(c=h.length-1;0<=c;--c)a=h[c],a=a.config,e=p,m=b?a.y/b:0,p=ja(1E3*(p+m*l))/1E3,p>d&&(p=d),A=p,a.shapeArgs={start:ja(1E3*e)/1E3,end:ja(1E3*A)/1E3},a.centerAngle=
k=(A+e)/2%l,a.slicedTranslation=[ja(ra(k)*J),ja(qa(k)*r)],e=ra(k)*t[2],g.radiusY=A=qa(k)*t[4],a.tooltipPos=[t[0]+.7*e,t[1]+A],a.percentage=100*m,a.total=b},placeDataLabels:function(){var c=function(c,a){return c.point.value-a.point.value},a=function(c,a){return c.angle-a.angle},h=["start","start","end","end"],b=[-1,1,1,-1],p=[1,1,-1,-1];return function(g){var K,e,f,J=this.config,s=this.components.dataset[0],u=s.config,t=s.components.data,A=u.piePlotOptions,n=J.canvasLeft,q=J.canvasTop,s=J.canvasWidth,
r=n+.5*J.canvasWidth,B=q+.5*J.canvasHeight,y=this.linkedItems.smartLabel,v=u.dataLabelOptions,G=v.style;K=x(xa(parseFloat(G.lineHeight)),12);var S=Sa(v.placeInside,!1),H=v.skipOverlapLabels,P=v.manageLabelOverflow,L=v.connectorPadding,Z=v.distance,I=v.connectorWidth,w=[[],[],[],[]],C=parseInt(G.fontSize,10),z=C,T=z/2,L=[L,L,-L,-L],Y=v.isSmartLineSlanted,E,Q,F,M,N,V,$,da,O,X,ga,ca,W,ia,Z=0<Z,ba=u.center||(u.center=[r,B,A.size,A.innerSize||0]),aa=ba[1],U=ba[0],A=ba[2],r=ba[4],B=u.labelsRadius,R=ja(100*
u.labelsRadiusY)/100,ea=u.maxLabels,la=u.enableSmartLabels,ka=u.pieSliceDepth/2,fa=this.get("config","animationObj"),pa=g?0:fa.duration,na=fa.dummyObj,oa=fa.animObj,fa=fa.animType;y.useEllipsesOnOverflow(J.useEllipsesWhenOverflow);if(u.dataLabelCounter)if(g||y.setStyle(G),1==t.length)K=t[0],ia=K.graphics,W=K.config,ga=ia.label,V=ia.connector,W.slicedTranslation=[n,q],ga&&null!==W.y&&(ga.attr({visibility:Aa,"text-anchor":"middle",x:U,y:aa+T-2}),ga.x=U),V&&V.hide();else if(S)Ca(t,function(c){ia=c.graphics;
W=c.config;if((ga=ia.label)&&null!==W.y){X=W.angle;O=aa+ba[6]*qa(X)+T-2;N=U+ba[5]*ra(X);ga.x=N;ga._x=N;ga.y=O;if(W.sliced){c=c.slicedTranslation;var a=c[1]-q;N+=c[0]-n;O+=a}ga.attr({visibility:Aa,align:"middle",x:N,y:O})}});else{Ca(t,function(c){ia=c.graphics;W=c.config;if(ga=ia.label)X=W.angle,0>X&&(X=l+X),ca=0<=X&&X<k?1:X<d?2:X<m?3:0,w[ca].push({point:c,angle:X})});for(u=J=4;u--;){if(H&&(t=w[u].length-ea,0<t))for(w[u].sort(c),S=w[u].splice(0,t),t=0,Q=S.length;t<Q;t+=1)K=S[t].point,ia=K.graphics,
ia.label.attr({visibility:"hidden"}),ia.connector&&ia.connector.attr({visibility:"hidden"});w[u].sort(a)}u=ua(w[0].length,w[1].length,w[2].length,w[3].length);R=ua(sa(u,ea)*z,R+z);w[1].reverse();w[3].reverse();for(y.setStyle(G);J--;){t=w[J];Q=t.length;H||(z=Q>ea?R/Q:C,T=z/2);S=Q*z;G=R;for(u=0;u<Q;u+=1,S-=z)K=va(R*qa(t[u].angle)),G<K?K=G:K<S&&(K=S),G=(t[u].oriY=K)-z;S=h[J];Q=R-(Q-1)*z;G=0;for(u=t.length-1;0<=u;--u,Q+=z)K=t[u].point,ia=K.graphics,W=K.config,null!==W.y&&(X=t[u].angle,f=W.sliced,ga=ia.label,
K=va(R*qa(X)),K<G?K=G:K>Q&&(K=Q),G=K+z,$=(K+t[u].oriY)/2,F=U+p[J]*B*ra(ma.asin($/R)),$*=b[J],$+=aa,da=aa+r*qa(X),M=U+A*ra(X),(2>J&&F<M||1<J&&F>M)&&(F=M),N=F+L[J],O=$+T-2,V=N+L[J],ga.x=V,ga._x=V,P&&(E=1<J?V-n:n+s-V,y.setStyle(W.style),K=x(xa(parseFloat(W.style.lineHeight)),12)+(2*xa(parseFloat(W.style.border),12)||0),K=y.getSmartText(W.displayValue,E,K),ga.attr({text:K.text}).tooltip(K.tooltext)),X<d&&($+=ka,da+=ka,O+=ka),ga.y=O,f&&(K=W.transX,f=W.transY,N+=K,F+=K,M+=K,da+=f,V+=K),ga.attr({visibility:Aa,
"text-anchor":S}),(f=ga.data("textPos"))&&ga.attr({x:f.x,y:f.y}),K={x:V,y:$},!g&&f?ga.animateWith(na,oa,K,pa,fa):ga.attr(K),ga.data("textPos",{x:V,y:$}),Z&&I&&la&&(V=ia.connector,W.connectorPath||(e=!0),W.connectorPath=K=["M",M,da,"L",Y?F:M,$,N,$],K={path:K,"stroke-width":I,stroke:v.connectorColor||"#606060",visibility:Aa},V&&(g||e?V.attr(K):V.animateWith(na,oa,K,pa,fa))))}}}}(),animate:function(){var c,a,h,b,p=this,g=p.components.dataset[0],d=g.components.data;c=p.graphics.datasetGroup;var e=d.length;
a=g.config.alphaAnimation;h=function(){p.disposed||p.disposing||p.placeDataLabels(!1)};var f=p.get("config","animationObj"),g=f.duration||0,J=f.dummyObj,s=f.animObj,f=f.animType;if(a)c.attr({opacity:0}),c.animateWith(J,s,{opacity:1},g,f,h);else for(c=0;c<e;c++)if(a=d[c],h=a.graphics,a=a.config,b=a.shapeArgs,a=2*wa,h=h.element)h.attr({start:a,end:a}),h=b.start,b=b.end,(void 0).animateWith(J,s,{cx:h-a,cy:b-a},g,f)},_rotate:function(c){var a=this.components.dataset[0],h=a.config,a=a.components,b=a.data,
p=h.slicedOffset,g=h.slicedOffsetY,d=h.startAngle,e;c=isNaN(c)?-h._lastAngle:c;e=(c-d)%360;h.startAngle=x(c,h.startAngle)%360;e=-(e*wa)/180;a.Pie3DManager&&a.Pie3DManager.rotate(e);Ca(b,function(c){var a=[],h=c.config;c=c.graphics.element;var a=h.shapeArgs,b=a.start+=e,a=a.end+=e,D=h.angle=O((b+a)/2),b=h.sliced,a=ra(D),D=qa(D),a=h.slicedTranslation=[ja(a*p),ja(D*g)];h.transX=a[0];h.transY=a[1];h.slicedX=b?ra(e)*p:0;h.slicedY=b?qa(e)*g:0;c&&b&&c.attr({transform:"t"+a[0]+","+a[1]})});this.placeDataLabels(!0,
b)},_plotRollOver:function(c){var a=this.data("plotItem"),h=a.chart,b=h.config,p=h.components.dataset[0],g=p.components.data[a.index],a=g.graphics.element,g=g.config.hoverEffects;p.config.isRotating||(Ka.call(a,h,c,"DataPlotRollOver"),g.enabled&&a.attr(g));b.isHovered=!0},_plotRollOut:function(c){var a=this.data("plotItem"),h=a.chart,b=h.config,p=h.components.dataset[0],g=p.components.data[a.index],a=g.config,g=g.graphics.element;p.config.isRotating||(Ka.call(g,h,c,"DataPlotRollOut"),g.attr({color:a.color.color.split(",")[0],
alpha:a._3dAlpha,borderWidth:a.borderWidth,borderColor:a.borderColor}));b.isHovered=!1},_plotDragStart:function(c,a,h){var b=this.data("plotItem").chart.components.dataset[0].config;b.isRightClicked=Pa||0===h.button||1===h.button?!1:!0;b.enableRotation&&!b.isRightClicked&&(b.isRotating=!1,c=Ja.call(h,c,a,b.center,b.chartPosition,b.pieYScale),b.dragStartAngle=c,b._lastAngle=-b.startAngle,b.startingAngleOnDragStart=b.startAngle)},_plotDragEnd:function(c){var a=this.data("plotItem"),h=a.index,a=a.chart,
b=a.config,p=a.components.dataset[0],g=p.config,p=p.components.Pie3DManager,d=g.startAngle;g.isRightClicked||(g.isRotating?(setTimeout(function(){g.isRotating=!1},0),ea.raiseEvent("rotationEnd",{startingAngle:O(d,!0),changeInAngle:d-g.startingAngleOnDragStart},a.chartInstance),!b.isHovered&&p.colorObjs[h]&&p.onPlotHover(h,!1)):a._plotGraphicClick.call(this,c))},_plotDragMove:function(c,a,h,b,p){var g=this.data("plotItem").chart,d=g.components.dataset[0].config;isNaN(c)||isNaN(a)||!d.enableRotation||
d.singletonCase||d.isRightClicked||(c=Ja.call(p,h,b,d.center,d.chartPosition,d.pieYScale),d.isRotating||(d.dragStartAngle!==c&&(d.isRotating=!0),ea.raiseEvent("rotationStart",{startingAngle:O(d.startAngle,!0)},g.chartInstance)),a=c-d.dragStartAngle,d.dragStartAngle=c,d.moveDuration=0,d._lastAngle+=180*a/wa,c=(new Date).getTime(),d._lastTime&&!(d._lastTime+d.timerThreshold<c))||(d._lastTime||g._rotate(),d.timerId=setTimeout(function(){g.disposed&&g.disposing||g._rotate()},d.timerThreshold),d._lastTime=
c)},_stubRadius:function(c,a,h,b){var d=this.components.dataset[0],g=d.config,e=x(d.components.data&&d.components.data.length,0),f=d.config,l=x(f.slicingdistance),J=g.preSliced||f.enableslicing!==H||f.showlegend===ba&&f.interactivelegend!==H?va(x(l,20)):0,f=x(f.pieradius,0),s=sa(h,c),d=g.dataLabelOptions||(g.dataLabelOptions=d._parseDataLabelOptions()),u=d.style,e=e?x(parseInt(u.lineHeight,10),12):0,f=0===f?.15*s:f,s=0,s=g.pieYScale;h-=g.pieSliceDepth;s=sa(c/2-a-J,(h/2-e)/s)-b;s>=f?f=s:l||(J=b=ua(sa(b-
(f-s),J),10));g.slicingDistance=J;g.pieMinRadius=f;d.distance=b;return f},_startingAngle:function(c,a){var h,b=this.components.dataset[0].config,d=(h=b.startAngle)+(0>h?360:0);if(!isNaN(c)){if(b.singletonCase||b.isRotating)return;c+=a?d:0;this._rotate(c);d=c}return ja(100*((d%=360)+(0>d?360:0)))/100}},e.pie2d,{plotborderthickness:.1,alphaanimation:1});e("doughnut2d",{friendlyName:"Doughnut Chart",defaultDatasetType:"Doughnut2D",creditLabel:!1,applicableDSList:{Doughnut2D:!0},getPointColor:function(c,
a,h){var b;c=f(c);a=B(a);100>h&&za?(b=q(c,pa(100*(85-.2*(100-h)))/100),c=v(c,pa(100*(100-.5*h))/100),a={FCcolor:{color:b+","+c+","+c+","+b,alpha:a+","+a+","+a+","+a,radialGradient:!0,gradientUnits:"userSpaceOnUse",r:h}}):a={FCcolor:{color:c+","+c,alpha:a+","+a,ratio:"0,100"}};return a},drawDoughnutCenterLabel:function(c,a,h,b,d,g,e){var f=this.components,l=f.dataset[0].config;g=g||l.lastCenterLabelConfig;var f=f.paper,J=this.linkedItems.smartLabel,s=this.graphics,u=s.datasetGroup,t=g.padding,A=2*
g.textPadding,k={fontFamily:g.font,fontSize:g.fontSize+"px",lineHeight:1.2*g.fontSize+"px",fontWeight:g.bold?"bold":"",fontStyle:g.italic?"italic":""},m=1.414*(.5*b-t)-A;d=1.414*(.5*d-t)-A;var n;J.setStyle(k);J.useEllipsesOnOverflow(this.config.useEllipsesWhenOverflow);J=J.getSmartText(c,m,d);(d=s.doughnutCenterLabel)?(d.attr("text")!==c&&this.centerLabelChange(c),n=s.centerLabelOvalBg):(g.bgOval&&(s.centerLabelOvalBg=n=f.circle(a,h,.5*b-t,u)),d=s.doughnutCenterLabel=f.text(u).hover(this.centerLabelRollover,
this.centerLabelRollout).click(this.centerLabelClick),d.chart=this);c?(d.css(k).attr({x:a,y:h,text:J.text,visibility:Aa,direction:l.textDirection,fill:ka({FCcolor:{color:g.color,alpha:g.alpha}}),"text-bound":g.bgOval?"none":[ka({FCcolor:{color:g.bgColor,alpha:g.bgAlpha}}),ka({FCcolor:{color:g.borderColor,alpha:g.borderAlpha}}),g.borderThickness,g.textPadding,g.borderRadius]}).tooltip(g.toolText||J.tooltext),g.bgOval&&n&&n.attr({visibility:Aa,fill:Ta(g.bgColor),"fill-opacity":g.bgAlpha/100,stroke:Ta(g.borderColor),
"stroke-width":g.borderThickness,"stroke-opacity":g.borderAlpha/100})):(d.attr("visibility","hidden"),n&&n.attr("visibility","hidden"));e&&(l.lastCenterLabelConfig=g,l.centerLabelConfig=g)},centerLabelRollover:function(){var c=this.chart,a=c.config,h=c.chartInstance,b=h.ref,d=c.components.dataset[0].config.lastCenterLabelConfig,a={height:a.height,width:a.width,pixelHeight:b.offsetHeight,pixelWidth:b.offsetWidth,id:h.id,renderer:h.args.renderer,container:c.linkedItems.container,centerLabelText:d&&
d.label};this.attr("text")&&ea.raiseEvent("centerLabelRollover",a,h,this,c.hoverOnCenterLabel)},centerLabelRollout:function(){var c=this.chart,a=c.config,h=c.chartInstance,b=h.ref,d=c.components.dataset[0].config.lastCenterLabelConfig,a={height:a.height,width:a.width,pixelHeight:b.offsetHeight,pixelWidth:b.offsetWidth,id:h.id,renderer:h.args.renderer,container:c.linkedItems.container,centerLabelText:d&&d.label};this.attr("text")&&ea.raiseEvent("centerLabelRollout",a,h,this,c.hoverOffCenterLabel)},
centerLabelClick:function(){var c=this.chart,a=c.config,h=c.chartInstance,b=h.ref,d=c.components.dataset[0].config.lastCenterLabelConfig,c={height:a.height,width:a.width,pixelHeight:b.offsetHeight,pixelWidth:b.offsetWidth,id:h.id,renderer:h.args.renderer,container:c.linkedItems.container,centerLabelText:d&&d.label};this.attr("text")&&ea.raiseEvent("centerLabelClick",c,h)},centerLabelChange:function(c){var a=this.config,h=this.chartInstance,b=h.ref;ea.raiseEvent("centerLabelChanged",{height:a.height,
width:a.width,pixelHeight:b.offsetHeight,pixelWidth:b.offsetWidth,id:h.id,renderer:h.args.renderer,container:this.linkedItems.container,centerLabelText:c},h)},hoverOnCenterLabel:function(){var c=this.chart.components.dataset[0].config.lastCenterLabelConfig;(c.hoverColor||c.hoverAlpha)&&this.attr({fill:ka({FCcolor:{color:c.hoverColor||c.color,alpha:c.hoverAlpha||c.alpha}})})},hoverOffCenterLabel:function(){var c=this.chart.components.dataset[0].config.lastCenterLabelConfig;(c.hoverColor||c.hoverAlpha)&&
this.attr({fill:ka({FCcolor:{color:c.color,alpha:c.alpha}})})}},e.pie2d,{singletonPlaceValue:!1});e("doughnut3d",{friendlyName:"3D Doughnut Chart",defaultDatasetType:"Doughnut3D",creditLabel:!1,applicableDSList:{Doughnut3D:!0},_configureManager:function(){var c=this.components.dataset[0],a=c.config,b=c.components,c=b.Pie3DManager,b=b.data;c&&c.configure(a.pieSliceDepth,1===b.length,a.use3DLighting,!0)}},e.pie3d);e("mscolumn2d",{standaloneInit:!0,friendlyName:"Multi-series Column Chart",creditLabel:!1,
defaultDatasetType:"column",applicableDSList:{column:!0},eiMethods:{}},e.mscartesian);e("mscolumn3d",{standaloneInit:!0,defaultDatasetType:"column3d",friendlyName:"Multi-series 3D Column Chart",applicableDSList:{column3d:!0},defaultPlotShadow:1,fireGroupEvent:!0,is3D:!0,creditLabel:!1,defaultZeroPlaneHighlighted:!1},e.mscartesian3d);e("msbar2d",{standaloneInit:!0,friendlyName:"Multi-series Bar Chart",isBar:!0,hasLegend:!0,creditLabel:!1,defaultDatasetType:"bar2d",applicableDSList:{bar2d:!0}},e.msbarcartesian);
e("msbar3d",{standaloneInit:!0,defaultSeriesType:"bar3d",friendlyName:"Multi-series 3D Bar Chart",fireGroupEvent:!0,defaultPlotShadow:1,is3D:!0,isBar:!0,hasLegend:!0,creditLabel:!1,defaultZeroPlaneHighlighted:!1,defaultDatasetType:"bar3d",applicableDSList:{bar3d:!0}},e.msbarcartesian3d);e("msarea",{standaloneInit:!0,friendlyName:"Multi-series Area Chart",creditLabel:!1,defaultDatasetType:"area",showValues:1,defaultPlotShadow:0,applicableDSList:{area:!0}},e.mscartesian);e("msline",{standaloneInit:!0,
friendlyName:"Multi-series Line Chart",creditLabel:!1,defaultDatasetType:"line",defaultPlotShadow:1,axisPaddingLeft:0,axisPaddingRight:0,applicableDSList:{line:!0}},e.mscartesian,{zeroplanethickness:1,zeroplanealpha:40,showzeroplaneontop:0});e("stackedarea2d",{friendlyName:"Stacked Area Chart",isStacked:!0,showsum:0,creditLabel:!1,areaAlpha:100},e.msarea);e("stackedcolumn2d",{friendlyName:"Stacked Column Chart",isStacked:!0,creditLabel:!1},e.mscolumn2d);e("stackedcolumn3d",{friendlyName:"3D Stacked Column Chart",
isStacked:!0,creditLabel:!1},e.mscolumn3d);e("stackedbar2d",{friendlyName:"Stacked Bar Chart",isStacked:!0,creditLabel:!1},e.msbar2d);e("stackedbar3d",{friendlyName:"3D Stacked Bar Chart",isStacked:!0,creditLabel:!1},e.msbar3d);e("marimekko",{standaloneInit:!0,friendlyName:"Marimekko Chart",isValueAbs:!0,distributedColumns:!0,stack100percent:!0,defaultDatasetType:"marimekko",applicableDSList:{marimekko:!0},isStacked:!0,showsum:1,creditLabel:!1,_setAxisLimits:function(){var c=this.components,a=c.dataset,
b=c.yAxis,c=c.xAxis,d,p=a.length,g,e=-Infinity,f=Infinity,l=Infinity,k=-Infinity,s,u,t={};s=this.config.categories;var A=[],m=function(c){e=ua(e,c.max);f=sa(f,c.min);k=ua(k,c.xMax||-Infinity);l=sa(l,c.xMin||Infinity)};for(g=0;g<p;g++)d=a[g],(u=d.groupManager)?t[d.type]=u:A.push(d);for(u in t)a=t[u].getDataLimits(),m(a);p=A.length;for(g=0;g<p;g++)a=A[g].getDataLimits(),m(a);-Infinity===e&&(e=0);Infinity===f&&(f=0);b[0].setAxisConfig({isPercent:this.isStacked?this.config.stack100Percent:0});b[0].setDataLimit(e,
f);if(-Infinity!==k||Infinity!==l)c[0].config.xaxisrange={max:k,min:l},c[0].setDataLimit(k,l);b=t[u].getStackSumPercent();g=b.length;u=c[0].getCategoryLen();u>g&&s.splice(g,u-g);this._setCategories();g=c[0].getLimit();l=g.min;k=g.max;s=l;u=k-l;for(g=0;g<b.length;g++)a=b[g],p=u*a/100,a=s+p/2,c[0].updateCategory(g,{x:a}),s+=p}},e.mscartesian);e("msstackedcolumn2d",{standaloneInit:!0,isStacked:!0,defaultDatasetType:"column",applicableDSList:{column:!0},friendlyName:"Multi-series Stacked Column Chart",
_createDatasets:function(){var c=this.components,a=this.jsonData,b=a.dataset,d=b&&b.length||0,p,g,e,f=this.defaultDatasetType,l=this.applicableDSList,k,s,u,t,A,m,n=a.lineset||[],q=this.config,r=q.dataSetMap,y=q.lineSetMap,v=r&&r.length,B=c.legend,S=[],x=[],H=0;u=0;var L,P=-1,I,w=this.config.catLen,C=c.xAxis[0],z,T,N=c.dataset;if(b||0!==n.length){this.config.categories=a.categories&&a.categories[0].category;g=c.dataset=[];for(a=0;a<d;a++){A=b[a];P++;if(A.dataset)for(T=!0,m=A.dataset&&A.dataset.length||
0,S[a]=[],p=0;p<m;p++){if(u=A.dataset[p],k=(k=R(u.renderas,f))&&k.toLowerCase(),l[k]||(k=f),e=FusionCharts.get("component",["dataset",k]))t="datasetGroup_"+k,s=FusionCharts.register("component",["datasetGroup",k]),k=c[t],s&&!k?(k=c[t]=new s,k.chart=this,k.init()):k&&r&&0!==r.length&&!L&&(k.init(),L=!0),r&&r[a]&&r[a][p]?(e=r[a][p],e.index=H,t=e.JSONData,s=t.data.length,t=u.data&&u.data.length||0,I=C.getCategoryLen(),z=w-I,s-=t,s=this._getDiff(s,t,z,I),t=s.diff,s=s.startIndex,0<t&&e.removeData(s,t,
!1),e.JSONData=u,e.configure()):(e=new e,e.chart=this,e.index=H,e.init(u)),H++,S[a].push(e),g.push(e),k&&k.addDataSet(e,P,p)}else m=p=0,P--;A=r&&r[a]&&r[a].length;if(A>m)for(u=p,A=A-m+p;u<A;u++)k=r[a][u],B.removeItem(k.legendItemId),Ha.call(k)}if(v>d)for(u=a,A=v-d+a;u<A;u++)for(m=r[u].length,p=0;p<m;p++)k=r[u][p],B.removeItem(k.legendItemId),Ha.call(k);q.dataSetMap=S;if(this.lineset){a=0;for(d=n.length;a<d;a++)b=n[a],e=FusionCharts.get("component",["dataset","line"]),e=new e,y&&y[a]?(e=y[a],e.index=
H,t=e.JSONData,s=t.data.length,t=b.data&&b.data.length||0,s>t&&e.removeData(t,s-t,!1),e.JSONData=b,e.configure()):(e.chart=this,e.index=H,e.init(b)),x.push(e),g.push(e),H++;n=y&&y.length;if(n>d)for(u=a,A=n-d+a;u<A;u++)k=y[u],B.removeItem(k.legendItemId),Ha.call(k);q.lineSetMap=x}T?this.config.catLen=C.getCategoryLen():(c.dataset=N,this.setChartMessage())}else this.setChartMessage()},creditLabel:!1},e.mscartesian);e("mscombi2d",{friendlyName:"Multi-series Combination Chart",standaloneInit:!0,creditLabel:!1,
defaultDatasetType:"column",applicableDSList:{line:!0,area:!0,column:!0},_createDatasets:function(){var c=this.components,a=this.jsonData,b=a.dataset,d=b&&b.length,p,g,e=this.defaultDatasetType,f=this.applicableDSList,k=this.components.legend,l=c.xAxis[0],s,u,t,A,m,n=this.isStacked,q,r,y=[],v={},B=this.config,S=this.config.catLen,x=B.datasetMap||(B.datasetMap={line:[],area:[],column:[],column3d:[],scrollcolumn2d:[]}),H={line:[],area:[],column:[],column3d:[],scrollcolumn2d:[]};b||this.setChartMessage();
this.config.categories=a.categories&&a.categories[0].category;p=c.dataset=[];for(a=0;a<d;a++)if(m=b[a],A=m.parentyaxis||"",u=(u=this.isDual&&"s"===A.toLowerCase()?"line"===this.defaultSecondaryDataset?this.sDefaultDatasetType:R(m.renderas,this.sDefaultDatasetType):R(m.renderas,e))&&u.toLowerCase(),f[u]||(u=e),t=FusionCharts.get("component",["dataset",u]))void 0===v[u]?v[u]=0:v[u]++,g="datasetGroup_"+u,A=FusionCharts.register("component",["datasetGroup",u]),(s=c[g])&&y.push(s),A&&!s&&(s=c[g]=new A,
s.chart=this,s.init()),A=x[u],(g=A[0])?(s=l.getCategoryLen(),t=S-s,r=g.JSONData,q=r.data&&r.data.length,r=m.data&&m.data.length||0,q-=r,t=this._getDiff(q,r,t,s),s=t.diff,t=t.startIndex,0<s&&g.removeData(t,s,!1),g.JSONData=m,g.configure(),A.splice(0,1)):(g=new t,g.chart=this,g.index=a,s&&(n?s.addDataSet(g,0,v[u]):s.addDataSet(g,v[u],0)),g.init(m)),H[u].push(g),p.push(g);for(b in x)if(A=x[b],e=A[0]&&A[0].groupManager,d=A.length,f=void 0===v[b]?0:v[b]+1,d)for(n&&e&&e.removeDataSet(0,f,d),c=0;c<d;c++)e&&
!n&&e.removeDataSet(f,0,1),k.removeItem(A[c].legendItemId),"column"===A[c].type&&!0===this.is3D?(A[c].visible=!1,A[c].draw()):Ha.call(A[c]);B.datasetMap=H;this.config.catLen=l.getCategoryLen()}},e.mscartesian);e("mscombi3d",{standaloneInit:!0,friendlyName:"Multi-series 3D Combination Chart",defaultDatasetType:"column3d",is3D:!0,creditLabel:!1,defaultPlotShadow:1,applicableDSList:{column3d:!0,line:!0,area:!0},_createDatasets:e.mscombi2d},e.mscartesian3d);e("mscolumnline3d",{friendlyName:"Multi-series Column and Line Chart",
use3dlineshift:1,is3D:!0,creditLabel:!1,defaultPlotShadow:1,applicableDSList:{column3d:!0,line:!0}},e.mscombi3d);e("stackedcolumn2dline",{friendlyName:"Stacked Column and Line Chart",isStacked:!0,stack100percent:0,defaultDatasetType:"column",creditLabel:!1,applicableDSList:{line:!0,column:!0}},e.mscombi2d);e("stackedcolumn3dline",{friendlyName:"Stacked 3D Column and Line Chart",isStacked:!0,is3D:!0,use3dlineshift:1,creditLabel:!1,stack100percent:0,applicableDSList:{column3d:!0,line:!0}},e.mscombi3d);
e("mscombidy2d",{standaloneInit:!0,friendlyName:"Multi-series Dual Y-Axis Combination Chart",defaultDatasetType:"column",sDefaultDatasetType:"line",_createDatasets:e.mscombi2d,isDual:!0,creditLabel:!1,applicableDSList:{column:!0,line:!0,area:!0}},e.msdybasecartesian);e("mscolumn3dlinedy",{standaloneInit:!0,friendlyName:"Multi-series 3D Column and Line Chart",defaultDatasetType:"column3d",sDefaultDatasetType:"line",is3D:!0,isDual:!0,use3dlineshift:1,creditLabel:!1,_createDatasets:e.mscombi2d,defaultPlotShadow:1,
applicableDSList:{column3d:!0,line:!0}},e.msdybasecartesian3d);e("stackedcolumn3dlinedy",{standaloneInit:!0,friendlyName:"Stacked 3D Column and Line Chart",isStacked:!0,is3D:!0,isDual:!0,use3dlineshift:1,defaultDatasetType:"column3d",creditLabel:!1,sDefaultDatasetType:"line",defaultSecondaryDataset:"line",_createDatasets:e.mscombi2d,applicableDSList:{column3d:!0,line:!0}},e.msdybasecartesian3d);e("msstackedcolumn2dlinedy",{standaloneInit:!0,friendlyName:"Multi-series Dual Y-Axis Stacked Column and Line Chart",
isDual:!0,stack100percent:0,isStacked:!0,defaultDatasetType:"column",sDefaultDatasetType:"line",hasLineSet:!0,creditLabel:!1,applicableDSList:{column:!0},lineset:!0,_createDatasets:e.msstackedcolumn2d},e.msdybasecartesian);e("scrollcolumn2d",{standaloneInit:!0,friendlyName:"Scrollable Multi-series Column Chart",tooltipConstraint:"plot",canvasborderthickness:1,creditLabel:!1,defaultDatasetType:"scrollcolumn2d",applicableDSList:{scrollcolumn2d:!0},avgScrollPointWidth:40,hasScroll:!0,defaultPlotShadow:1,
_manageScrollerPosition:function(){var c=this.config,a;a=this._scrollBar.get;var b=this.components.scrollBar,d;a=a()[0];b.setConfiguaration(a.conf);a=c.scrollEnabled;d=b.getLogicalSpace();this._allocateSpace({bottom:c.shift=!1===a?0:d.height+b.conf.padding})},_resetViewPortConfig:function(){this.config.viewPortConfig={scaleX:1,scaleY:1,x:0,y:0}},updateManager:function(c){var a=this.config,b=this.config.viewPortConfig,d=b.scaleX,p=this.graphics.datasetGroup,g=this.graphics.datalabelsGroup,e=this.graphics.trackerGroup,
f=a.canvasWidth*(d-1)*c,k=this.components.xAxis[0],l=this.graphics.sumLabelsLayer;b.x=f/d;b="t"+-ja(f)+",0";a.lastScrollPosition=c;p.attr({transform:b});g.attr({transform:b});e.attr({transform:b});l&&l.attr({transform:b});c=k.getAxisConfig("animateAxis");k.setAxisConfig({animateAxis:!1});k.draw();k.setAxisConfig({animateAxis:c})},_createToolBox:function(){var c=this.components,a=this._scrollBar,b=a.get,d=a.add,p,g,f=c.scrollBar;e.mscartesian._createToolBox.call(this);p=c.tb;g=(c.toolBoxAPI||p.getAPIInstances(p.ALIGNMENT_HORIZONTAL)).Scroller;
a.clear();d({isHorizontal:!0},{scroll:function(c){return function(){c.updateManager.apply(c,arguments)}}(this)});a=b()[0];f||(c.scrollBar=(new g(a.conf,p.idCount,p.pId)).attachEventHandlers(a.handler))},_setAxisScale:function(){var c=this.config,a=this.components.xAxis[0].getCategoryLen(),b=this.jsonData,d=c.scrollOptions||(c.scrollOptions={}),e=this.components.dataset,g=e.length,f,k,l=0,m;m=c.canvasWidth;var s=c.scrollToEnd,u=c.lastScrollPosition,b=x(b.chart.numvisibleplot,pa(c.width/this.avgScrollPointWidth));
for(k=0;k<g;k++)f=e[k],"column"===f.type&&l++;this.isStacked&&(l=1);a*=l||1;2<=b&&b<a?(c.viewPortConfig.scaleX=a/=b,m=m*(a-1)*(void 0!==u?u:s?1:0),c.viewPortConfig.x=m/a,d.vxLength=b/g,c.scrollEnabled=!0):c.scrollEnabled=!1},drawScrollBar:function(){var c=this,a=c.config,b=a.viewPortConfig,d=c.components,e=d.xAxis[0],g=e.config.axisData,f=e.config.axisRange,k=a.scrollOptions||(a.scrollOptions={}),l=f.max,m=f.min,s=k.vxLength,u=d.scrollBar,f=u.node,t=a.scrollToEnd,A=a.lastScrollPosition,n=b.scaleX,
q,r,v,y,B;y=void 0!==A?A:t?1:0;b=a.canvasLeft;t=a.canvasTop;A=a.canvasHeight;q=a.canvasWidth;d=d.canvas.config;r=d.canvasBorderWidth;v=g.showAxisLine?g.axisLineThickness||0:0;B=x(r,g.lineStartExtension);g=x(r,g.lineEndExtension);k.viewPortMin=m;k.viewPortMax=l;n=k.scrollRatio=1/n;s=k.windowedCanvasWidth=e.getAxisPosition(s);e=k.fullCanvasWidth=e.getAxisPosition(l-m)-s;!1!==a.scrollEnabled?(u.draw(b-B,t+A+r+v-2,{width:q+B+g,scrollRatio:n,roundEdges:d.isRoundEdges,fullCanvasWidth:e,windowedCanvasWidth:s,
scrollPosition:y}),!f&&function(){var a;na.eve.on("raphael.scroll.start."+u.node.id,function(b){a=b;ea.raiseEvent("scrollstart",{scrollPosition:b},c.chartInstance)});na.eve.on("raphael.scroll.end."+u.node.id,function(b){ea.raiseEvent("scrollend",{prevScrollPosition:a,scrollPosition:b},c.chartInstance)})}()):u&&u.node&&u.node.hide()},_drawDataset:function(){this._setClipping();e.mscartesian._drawDataset.call(this)},_setClipping:function(){var c=this.config,a=this.graphics.datasetGroup,b=this.graphics.datalabelsGroup,
d=this.graphics.trackerGroup,e=c.viewPortConfig,g=this.graphics.sumLabelsLayer,f=e.scaleX,k=this.get("config","animationObj"),l=k.duration,m=k.dummyObj,s=k.animObj,k=k.animType,e=e.x,c=c.height,u=this.components.canvas.config.clip["clip-canvas"],u=u&&u.slice(0)||[];this.config.clipSet?(a.animateWith(m,s,{"clip-rect":u},l,k),b.animateWith(m,s,{"clip-rect":u},l,k),d.attr({"clip-rect":u}),u[3]=c,u[1]=0,g&&g.animateWith(m,s,{"clip-rect":u},l,k)):(a.attr({"clip-rect":u}),b.attr({"clip-rect":u}),d.attr({"clip-rect":u}),
u[3]=c,u[1]=0,g&&g.attr({"clip-rect":u}));a.attr({transform:"T"+-(e*f)+",0"});b.attr({transform:"T"+-(e*f)+",0"});d.attr({transform:"T"+-(e*f)+",0"});g&&g.attr({transform:"T"+-(e*f)+",0"});this.config.clipSet=!0},configure:function(){var c=this.jsonData.chart,a;e.mscolumn2d.configure.call(this);a=this.config;a.scrollToEnd=x(c.scrolltoend,0);a.lastScrollPosition=void 0}},e.scrollbase);e("scrollarea2d",{friendlyName:"Scrollable Multi-series Area Chart",tooltipConstraint:"plot",canvasborderthickness:1,
creditLabel:!1,hasScroll:!0,defaultDatasetType:"scrollarea2d",applicableDSList:{scrollarea2d:!0},avgScrollPointWidth:75,defaultPlotShadow:0,_setAxisScale:function(){var c=this.config,a=this.components.xAxis[0].getCategoryLen(),b=this.jsonData,d=c.scrollOptions||(c.scrollOptions={}),e;e=c.lastScrollPosition;var g=c.scrollToEnd,f=c.canvasWidth,b=x(b.chart.numvisibleplot,pa(c.width/this.avgScrollPointWidth));2<=b&&b<a?(c.viewPortConfig.scaleX=a/=b,e=f*(a-1)*(void 0!==e?e:g?1:0),c.viewPortConfig.x=e/
a,d.vxLength=b,c.scrollEnabled=!0):c.scrollEnabled=!1}},e.scrollcolumn2d);e("scrollline2d",{friendlyName:"Scrollable Multi-series Line Chart",tooltipConstraint:"plot",canvasborderthickness:1,defaultDatasetType:"line",creditLabel:!1,avgScrollPointWidth:75},e.scrollarea2d);e("scrollstackedcolumn2d",{friendlyName:"Scrollable Stacked Column Chart",canvasborderthickness:1,tooltipConstraint:"plot",avgScrollPointWidth:75,creditLabel:!1,isStacked:!0},e.scrollcolumn2d);e("scrollcombi2d",{friendlyName:"Scrollable Combination Chart",
tooltipConstraint:"plot",hasScroll:!0,canvasborderthickness:1,avgScrollPointWidth:40,applicableDSList:{area:!0,line:!0,column:!0},creditLabel:!1,_createDatasets:e.mscombi2d},e.scrollcolumn2d);e("scrollcombidy2d",{friendlyName:"Scrollable Dual Y-Axis Combination Chart",tooltipConstraint:"plot",canvasborderthickness:1,avgScrollPointWidth:40,hasScroll:!0,_drawDataset:e.scrollcolumn2d,updateManager:e.scrollcolumn2d,_setAxisScale:e.scrollcolumn2d,_createToolBox:e.scrollcolumn2d,_scrollBar:e.scrollcolumn2d,
_manageScrollerPosition:e.scrollcolumn2d,drawScrollBar:e.scrollcolumn2d,_setClipping:e.scrollcolumn2d,creditLabel:!1,configure:e.scrollcolumn2d},e.mscombidy2d);e("scatter",{friendlyName:"Scatter Chart",isXY:!0,standaloneInit:!0,hasLegend:!0,defaultZeroPlaneHighlighted:!1,creditLabel:!1,defaultDatasetType:"Scatter",applicableDSList:{Scatter:!0}},e.scatterBase);Ia(U.eventList,{zoomedOut:"FC_ZoomedOut"});e("bubble",{friendlyName:"Bubble Chart",standaloneInit:!0,defaultDatasetType:"bubble",creditLabel:!1,
applicableDSList:{bubble:!0},getDataLimits:function(){var c=this.components.dataset,a,b,d,e=-Infinity,g=Infinity;a=0;for(d=c.length;a<d;a++)b=c[a],b=b.getDataLimits(),e=ua(e,b.zMax||-Infinity),g=sa(g,b.zMin||Infinity);e=-Infinity===e?0:e;g=Infinity===g?0:g;return{zMax:e,zMin:g}}},e.scatter);na._availableAnimAttrs&&na._availableAnimAttrs.cx&&(na._availableAnimAttrs.innerR=na._availableAnimAttrs.depth=na._availableAnimAttrs.radiusYFactor=na._availableAnimAttrs.start=na._availableAnimAttrs.end=na._availableAnimAttrs.cx);
fa.prototype={configure:function(c,a,b,d){var e=this.linkedItems.chart,g=e.get("components","paper"),e=e.get("graphics","datasetGroup");"object"===typeof c&&(c=c.depth,a=c.hasOnePoint,b=c.use3DLighting,d=c.isDoughnut);this.renderer||(this.renderer=g);this.hasOnePoint=a;this.use3DLighting=b;this.isDoughnut=d;this.depth=c;!this.bottomBorderGroup&&(this.bottomBorderGroup=g.group("bottom-border",e));this.bottomBorderGroup.attr({transform:"t0,"+c});!this.slicingWallsBackGroup&&(this.slicingWallsBackGroup=
g.group("slicingWalls-back-Side",e));!this.slicingWallsFrontGroup&&(this.slicingWallsFrontGroup=g.group("slicingWalls-front-Side",e));!this.topGroup&&(this.topGroup=g.group("top-Side",e));!this.pointElemStore&&(this.pointElemStore=[]);!this.slicingWallsArr&&(this.slicingWallsArr=[]);this.moveCmdArr=["M"];this.lineCmdArr=["L"];this.closeCmdArr=["Z"];this.colorObjs=[]},getArcPath:function(c,a,b,d,e,g,f,k,l,m){return b==e&&d==g?[]:["A",f,k,0,m,l,e,g]},_parseSliceColor:function(c,a,b){var d,e,g,f,k,l,
m,s,u,t,A,B,S,x,H;H=3;var y=(d=this.use3DLighting)?L:N,ha=b.radiusYFactor,G=b.cx,P=b.cy,O=b.r,ba=O*ha,U=b.innerR||0,Z=G+O,I=G-O,w=G+U,C=G-U;a=a||100;b=a/2;y[c]&&y[c][a]?y=y[c][a]:(y[c]||(y[c]={}),y[c][a]||(y[c][a]={}),y=y[c][a],d?(d=q(c,80),e=q(c,75),l=v(c,85),m=v(c,70),s=v(c,40),u=v(c,50),v(c,30),t=v(c,65),q(c,85),g=q(c,69),f=q(c,75),k=q(c,95)):(H=10,d=q(c,90),e=q(c,87),l=v(c,93),m=v(c,87),s=v(c,80),t=u=v(c,85),v(c,80),k=q(c,85),g=q(c,75),f=q(c,80)),A=e+n+l+n+m+n+l+n+e,S=a+n+a+n+a+n+a+n+a,B=e+n+
c+n+l+n+c+n+e,x=b+n+b+n+b+n+b+n+b,s=e+n+c+n+s+n+c+n+e,g=f+n+l+n+u+n+l+n+g,f="FFFFFF"+n+"FFFFFF"+n+"FFFFFF"+n+"FFFFFF"+n+"FFFFFF",H=0+n+b/H+n+a/H+n+b/H+n+0,y.top=za?{FCcolor:{gradientUnits:"userSpaceOnUse",radialGradient:!0,color:t+n+k,alpha:a+n+a,ratio:"0,100"}}:{FCcolor:{gradientUnits:"objectBoundingBox",color:m+n+m+n+l+n+e,alpha:a+n+a+n+a+n+a,angle:-72,ratio:"0,8,15,77"}},y.frontOuter={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:g,alpha:S,angle:0,ratio:"0,20,15,15,50"}},y.backOuter=
{FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:s,alpha:x,angle:0,ratio:"0,62,8,8,22"}},y.frontInner={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:B,alpha:x,angle:0,ratio:"0,25,5,5,65"}},y.backInner={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:A,alpha:S,angle:0,ratio:"0,62,8,8,22"}},y.topBorder={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:f,alpha:H,angle:0,ratio:"0,20,15,15,50"}},y.topInnerBorder={FCcolor:{gradientUnits:"userSpaceOnUse",y1:0,y2:0,color:f,
alpha:H,angle:0,ratio:"0,50,15,15,20"}},y.bottom=ka(r(c,b)),y.startSlice=ka(r(d,a)),y.endSlice=ka(r(d,a)));if(y.cx!==G||y.cy!==P||y.rx!==O||y.radiusYFactor!==ha||y.innerRx!==U)za&&(y.top.FCcolor.cx=G,y.top.FCcolor.cy=P,y.top.FCcolor.r=O,y.top.FCcolor.fx=G-.3*O,y.top.FCcolor.fy=P*****ba),y.topBorder.FCcolor.x1=y.backOuter.FCcolor.x1=y.frontOuter.FCcolor.x1=I,y.topBorder.FCcolor.x2=y.backOuter.FCcolor.x2=y.frontOuter.FCcolor.x2=Z,y.topInnerBorder.FCcolor.x1=y.backInner.FCcolor.x1=y.frontInner.FCcolor.x1=
C,y.topInnerBorder.FCcolor.x2=y.backInner.FCcolor.x2=y.frontInner.FCcolor.x2=w,y.cx=G,y.cy=P,y.rx=O,y.radiusYFactor=ha,y.innerRx=U;return y},rotate:function(a){var b=this.pointElemStore,d=0,e=b.length,f;if(!this.hasOnePoint){for(;d<e;d+=1)f=b[d],f=f._confObject,f.start+=a,f.end+=a,this._setSliceShape(f);this.refreshDrawing()}},removeSlice:function(a){var b=this.pointElemStore,d=a._confObject.elements,e=this.slicingWallsArr,f;f=b.length;var g;for(--f;0<=f;--f)g=b[f],g===a&&b.splice(f,1);f=e.length;
for(--f;0<=f;--f)b=e[f],b!==d.startSlice&&b!==d.frontOuter1&&b!==d.frontOuter&&b!==d.backInner&&b!==d.endSlice||e.splice(f,1);a.hide&&a.hide();this._slicePool||(this._slicePool=[]);this._slicePool.push(a);this.refreshDrawing()},useSliceFromPool:function(){var a=this._slicePool||(this._slicePool=[]),b=this.slicingWallsArr,d=!1;a.length&&(d=a.shift(),this.pointElemStore.push(d),d.show(),a=d._confObject.elements,b.push(a.startSlice,a.frontOuter1,a.frontOuter),a.backInner&&b.push(a.backInner),b.push(a.endSlice));
return d},refreshDrawing:function(){var a=function(a,c){return a._conf.index-c._conf.index||a._conf.cIndex-c._conf.cIndex||a._conf.isStart-c._conf.isStart||a._conf.si-c._conf.si};return function(){var b=this.slicingWallsArr,e=0,f,l=b.length,g,n,q,r,v=this.slicingWallsFrontGroup,s=this.slicingWallsBackGroup;b.sort(a);a:{var u=b[0]&&b[0]._conf.index,t,A;r=u<=d;g=1;for(f=b.length;g<f;g+=1)if(A=b[g]._conf.index,t=A<=d,t!=r||A<u)break a;g=0}for(;e<l;e+=1,g+=1)g===l&&(g=0),f=b[g],r=f._conf.index,r<k?v.appendChild(f):
r<=d?(n?f.insertBefore(n):v.appendChild(f),n=f):r<=m?(q?f.insertBefore(q):s.appendChild(f),q=f):s.appendChild(f)}}(),_setSliceShape:function(a,b){var e=this.getArcPath,f=a.start,p=a.end,g=O(f),n=O(p),q,r,v,s,u,t,A,B,S,H,x,y,P,G,L,N,U,ba=this.isDoughnut,Z=a.radiusYFactor,I=a.cx,w=a.cy,C=a.r,z=C*Z,T=C+(za?-1:2),Y=z+(za?-1:2),E=a.innerR||0,Q=E*Z,F=this.depth,M=F+w,R=I+C,V=I-C,$=I+E,da=I-E,ja=w-z,X=["M",da,ja,"L",da,M+z,"Z"],Z=a.elements,ga,ca,W,ia,fa,aa="path",ea=(g+n)/2,ka=g>n;r=ra(g);v=qa(g);s=ra(n);
u=qa(n);t=I+C*r;A=w+z*v;B=I+T*r;S=w+Y*v;ga=A+F;ca=I+C*s;W=w+z*u;H=I+T*s;x=w+Y*u;ia=W+F;ba?(y=I+E*r,P=w+Q*v,N=P+F,G=I+E*s,L=w+Q*u,U=L+F,a.startSlice=["M",t,A,"L",t,ga,y,N,y,P,"Z"],a.endSlice=["M",ca,W,"L",ca,ia,G,U,G,L,"Z"]):(a.startSlice=["M",t,A,"L",t,ga,I,M,I,w,"Z"],a.endSlice=["M",ca,W,"L",ca,ia,I,M,I,w,"Z"]);za?(q=(g>n?l:0)+n-g,a.clipTopPath=ba?[["M",t,A,"A",C,z,0,q>d?1:0,1,ca,W,"L",G,L,"A",E,Q,0,q>d?1:0,0,y,P,"Z"]]:[["M",t,A,"A",C,z,0,q>d?1:0,1,ca,W,"L",I,w,"Z"]],a.clipOuterFrontPath1=[X],a.clipTopBorderPath=
[["M",B,S,"A",T,Y,0,q>d?1:0,1,H,x,"L",ca,W,ca,W+1,"A",C,z,0,q>d?1:0,0,t,A+1,"L",t,A,"Z"]],f!=p?g>n?g<d?(a.clipOuterFrontPath=[["M",R,w,"A",C,z,0,0,1,ca,W,"v",F,"A",C,z,0,0,0,R,w+F,"Z"]],a.clipOuterFrontPath1=[["M",V,w,"A",C,z,0,0,0,t,A,"v",F,"A",C,z,0,0,1,V,w+F,"Z"]],a.clipOuterBackPath=[["M",R,w,"A",C,z,0,1,0,V,w,"v",F,"A",C,z,0,1,1,R,w+F,"Z"]],ba&&(a.clipInnerBackPath=[["M",$,w,"A",E,Q,0,1,0,da,w,"v",F,"A",E,Q,0,1,1,$,w+F,"Z"]],a.clipInnerFrontPath=[["M",$,w,"A",E,Q,0,0,1,G,L,"v",F,"A",E,Q,0,0,
0,$,w+F,"Z","M",da,w,"A",E,Q,0,0,0,y,P,"v",F,"A",E,Q,0,0,1,da,w+F,"Z"]])):n>d?(a.clipOuterFrontPath=[["M",R,w,"A",C,z,0,1,1,V,w,"v",F,"A",C,z,0,1,0,R,w+F,"Z"]],a.clipOuterBackPath=[["M",V,w,"A",C,z,0,0,1,ca,W,"v",F,"A",C,z,0,0,0,V,w+F,"Z","M",R,w,"A",C,z,0,0,0,t,A,"v",F,"A",C,z,0,0,1,R,w+F,"Z"]],ba&&(a.clipInnerFrontPath=[["M",$,w,"A",E,Q,0,1,1,da,w,"v",F,"A",E,Q,0,1,0,$,w+F,"Z"]],a.clipInnerBackPath=[["M",da,w,"A",E,Q,0,0,1,G,L,"v",F,"A",E,Q,0,0,0,da,w+F,"Z","M",$,w,"A",E,Q,0,0,0,y,P,"v",F,"A",E,
Q,0,0,1,$,w+F,"Z"]])):(a.clipOuterFrontPath=[["M",R,w,"A",C,z,0,0,1,ca,W,"v",F,"A",C,z,0,0,0,R,w+F,"Z"]],a.clipOuterBackPath=[["M",t,A,"A",C,z,0,0,1,R,w,"v",F,"A",C,z,0,0,0,t,ga,"Z"]],ba&&(a.clipInnerFrontPath=[["M",$,w,"A",E,Q,0,0,1,G,L,"v",F,"A",E,Q,0,0,0,$,w+F,"Z"]],a.clipInnerBackPath=[["M",y,P,"A",E,Q,0,0,1,$,w,"v",F,"A",E,Q,0,0,0,y,N,"Z"]])):g<d?n>d?(a.clipOuterFrontPath=[["M",t,A,"A",C,z,0,0,1,V,w,"v",F,"A",C,z,0,0,0,t,ga,"Z"]],a.clipOuterBackPath=[["M",V,w,"A",C,z,0,0,1,ca,W,"v",F,"A",C,z,
0,0,0,V,w+F,"Z"]],ba&&(a.clipInnerFrontPath=[["M",y,P,"A",E,Q,0,0,1,da,w,"v",F,"A",E,Q,0,0,0,y,N,"Z"]],a.clipInnerBackPath=[["M",da,w,"A",E,Q,0,0,1,G,L,"v",F,"A",E,Q,0,0,0,da,w+F,"Z"]])):(a.clipOuterFrontPath=[["M",t,A,"A",C,z,0,0,1,ca,W,"v",F,"A",C,z,0,0,0,t,ga,"Z"]],a.clipOuterBackPath=[X],ba&&(a.clipInnerFrontPath=[["M",y,P,"A",E,Q,0,0,1,G,L,"v",F,"A",E,Q,0,0,0,y,N,"Z"]],a.clipInnerBackPath=[X])):(a.clipOuterFrontPath=[X],a.clipOuterBackPath=[["M",t,A,"A",C,z,0,0,1,ca,W,"v",F,"A",C,z,0,0,0,t,ga,
"Z"]],ba&&(a.clipInnerFrontPath=[X],a.clipInnerBackPath=[["M",y,P,"A",E,Q,0,0,1,G,L,"v",F,"A",E,Q,0,0,0,y,N,"Z"]])):a.clipOuterFrontPath=a.clipOuterBackPath=a.clipInnerBackPath=a.clipInnerFrontPath=[X],aa="litepath",a.clipBottomBorderPath=a.clipTopPath,a.startSlice=[a.startSlice],a.endSlice=[a.endSlice]):(T=this.moveCmdArr,Y=this.lineCmdArr,r=this.closeCmdArr,F=[I,w],v=[V,w],ja=[I,ja],s=[R,w],u=[I,w+z],X=[V,M],fa=[R,M],B=[da,w],S=[$,w],H=[da,M],x=[$,M],a.clipOuterFrontPath1=[],f!=p?(g>n?g<d?(f=e(I,
w,t,A,V,w,C,z,1,0),p=e(I,w,V,w,R,w,C,z,1,0),W=e(I,w,R,w,ca,W,C,z,1,0),a.clipOuterBackPath=T.concat(v,p,Y,fa,e(I,M,R,M,V,M,C,z,0,0),r),a.clipOuterFrontPath1=T.concat([t,A],f,Y,X,e(I,M,V,M,t,ga,C,z,0,0),r),a.clipOuterFrontPath=T.concat(s,W,Y,[ca,ia],e(I,M,ca,ia,R,M,C,z,0,0),r),a.clipTopBorderPath=T.concat([t,A],f,p,W),ba?(C=e(I,w,G,L,$,w,E,Q,0,0),z=e(I,w,$,w,da,w,E,Q,0,0),P=e(I,w,da,w,y,P,E,Q,0,0),a.clipInnerBackPath=T.concat(S,z,Y,H,e(I,M,da,M,$,M,E,Q,1,0),r),a.clipInnerFrontPath=T.concat(B,P,Y,[y,
N],e(I,M,y,N,da,M,E,Q,1,0),r,T,[G,L],C,Y,x,e(I,M,$,M,G,U,E,Q,1,0),r),a.clipTopPath=a.clipTopBorderPath.concat(Y,[G,L],C,z,P,r),a.clipTopBorderPath=a.clipTopBorderPath.concat(T,[G,L],C,z,P)):a.clipTopPath=a.clipTopBorderPath.concat(Y,F,r)):n>d?(f=e(I,w,t,A,R,w,C,z,1,0),p=e(I,w,R,w,V,w,C,z,1,0),W=e(I,w,V,w,ca,W,C,z,1,0),a.clipOuterFrontPath=T.concat(s,p,Y,X,e(I,M,V,M,R,M,C,z,0,0),r),a.clipOuterBackPath=T.concat([t,A],f,Y,fa,e(I,M,R,M,t,ga,C,z,0,0),r,T,v,W,Y,[ca,ia],e(I,M,ca,ia,V,M,C,z,0,0),r),a.clipTopBorderPath=
T.concat([t,A],f,p,W),ba?(C=e(I,w,G,L,da,w,E,Q,0,0),z=e(I,w,da,w,$,w,E,Q,0,0),P=e(I,w,$,w,y,P,E,Q,0,0),a.clipInnerFrontPath=T.concat(B,z,Y,x,e(I,M,$,M,da,M,E,Q,1,0),r),a.clipInnerBackPath=T.concat(S,P,Y,[y,N],e(I,M,y,N,$,M,E,Q,1,0),r,T,[G,L],C,Y,H,e(I,M,da,M,G,U,E,Q,1,0),r),a.clipTopPath=a.clipTopBorderPath.concat(Y,[G,L],C,z,P,r),a.clipTopBorderPath=a.clipTopBorderPath.concat(T,[G,L],C,z,P)):a.clipTopPath=a.clipTopBorderPath.concat(Y,F,r)):(f=e(I,w,t,A,R,w,C,z,1,0),p=e(I,w,R,w,ca,W,C,z,1,0),a.clipOuterFrontPath=
T.concat(s,p,Y,[ca,ia],e(I,M,ca,ia,R,M,C,z,0,0),r),a.clipOuterBackPath=T.concat([t,A],f,Y,fa,e(I,M,R,M,t,ga,C,z,0,0),r),a.clipTopBorderPath=T.concat([t,A],f,p),ba?(C=e(I,w,G,L,$,w,E,Q,0,0),z=e(I,w,$,w,y,P,E,Q,0,0),a.clipInnerFrontPath=T.concat([G,L],C,Y,x,e(I,M,$,M,G,U,E,Q,1,0),r),a.clipInnerBackPath=T.concat(S,z,Y,[y,N],e(I,M,y,N,$,M,E,Q,1,0),r),a.clipTopPath=a.clipTopBorderPath.concat(Y,[G,L],C,z,r),a.clipTopBorderPath=a.clipTopBorderPath.concat(T,[G,L],C,z)):a.clipTopPath=a.clipTopBorderPath.concat(Y,
F,r)):g<d?n>d?(f=e(I,w,t,A,V,w,C,z,1,0),p=e(I,w,V,w,ca,W,C,z,1,0),a.clipOuterBackPath=T.concat(v,p,Y,[ca,ia],e(I,M,ca,ia,V,M,C,z,0,0),r),a.clipOuterFrontPath=T.concat([t,A],f,Y,X,e(I,M,V,M,t,ga,C,z,0,0),r),a.clipTopBorderPath=T.concat([t,A],f,p),ba?(C=e(I,w,G,L,da,w,E,Q,0,0),z=e(I,w,da,w,y,P,E,Q,0,0),a.clipInnerBackPath=T.concat([G,L],C,Y,H,e(I,M,da,M,G,U,E,Q,1,0),r),a.clipInnerFrontPath=T.concat(B,z,Y,[y,N],e(I,M,y,N,da,M,E,Q,1,0),r),a.clipTopPath=a.clipTopBorderPath.concat(Y,[G,L],C,z,r),a.clipTopBorderPath=
a.clipTopBorderPath.concat(T,[G,L],C,z)):a.clipTopPath=a.clipTopBorderPath.concat(Y,F,r)):(f=e(I,w,t,A,ca,W,C,z,1,0),a.clipOuterBackPath=T.concat([t,A]),a.clipTopBorderPath=a.clipOuterBackPath.concat(f),a.clipOuterFrontPath=a.clipTopBorderPath.concat(Y,[ca,ia],e(I,M,ca,ia,t,ga,C,z,0,0),r),ba?(C=e(I,w,G,L,y,P,E,Q,0,0),a.clipInnerBackPath=T.concat([G,L]),a.clipTopPath=a.clipTopBorderPath.concat(Y,[G,L],C,r),a.clipTopBorderPath=a.clipTopBorderPath.concat(T,[G,L],C),a.clipInnerFrontPath=a.clipInnerBackPath.concat(C,
Y,[y,N],e(I,M,y,N,G,U,E,Q,1,0),r)):a.clipTopPath=a.clipTopBorderPath.concat(Y,F,r)):(f=e(I,w,t,A,ca,W,C,z,1,0),a.clipOuterFrontPath=T.concat([t,A]),a.clipTopBorderPath=a.clipOuterFrontPath.concat(f),a.clipOuterBackPath=a.clipTopBorderPath.concat(Y,[ca,ia],e(I,M,ca,ia,t,ga,C,z,0,0),r),ba?(C=e(I,w,G,L,y,P,E,Q,0,0),a.clipInnerFrontPath=T.concat([G,L]),a.clipTopPath=a.clipTopBorderPath.concat(Y,[G,L],C,r),a.clipTopBorderPath=a.clipTopBorderPath.concat(a.clipInnerFrontPath,C),a.clipInnerBackPath=a.clipInnerFrontPath.concat(C,
Y,[y,N],e(I,M,y,N,G,U,E,Q,1,0),r)):a.clipTopPath=a.clipTopBorderPath.concat(Y,F,r)),f=T.concat(v,Y,s),C=T.concat(ja,Y,u),a.clipTopPath=a.clipTopPath.concat(f,C),a.clipOuterFrontPath=a.clipOuterFrontPath.concat(f),a.clipOuterFrontPath1=a.clipOuterFrontPath1.concat(f),a.clipOuterBackPath=a.clipOuterBackPath.concat(f),ba&&(C=T.concat(B,Y,S),a.clipInnerFrontPath=a.clipInnerFrontPath.concat(C),a.clipInnerBackPath=a.clipInnerBackPath.concat(C))):(a.clipTopPath=a.clipOuterFrontPath=a.clipOuterBackPath=[],
ba&&(a.clipInnerFrontPath=a.clipInnerBackPath=[])),a.clipBottomBorderPath=a.clipTopBorderPath);b||(Z.startSlice._conf.index=g,Z.endSlice._conf.index=n,Z.backOuter._conf.index=y=ka&&(g<=m||n>m)||g<=m&&n>m?m:g>d?g:n,Z.frontOuter._conf.index=e=n<=k?n:g>n||g<=k?k:g,Z.frontOuter1._conf.index=g,Z.frontOuter1._conf.cIndex=d,g>n?(Z.backOuter._conf.cIndex=g<m?m:l,Z.startSlice._conf.cIndex=g<d?(g+d)/2:(g+l)/2,Z.endSlice._conf.cIndex=Z.frontOuter._conf.cIndex=0):Z.backOuter._conf.cIndex=Z.startSlice._conf.cIndex=
Z.endSlice._conf.cIndex=Z.frontOuter._conf.cIndex=ea,q>d?Z.frontOuter1.show().attr(aa,a.clipOuterFrontPath1):Z.frontOuter1.hide(),a.thisElement._attr(aa,a.clipTopPath),Z.bottom.attr(aa,a.clipTopPath),Z.bottomBorder.attr(aa,a.clipBottomBorderPath),Z.topBorder&&Z.topBorder.attr(aa,a.clipTopBorderPath),Z.frontOuter.attr(aa,a.clipOuterFrontPath),Z.backOuter.attr(aa,a.clipOuterBackPath),ba&&(Z.backInner.attr(aa,a.clipInnerBackPath),Z.frontInner.attr(aa,a.clipInnerFrontPath),Z.backInner._conf.index=y,Z.frontInner._conf.index=
e,g>n?(Z.backInner._conf.cIndex=l,Z.frontInner._conf.cIndex=0):Z.backInner._conf.cIndex=Z.frontInner._conf.cIndex=ea),this.hasOnePoint?(Z.startSlice.hide(),Z.endSlice.hide()):(Z.startSlice.attr(aa,a.startSlice).show(),Z.endSlice.attr(aa,a.endSlice).show()))},_setSliceCosmetics:function(a){var b=a.thisElement,d=a.showBorderEffect,e=a.elements,f=r(a.borderColor,x(a.borderAlpha,a.alpha)),g=a.borderWidth,k;a.color&&(a=this._parseSliceColor(a.color,a.alpha,a),za?(k={fill:ka(a.top),"stroke-width":0},d?
e.topBorder.show().attr({fill:ka(a.topBorder),"stroke-width":0}):(e.topBorder.hide(),k.stroke=f,k["stroke-width"]=g),b._attr(k)):(b._attr({fill:ka(a.top),"stroke-width":0}),e.topBorder.attr({stroke:f,"stroke-width":g})),e.bottom.attr({fill:ka(a.bottom)}),e.bottomBorder.attr({stroke:f,"stroke-width":g}),e.frontOuter.attr({fill:ka(a.frontOuter)}),e.frontOuter1.attr({fill:ka(a.frontOuter)}),e.backOuter.attr({fill:ka(a.backOuter)}),e.startSlice.attr({fill:ka(a.startSlice),stroke:f,"stroke-width":g}),
e.endSlice.attr({fill:ka(a.endSlice),stroke:f,"stroke-width":g}),this.isDoughnut&&(e.frontInner.attr({fill:ka(a.frontInner)}),e.backInner.attr({fill:ka(a.backInner)})))},createSlice:function(){var a={stroke:!0,strokeWidth:!0,"stroke-width":!0,dashstyle:!0,"stroke-dasharray":!0,translateX:!0,translateY:!0,"stroke-opacity":!0,fill:!0,opacity:!0,transform:!0,ishot:!0,cursor:!0,start:!0,end:!0,color:!0,alpha:!0,borderColor:!0,borderAlpha:!0,borderWidth:!0,rolloverProps:!0,showBorderEffect:!0,positionIndex:!0,
cx:!0,cy:!0,radiusYFactor:!0,r:!0,innerR:!0},b=function(b,d){var e,g,f=this,h=f._confObject,k={},l=h.elements,m,p,n,r=h.Pie3DManager,q;"string"===typeof b&&void 0!==d&&null!==d&&(e=b,b={},b[e]=d);if(b&&"string"!==typeof b){for(e in b)if(g=b[e],a[e])if(h[e]=g,"ishot"===e||"cursor"===e||"transform"===e)k[e]=g,q=!0;else if("start"===e||"end"===e||"cx"===e||"cy"===e||"radiusYFactor"===e||"r"===e||"innerR"===e)p=!0;else{if("color"===e||"alpha"===e||"borderColor"===e||"borderAlpha"===e||"borderWidth"===
e)n=!0}else f._attr(e,g);p&&(r._setSliceShape(h),r.refreshDrawing());(n||p)&&r._setSliceCosmetics(h);if(q){for(m in l)l[m].attr(k);f._attr(k)}}else f=a[b]?h[b]:f._attr(b);return f},d=function(a,b){var c=this._confObject.elements,d;for(d in c)c[d].on(a,b);return this._on(a,b)},e=function(a,b,c){var d,e=this._confObject.elements,g=-1<Da.navigator.userAgent.toLowerCase().indexOf("android");for(d in e)g?"topBorder"!==d&&"frontOuter"!==d&&"startSlice"!==d&&"endSlice"!==d||e[d].drag(a,b,c):e[d].drag(a,
b,c);return this._drag(a,b,c)},f=function(){var a=this._confObject.elements,b;for(b in a)a[b].hide();return this._hide()},g=function(){var a=this._confObject.elements,b;for(b in a)a[b].show();return this._show()},k=function(){var a=this._confObject,b=a.elements,c;for(c in b)b[c].destroy();za&&(a.clipTop.destroy(),a.clipOuterFront.destroy(),a.clipOuterBack.destroy(),a.clipOuterFront1&&a.clipOuterFront1.destroy(),a.clipInnerFront&&a.clipInnerFront.destroy(),a.clipInnerBack&&a.clipInnerBack.destroy());
return this._destroy()},l=function(a){var b=this._confObject.elements,c;for(c in b)b[c].tooltip(a);return this._tooltip(a)},m=function(a,b){var c=this._confObject.elements,d;if(void 0===b)return this._data(a);for(d in c)c[d].data(a,b);return this._data(a,b)},n=0;return function(){var a=this.renderer,c,r={elements:{},Pie3DManager:this},A=this.slicingWallsArr,q=r.elements,v=za?"litepath":"path";c=a[v](this.topGroup);c._confObject=r;r.thisElement=c;c._destroy=c.destroy;c.destroy=k;c._show=c.show;c.show=
g;c._hide=c.hide;c.hide=f;c._on=c.on;c.on=d;c._drag=c.drag;c.drag=e;c._attr=c.attr;c.attr=b;c._tooltip=c.tooltip;c.tooltip=l;c._data=c.data;c.data=m;this.pointElemStore.push(c);q.topBorder=a[v](this.topGroup);q.bottom=a[v](this.bottomBorderGroup).attr({"stroke-width":0});q.bottomBorder=a[v](this.bottomBorderGroup);q.frontOuter=a[v](this.slicingWallsFrontGroup).attr({"stroke-width":0});q.backOuter=a[v](this.slicingWallsFrontGroup).attr({"stroke-width":0});q.startSlice=a[v](this.slicingWallsFrontGroup);
q.endSlice=a[v](this.slicingWallsFrontGroup);q.frontOuter1=a[v](this.slicingWallsFrontGroup).attr({"stroke-width":0});q.frontOuter._conf={si:n,isStart:.5};q.frontOuter1._conf={si:n,isStart:.5};q.startSlice._conf={si:n,isStart:0};q.endSlice._conf={si:n,isStart:1};q.backOuter._conf={si:n,isStart:.4};A.push(q.startSlice,q.frontOuter1,q.frontOuter,q.backOuter,q.endSlice);this.isDoughnut&&(q.frontInner=a[v](this.slicingWallsFrontGroup).attr({"stroke-width":0}),q.backInner=a[v](this.slicingWallsFrontGroup).attr({"stroke-width":0}),
q.backInner._conf={si:n,isStart:.5},q.frontInner._conf={si:n,isStart:.4},A.push(q.frontInner,q.backInner));n+=1;return c}}()};fa.prototype.constructor=fa},[3,2,2,"sr4"]]);
FusionCharts.register("module",["private","modules.renderer.js-zoomline",function(){var ya=this,O=ya.hcLib,Ja=O.hashify,fa=ya.window,ea=fa.document,U=fa.Image,Pa=fa.MouseEvent,Da=/msie/i.test(fa.navigator.userAgent)&&!fa.opera,la=O.chartAPI,na=O.extend2,Ga=O.addEvent,R=O.pluck,x=O.pluckNumber,Sa=O.getFirstColor,Ia=O.graphics.convertColor,ka=O.bindSelectionEvent,za=O.parseUnsafeString,Ta=O.componentDispose,oa=O.Raphael,Ea=O.toRaphaelColor,Ca=O.hasTouch,Ka=O.plotEventHandler,Ha=O.getMouseCoordinate,
Aa="rgba(192,192,192,"+(Da?.002:1E-6)+")",ma=fa.Math,qa=ma.ceil,ra=ma.floor,Oa=ma.round,ja=ma.max,sa=ma.min,ua=ma.cos,va=ma.sin,wa=fa.parseFloat,xa=fa.parseInt,pa;na(O.eventList,{zoomed:"FC_Zoomed",pinned:"FC_Pinned",resetzoomchart:"FC_ResetZoomChart"});la("zoomline",{standaloneInit:!0,canvasborderthickness:1,defaultDatasetType:"zoomline",applicableDSList:{zoomline:!0},friendlyName:"Zoomable and Panable Multi-series Line Chart",creditLabel:!1,_drawAxis:function(){var a=this.components.yAxis||[],b,
d;b=0;for(d=a.length;b<d;b++)a[b].draw()},_setCategories:function(){var a=this.config,b=this.jsonData,d=this.components.xAxis,k,l,m;l=a.cdmchar;var f=b.categories&&b.categories[0].category||[];if((a.cdm||"string"===typeof f)&&f.split){a=f.split(l);k=[];l=0;for(m=a.length;l<m;l+=1)k.push({label:a[l]});this.config.categories=b.categories[0].category=k}d[0].setAxisPadding(0,0);d[0].setCategory(k||f)},_createDatasets:function(){var a,b,d,k,l,m,f,B,q;l={};var v=this.config;a=this.components;B=this.jsonData;
var r=B.dataset,S=r&&r.length,P=v.cdmchar,e=v.cdm,n=this.defaultDatasetType,H=this.applicableDSList,v=this.components.legend.components.items||[];B=B.categories&&B.categories[0].category;r&&B||this.setChartMessage();this.config.categories=B;B=a.dataset||(a.dataset=[]);q=B.length;for(a=0;a<S;a++){f=r[a];if(e&&f.data&&f.data.split){m=f.data.split(P);k=[];b=0;for(d=m.length;b<d;b++)k.push({value:m[b]});f.data=k}b=f.parentyaxis||"";b=(b=this.isDual&&"s"===b.toLowerCase()?R(f.renderas,this.sDefaultDatasetType):
R(f.renderas,n))&&b.toLowerCase();H[b]||(b=n);if(d=FusionCharts.get("component",["dataset",b]))void 0===l[b]?l[b]=0:l[b]++,(b=B[a])?(d=(b.JSONData.data||[]).length,k=(f.data||[]).length,d>k&&b.removeData(k,d-k,!1),B[a].JSONData=f,B[a].configure(),B[a]._deleteGridImages&&B[a]._deleteGridImages()):(b=new d,B.push(b),b.chart=this,b.index=a,b.init(f))}if(q>S){l=q-S;b=a;for(S=l+a;b<S;b++)Ta.call(B[b]);B.splice(a,l);v.splice(a,l)}},isWithinCanvas:function(a,b){var d=O.getMouseCoordinate(b.get("linkedItems",
"container"),a),k=d.chartX,l=d.chartY,m=b.get("config"),f=m.canvasLeft,B=m.canvasTop,q=m.canvasLeft+m.canvasWidth,m=m.canvasHeight+m.canvasTop;d.insideCanvas=!1;d.originalEvent=a;k>f&&k<q&&l>B&&l<m&&(d.insideCanvas=!0);return d},highlightPoint:function(a,b,d,k,l,m){var f=this,B=f.config,q=f.components,v=f.graphics,r=q.paper,S=v.tracker,P=(q=q.dataset[l])&&q.config;l=q&&P.zoomedRadius||0;var e=q&&P.hoverCosmetics,q=e&&e.fill,P=e&&e.borderColor,e=e&&e.borderThickness,n={},n=function(a){O.plotEventHandler.call(this,
f,a)},H=function(a){O.plotEventHandler.call(this,f,a,"dataplotRollover")},x=function(a){O.plotEventHandler.call(this,f,a,"dataplotRollout")};S||(S=v.tracker=r.circle(0,0,0,v.trackerGroup).attr({"clip-rect":B.canvasLeft+","+B.canvasTop+","+B.canvasWidth+","+B.canvasHeight}).click(n).trackTooltip(!0).hover(H,x));k&&S.data("eventArgs",{x:k.x,y:k.y,tooltip:k.tooltip,link:k.link});B.lastHoveredPoint=k;n=Number(a)?{r:l,fill:q,stroke:P,"stroke-width":e}:{r:l,fill:Aa,stroke:Aa,"stroke-width":0};S.attr(n).tooltip(m).transform("t"+
(b+B.canvasLeft)+","+(d+B.canvasTop));k&&f.fireMouseEvent("mouseover",S&&S.node,B.lastMouseEvent)},fireMouseEvent:function(a,b,d){var k;b&&a&&(d||(d={}),d.originalEvent&&(d=d.originalEvent),d.touches&&(d=d.touches[0]),b.dispatchEvent?(Pa?k=new Pa(a,{bubbles:!!d.bubbles,cancelable:!!d.cancelable,clientX:d.clientX||d.pageX&&d.pageX-ea.body.scrollLeft-ea.documentElement.scrollLeft||0,clientY:d.clientY||d.pageY&&d.pageY-ea.body.scrollTop-ea.documentElement.scrollTop||0,screenX:d.screenX||0,screenY:d.screenY||
0,pageX:d.pageX||0,pageY:d.pageY||0}):ea.createEvent&&(k=ea.createEvent("HTMLEvents"),k.initEvent(a,!!d.bubbles,!!d.cancelable)),k.eventName=a,k&&b.dispatchEvent(k)):ea.createEventObject&&b.fireEvent&&(k=ea.createEventObject(),k.eventType=a,k.eventName=a,b.fireEvent("on"+a,k)))},configure:function(){var a,b=this.jsonData.chart||{},d=this.components.colorManager,k=d.getColor("canvasBorderColor"),l;b.animation=0;b.showvalues=0;la.msline.configure.call(this);l=this.config;a=l.style;na(l,{useRoundEdges:x(b.useroundedges,
0),animation:!1,zoomType:"x",canvasPadding:x(b.canvaspadding,0),scrollColor:Sa(R(b.scrollcolor,d.getColor("altHGridColor"))),scrollShowButtons:!!x(b.scrollshowbuttons,1),scrollHeight:x(b.scrollheight,16)||16,scrollBarFlat:x(b.flatscrollbars,0),allowPinMode:x(b.allowpinmode,1),skipOverlapPoints:x(b.skipoverlappoints,1),showToolBarButtonTooltext:x(b.showtoolbarbuttontooltext,1),btnResetChartTooltext:R(b.btnresetcharttooltext,"Reset Chart"),btnZoomOutTooltext:R(b.btnzoomouttooltext,"Zoom out one level"),
btnSwitchToZoomModeTooltext:R(b.btnswitchtozoommodetooltext,"<strong>Switch to Zoom Mode</strong><br/>Select a subset of data to zoom into it for detailed view"),btnSwitchToPinModeTooltext:R(b.btnswitchtopinmodetooltext,"<strong>Switch to Pin Mode</strong><br/>Select a subset of data and compare with the rest of the view"),pinPaneFill:Ia(R(b.pinpanebgcolor,k),x(b.pinpanebgalpha,15)),zoomPaneFill:Ia(R(b.zoompanebgcolor,"#b9d5f1"),x(b.zoompanebgalpha,30)),zoomPaneStroke:Ia(R(b.zoompanebordercolor,"#3399ff"),
x(b.zoompaneborderalpha,80)),showPeakData:x(b.showpeakdata,0),maxPeakDataLimit:x(b.maxpeakdatalimit,b.maxpeaklimit,null),minPeakDataLimit:x(b.minpeakdatalimit,b.minpeaklimit,null),crossline:{enabled:x(b.showcrossline,1),line:{"stroke-width":x(b.crosslinethickness,1),stroke:Sa(R(b.crosslinecolor,"#000000")),"stroke-opacity":x(b.crosslinealpha,20)/100},labelEnabled:x(b.showcrosslinelabel,b.showcrossline,1),labelstyle:{fontSize:wa(b.crosslinelabelsize)?wa(b.crosslinelabelsize)+"px":a.outCanfontSize,
fontFamily:R(b.crosslinelabelfont,a.outCanfontFamily)},valueEnabled:x(b.showcrosslinevalues,b.showcrossline,1),valuestyle:{fontSize:wa(b.crosslinevaluesize)?wa(b.crosslinevaluesize)+"px":a.inCanfontSize,fontFamily:R(b.crosslinevaluefont,a.inCanvasStyle.fontFamily)}},useCrossline:x(b.usecrossline,1),tooltipSepChar:R(b.tooltipsepchar,", "),showTerminalValidData:x(b.showterminalvaliddata,0),cdmchar:R(b.dataseparator,"|"),cdm:x(b.compactdatamode,0)})},getValuePixel:function(a){var b=this.config.viewPortConfig;
return b.ddsi+ra(a/b.ppp)},__toolbar:function(){var a,b,d,k,l=this,m=l.components,f=m.tb=new (FusionCharts.register("component",["toolbox","toolbox"])),B=f.getDefaultConfiguration(),q,v;f.init({iAPI:{chart:l},graphics:l.graphics,chart:l,components:m});a=m.toolBoxAPI||f.getAPIInstances(f.ALIGNMENT_HORIZONTAL);b=a.SymbolStore;d=a.ComponentGroup;k=a.Toolbar;q=a.Symbol;v=a.Scroller;f.graphics={};return{reInit:function(){f.init({iAPI:{chart:l},graphics:l.graphics,chart:l,components:m})},addSymbol:function(a,
b,d,e){a=new q(a);d&&e.setConfiguaration({buttons:na(na({},B),d)});b.tooltext=d.tooltip;b&&a.attachEventHandlers(b);e.addSymbol(a);return a},addScroll:function(a,b){var d=new v(a);b&&d.attachEventHandlers(b);return d},addComponentGroup:function(a,b){var f;f=new d;f.setConfiguaration({group:{fill:b?b.fill:Ia("EBEBEB",0),borderThickness:b?x(b.borderThickness,0):0}});return f},addToolBox:function(a){var b,d=new k;for(b=0;b<a.length;b+=1)d.addComponent(a[b]);return d},setDrawingArea:function(a,b){a.drawingArea=
b;return a},draw:function(a){var b,d,e;for(b=0;b<a.length;b+=1)d=a[b],e=d.drawingArea,d.draw(e.x,e.y)},registerSymbol:function(a,d){b.register(a,d)},getLogicalSpace:function(a){return a.getLogicalSpace()},getNode:function(a){return a.node}}},__preDraw:function(){var a,b,d,k,l,m,f,B,q,v,r=this,S=r.components,P=S.paper,e=r.graphics;b=e.imageContainer;var n=r.config,H=n.canvasLeft,R=n.canvasWidth;a=r.jsonData.chart;var L=n.cdm;d=S.xAxis[0];var N=n.viewPortConfig,c=r.components.canvas.config,D=ja(c.canvasPadding,
c.canvasPaddingLeft,c.canvasPaddingRight);v=S.yAxis[0];B=e.datasetGroup;var c=n.canvasHeight,h=n.canvasTop,O=r.jsonData.chart,O=n.borderWidth||(n.borderWidth=x(O.showborder,1)?x(O.borderthickness,1):0),p=n.allowPinMode,g=n.crossline,N=d.getCategoryLen(),K=xa(x(a.displaystartindex,1),10)-1,U=xa(x(a.displayendindex,N||2),10)-1,fa=0,J=S.dataset,s=J.length,S=e.crossline;n.updateAnimDuration=500;b.transform("t"+H+","+h);b.attr({"clip-rect":H+","+h+","+R+","+c});n.status="zoom";n.maxZoomLimit=x(a.maxzoomlimit,
1E3);n.viewPortHistory=[];1>(b=x(a.pixelsperpoint,15))&&(b=1);(d=x(a.pixelsperlabel,a.xaxisminlabelwidth,d.getAxisData("labels").rotation?20:60))<b&&(d=b);(0>K||K>=(N-1||1))&&(K=0);(U<=K||U>(N-1||1))&&(U=N-1||1);N=n.viewPortConfig=na(n.viewPortConfig,{amrd:x(a.anchorminrenderdistance,20),nvl:x(a.numvisiblelabels,0),cdm:L,oppp:b,oppl:d,dsi:K,dei:U,vdl:U-K,clen:N,offset:0,step:1,llen:0,alen:0,ddsi:K,ddei:U,ppc:0});if(N.clen){for(;s--;)a=J[s].config,fa=ja(fa,a.drawAnchors&&(a.anchorRadius||0)+(Number(a.anchorBorderThickness)||
0)||0);n.overFlowingMarkerWidth=fa;D=n.canvasPadding=ja(fa,D);n._prezoomed=N.dei-N.dsi<N.clen-1;k=n._visw=n.canvasWidth-2*D;l=n._visx=n.canvasLeft+D;n._visout=-(n.height+c+1E3);n._ypvr=v&&v.getPVR()||0;a=n._yminValue=v.getLimit().min;m=n._ymin=v.getPixel(a);v=B.attr("clip-rect",[l-fa,h,k+2*fa,c]);e.scroll||(e.scroll=P.group("scroll").insertAfter(e.datasetGroup));p&&(B=oa.crispBound(0,h-m,0,c,O),f=n["clip-pinrect"]=[B.x,h,B.width,B.height],q=(e.zoompin=P.group("zoompin")).insertBefore(v).transform(n._pingrouptransform=
["T",l,m]).hide(),e.pinrect=P.rect(0,h-m,k,c,q).attr({"stroke-width":0,stroke:"none",fill:n.pinPaneFill,"shape-rendering":"crisp",ishot:!0}),e.pintracker=P.rect(e.trackerGroup).attr({transform:q.transform(),x:0,y:h-m,width:0,height:c,stroke:"none",fill:Aa,ishot:!0,cursor:oa.svg&&"ew-resize"||"e-resize"}).hide().drag(function(a){var b=l+a+this.__pindragdelta,c=this.__pinboundleft,d=this.__pinboundright,f=this.data("cliprect").slice(0);b<c?b=c:b>d&&(b=d);q.transform(["T",b,m]);e.pintracker.transform(q.transform());
oa.svg||(f[0]=f[0]+b-l-this.__pindragdelta,q.attr("clip-rect",f));this.__pindragoffset=a},function(){this.__pinboundleft=0-f[0]+l+H;this.__pinboundright=this.__pinboundleft+k-f[2];this.data("cliprect",q.attr("clip-rect"));q._.clipispath=!0},function(){q._.clipispath=!1;this.__pindragdelta=this.__pindragoffset;delete this.__pindragoffset;delete this.__pinboundleft;delete this.__pinboundright}));O++;B=oa.crispBound(H-O,h+c+O,R+O+O,n.scrollHeight,O);O--;ka(r,{attr:{stroke:n.zoomPaneStroke,fill:n.zoomPaneFill,
strokeWidth:0},selectionStart:function(){},selectionEnd:function(a){var b=a.selectionLeft-H;a=b+a.selectionWidth;e.crossline&&e.crossline.hide();r[n.viewPortConfig.pinned?"pinRangePixels":"zoomRangePixels"](b,a)}});g&&0!==g.enabled&&1===n.useCrossline?(S||(S=e.crossline=new pa),S.configure(r,g)):S&&S.hide()}},resetZoom:function(){var a=this.config.viewPortHistory,b=a[0];if(!a.length)return!1;a.length=0;this.zoomTo(b.dsi,b.dei,b)&&ya.raiseEvent("zoomReset",this._zoomargs,this.chartInstance,[this.chartInstance.id]);
return!0},eiMethods:{zoomOut:function(){var a;if(a=this.apiInstance)return a.zoomOut&&a.zoomOut()},zoomTo:function(a,b){var d;if(d=this.apiInstance)return d.zoomRange&&d.zoomRange(a,b)},resetChart:function(){var a;if(a=this.apiInstance)a.pinRangePixels&&a.pinRangePixels(),a.resetZoom&&a.resetZoom()},setZoomMode:function(a){var b;(b=this.apiInstance)&&b.activatePin&&b.activatePin(!a)},getViewStartIndex:function(){var a;if(this.apiInstance&&(a=this.apiInstance.config.viewPortConfig))return a.ddsi},
getViewEndIndex:function(){var a,b;if(this.apiInstance&&(a=this.apiInstance.config.viewPortConfig))return b=a.ddei-1,(b>=a.clen?a.clen:b)-1}},zoomOut:function(){var a,b,d=this.config;b=d.viewPortHistory;var k,l,m;a=b.pop();b=b[0]||d.viewPortConfig;a?(k=a.dsi,l=a.dei):d._prezoomed&&(k=0,l=b.clen-1);(m=this.zoomTo(k,l,a))&&ya.raiseEvent("zoomedout",m,this.chartInstance);return!0},zoomRangePixels:function(a,b){var d=this.config,k=d.viewPortHistory,d=d.viewPortConfig,l=d.ppp,m=d.ddsi,f;k.push(d);(f=this.zoomTo(m+
ra(a/l),m+ra(b/l)))?ya.raiseEvent("zoomedin",f,this.chartInstance):k.pop()},zoomRange:function(a,b){var d,k,l=this.config,m=l.viewPortConfig;k=this.components.xAxis[0];var f=l.viewPortHistory,B;f.push(m);d=k.getPixel(a);k=k.getPixel(b);m.x=d;m.scaleX=l.canvasWidth/(d-k);(B=this.zoomTo(+a,+b))?ya.raiseEvent("zoomedin",B,this.chartInstance):f.pop()},zoomTo:function(a,b,d){var k,l;k=this.config;var m=this.components,f=k.viewPortConfig,B=k.canvasHeight;l=k.canvasLeft;var q=k.canvasTop,v=k.canvasBottom,
r=k.viewPortHistory,S=f.clen,x=this.components.xAxis[0];0>a&&(a=0);a>=S-1&&(a=S-1);b<=a&&(b=a+1);b>S-1&&(b=S-1);if(a===b||a===f.dsi&&b===f.dei)return!1;this.pinRangePixels();f=na({},f);f.dsi=a;f.dei=b;f=k.viewPortConfig=f;d?this.updateVisual(d.x,d.y,d.scaleX,d.scaleY):(d=x.getPixel(a),k=x.getPixel(b),l=this.getOriginalPositions(d-l,q,k-l,v-q),this.zoomSelection(l[0],0,l[2],B));m.scrollBar.node.attr({"scroll-ratio":f.vdl/(S-!!S),"scroll-position":[f.dsi/(S-f.vdl-1),!0]});m={level:r.length+1,startIndex:a,
startLabel:x.getLabel(a).oriLabel,endIndex:b,endLabel:x.getLabel(b).oriLabel};ya.raiseEvent("zoomed",m,this.chartInstance,[this.chartInstance.id,a,b,m.startLabel,m.endLabel,m.level]);return m},activatePin:function(a){var b=this.config.viewPortConfig,d=this.components.tb.graphics.pinButton;if(b.pinned^(a=!!a))return a||this.pinRangePixels(),ya.raiseEvent("zoomModeChanged",{pinModeActive:a},this.chartInstance,[]),this.updateButtonVisual(d.node,a?"pressed":"enable"),b.pinned=a},updateButtonVisual:function(a,
b){return a.attr({disable:{config:{hover:{fill:"#FFFFFF","stroke-width":1,stroke:"#E3E3E3",cursor:"default"},normal:{fill:"#FFFFFF",stroke:"#E3E3E3","stroke-width":1,cursor:"default"},disable:{fill:"#FFFFFF","stroke-width":1,stroke:"#E3E3E3","stroke-opacity":1,cursor:"default"},pressed:{fill:"#FFFFFF","stroke-width":1,stroke:"#E3E3E3",cursor:"default"}},"button-disabled":!1,stroke:"#E3E3E3","stroke-opacity":1},enable:{config:{hover:{fill:"#FFFFFF","stroke-width":1,stroke:"#aaaaaa",cursor:"pointer"},
normal:{fill:"#FFFFFF",stroke:"#C2C2C2","stroke-width":1,cursor:"pointer"},disable:{fill:"#FFFFFF","stroke-width":1,stroke:"#E3E3E3","stroke-opacity":1,cursor:"pointer"},pressed:{fill:"#EFEFEF","stroke-width":1,stroke:"#C2C2C2",cursor:"pointer"}},"button-disabled":!1,fill:["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF",!0],stroke:"#C2C2C2","stroke-opacity":1},pressed:{config:{pressed:{fill:["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF",!0]}},fill:["#FFFFFF","#FFFFFF","#FFFFFF","#FFFFFF",!0],stroke:"#E3E3E3"}}[b])},
pinRangePixels:function(a,b){var d,k=this.components,l=k.paper,m=this.graphics,f=this.config,B=f.canvasLeft,q=f.viewPortConfig,v=m.zoompin;d=m.pinrect;var r=f["clip-pinrect"],x=f._pingrouptransform,k=k.dataset,P=b-a,e,n,H,m=m.pintracker;if(q&&v&&d){if(a===b)return v.hide(),m.hide(),q.pinned=!1;for(H=k.length;H--;)e=k[H],d=e.graphics,n=d.pinline,n||(n=d.pinline=l.path(v)),n.attr({path:d.lineElement.attrs.path,transform:["T",-f._visx,-f._ymin]}).attr(e.config.pin);r[0]=a+B;r[2]=P;v.attr({"clip-rect":r,
transform:x}).show();m.__pindragdelta=0;m.show().attr({transform:x,x:a,width:P});this.getValuePixel(a);this.getValuePixel(b);return q.pinned=!0}},_createLayers:function(){var a,b=this.components.paper;la.scatter._createLayers.call(this);a=this.graphics;a.imageContainer=b.group("dataset-orphan",a.dataSetGroup);this.__preDraw();this.toogleDragPan(!0)},getValue:function(a){var b=this.config,d=this.components,k=b.viewPortConfig;a=this.getOriginalPositions(a.x,a.y,a.x,a.y);var l=d.xAxis[0].config.axisRange,
d=d.yAxis[0].config.axisRange,m=l.min,f=d.max;return{x:m+(a[0]-b.canvasLeft)/(b.canvasWidth*k.scaleX/(l.max-m)),y:f-(a[1]-b.canvasTop)/(b.canvasHeight*k.scaleY/(f-d.min))}},getOriginalPositions:function(a,b,d,k){var l=this.config,m=l.viewPortConfig,f=m.scaleX,B=m.scaleY,q=m.x,m=m.y,v=sa(a,d);a=ja(a,d);d=sa(b,k);b=ja(b,k);a=a>l.canvasWidth?l.canvasWidth:a;b=b>l.canvasHeight?l.canvasHeight:b;v=0>v?0:v;d=0>d?0:d;return[q+v/f,m+d/B,(a-v)/f,(b-d)/B]},zoomSelection:function(a,b,d,k){var l=this.config;d&&
k&&(d=Math.abs(l.canvasWidth/d),k=Math.abs(l.canvasHeight/k),this.updateVisual(a,b,d,k))},updateVisual:function(a,b,d,k,l){var m=this.config,f=m.viewPortConfig,B=m.canvasWidth,q=m.canvasHeight,v=m.viewPortHistory.slice(-1)[0]||f,m=m.maxZoomLimit;f.x=isNaN(a)?a=v.x:a;f.y=isNaN(b)?b=v.y:b;f.scaleX=d||(d=v.scaleX);f.scaleY=k||(k=v.scaleY);d>m&&(f.x=sa(a,B-B/m),f.scaleX=m);k>m&&(f.y=sa(b,q-q/m),f.scaleY=m);this.updateManager(l)},toogleDragPan:function(a){var b=this.config.viewPortConfig,d=b.status;a&&
(b.status="zoom"===d?"pan":"zoom")},resize:function(){var a=this.config,b=this.graphics,d=this.components.canvas,k=d.graphics,l=k.canvasBorderElement,k=k.canvasElement,d=d.config.canvasBorderThickness,m=d/2,f=a.canvasHeight-=d,B=a.canvasWidth-=2*d,q=a.canvasLeft+=d;a.canvasBottom-=d;a.canvasRight-=d;k?k.attr({x:q,y:a.canvasTop,height:f,width:B}):this.drawCanvas();l&&l.attr({x:q-m,y:a.canvasTop-m,height:f+d,width:B+d,"stroke-width":d});b.imageContainer.attr({"clip-rect":a.canvasLeft+","+a.canvasTop+
","+a.canvasWidth+","+a.canvasHeight}).transform("t"+a.canvasLeft+","+a.canvasTop);b.trackerElem.attr({x:a.canvasLeft,y:a.canvasTop,width:a.canvasWidth,height:a.canvasHeight});b.tracker&&b.tracker.attr({"clip-rect":a.canvasLeft+","+a.canvasTop+","+a.canvasWidth+","+a.canvasHeight})},updateManager:function(a){var b,d=this.components,k=d.dataset,l=k.length,m=this.config,f=m.viewPortConfig,B=m._ypvr,q=m._visw,v=this.components.xAxis[0],r=function(){return v.getPixel.apply(v,arguments)},x=v.getAxisData("labels").style,
P,e,n,H,O=this.updateButtonVisual,L=d.tb.graphics,N=L.zoomOutButton,L=L.resetButton,c=m.viewPortHistory;!f&&(f=m.viewPortConfig);P=f.oppp;b=n=f.nvl;e=f.vdl=f.dei-f.dsi;n=f.ppl=b?q/b:f.oppl;q=f.step=(H=f.ppp=q/e)<P?qa(P/H):1;x=f.lskip=qa(ja(n,wa(x.lineHeight))/H/q);void 0!==a?(n=(f.clen-e-1)*a,f.offset=(n-(n=xa(n)))*H,e=n+e):(n=f.dsi,e=f.dei,f.offset=0);H=f.norm=n%q;f.ddsi=n-=H;f.ddei=e=e+2*q-H;f.pvr=B;f._ymin=m._ymin;f._yminValue=m._yminValue;f.x=(r(n)-r(v.getLimit().min)+f.offset)/f.scaleX;e-n>v.getCategoryLen()?
f.scaleX=1:f.scaleX=v.getCategoryLen()/Math.abs(e-n-q-.9);void 0!==a&&d.scrollBar.node.attr({"scroll-position":a});a=v._getVisibleConfig();a=Math.ceil((a.maxValue-a.minValue+1)/b);v.setLabelConfig({step:b?a:x*q});v.setAxisData({labelStep:b?a:x*q});v.setAxisConfig({animateAxis:!1});v.draw();v.setAxisConfig({animateAxis:!0});for(b=0;b<l;b+=1)k[b].draw();O(N.node,f.vdl===f.clen-1?"disable":"enable");O(L.node,0<c.length?"enable":"disable");fa.FC_DEV_ENVIRONMENT&&fa.jQuery&&(FusionCharts[" "].enable()?
(this.debug=this.debug||(fa.jQuery("#fc-zoominfo").length||fa.jQuery("body").append('<pre id="fc-zoominfo">'),fa.jQuery("#fc-zoominfo").css({position:"absolute",left:"10px",top:"0","pointer-events":"none",opacity:.7,width:"250px",zIndex:"999",border:"1px solid #cccccc","box-shadow":"1px 1px 3px #cccccc",background:"#ffffff"})),this.debug.text(JSON.stringify(f,0,2))):(this.debug&&fa.jQuery("#fc-zoominfo").remove(),delete this.debug))},_drawDataset:function(){la.zoomline.updateManager.call(this)},getParsedLabel:function(a){var b=
this.xlabels;return b.parsed[a]||(b.parsed[a]=za(b.data[a]||""))},_createToolBox:function(){var a,b,d,k,l,m,f,B=this,q=B.config;f=q.allowPinMode;k=B.components;var v=q.showToolBarButtonTooltext;a=k.chartMenuBar;a&&a.drawn||(la.scrollcolumn2d._createToolBox.call(B),a=k.tb,b=a.graphics||(a.graphics={}),d=k.toolBoxAPI||a.getAPIInstances(a.ALIGNMENT_HORIZONTAL),d=d.Symbol,k=k.chartMenuBar.componentGroups[0],l=b.zoomOutButton=(new d("zoomOutIcon",void 0,a.idCount++,a.pId)).attachEventHandlers({click:function(){B.zoomOut()},
tooltext:v&&q.btnZoomOutTooltext||""}),m=b.resetButton=(new d("resetIcon",void 0,a.idCount++,a.pId)).attachEventHandlers({click:function(){B.resetZoom()},tooltext:v&&q.btnResetChartTooltext||""}),f&&(f=b.pinButton=(new d("pinModeIcon",void 0,a.idCount++,a.pId)).attachEventHandlers({click:function(){B.activatePin(!q.viewPortConfig.pinned)},tooltext:v&&q.btnSwitchToPinModeTooltext||""}),k.addSymbol(f,!0)),k.addSymbol(m,!0),k.addSymbol(l,!0))},_scrollBar:la.scrollcolumn2d,_manageScrollerPosition:la.scrollcolumn2d,
draw:function(){var a,b,d,k,l,m,f,B,q,v,r=this,x=r.config,P=r.graphics||(r.graphics={});m=r.components;a=r.jsonData;k=a.dataset;var e=a.categories&&a.categories[0].category;la.msline.draw.call(r);f=x.canvasLeft;B=x.canvasTop;q=x.canvasHeight;v=x.canvasWidth;a=x.borderWidth;b=x.useRoundEdges;d=x.viewPortConfig;k&&e&&(a++,k=oa.crispBound(f-a,B+q+a,v+a+a,x.scrollHeight,a),a--,m=(l=m.scrollBar)&&l.node,l.draw(k.x+(b&&-1||a%2),k.y-(b&&4||2),{isHorizontal:!0,width:k.width-(!b&&2||0),height:k.height,showButtons:x.scrollShowButtons,
scrollRatio:d.vdl/(d.clen-!!d.clen),scrollPosition:[d.dsi/(d.clen-d.vdl-1),!1],r:b&&2||0,parentLayer:P.parentGroup}),!m&&function(){var a;oa.eve.on("raphael.scroll.start."+l.node.id,function(b){a=b;r.crossline&&r.crossline.disable(!0);ya.raiseEvent("scrollstart",{scrollPosition:b},r.chartInstance)});oa.eve.on("raphael.scroll.end."+l.node.id,function(b){r.crossline&&r.crossline.disable(!1);ya.raiseEvent("scrollend",{prevScrollPosition:a,scrollPosition:b},r.chartInstance)})}())}},la.msline,{showValues:0});
la("zoomlinedy",{isDual:!0,standaloneInit:!0,defaultDatasetType:"zoomline",applicableDSList:{zoomline:!0},creditLabel:!1,friendlyName:"Zoomable and Panable Multi-series Dual-axis Line Chart",_spaceManager:la.msdybasecartesian._spaceManager,_setAxisLimits:la.msdybasecartesian._setAxisLimits,_createAxes:la.msdybasecartesian._createAxes,_feedAxesRawData:la.msdybasecartesian._feedAxesRawData},la.zoomline);FusionCharts.register("component",["dataset","zoomline",{_setConfigure:function(){var a=this.config,
b=this.chart.jsonData.chart,d=this.JSONData;a.drawAnchors=x(b.drawanchors,b.showanchors,1);a.anchorRadius=x(d.anchorradius,b.anchorradius,a.lineThickness+2);this.__base__._setConfigure.apply(this,arguments)},configure:function(){var a,b,d={};a=this.chart.jsonData.chart;a.animation=0;a.showvalues=x(a.showvalues,0);this.__base__.configure.call(this);b=this.config;a=b.lineThickness+x(a.pinlinethicknessdelta,1);d["stroke-width"]=0<a&&a||0;d["stroke-dasharray"]=[3,2];d.stroke=O.hashify(b.lineColor);d["stroke-opacity"]=
b.lineAlpha/100;d["stroke-linejoin"]=b["stroke-linejoin"]="round";d["stroke-linecap"]=b["stroke-linecap"]="round";b.pin=d;b.animation=!1;b.transposeanimduration=0},draw:function(){var a,b,d=!1,k=!1,l=this,m=l.JSONData,f=l.chart,B=f.components,q=l.config,v=l.index||l.positionIndex,r=f.config,S=f.jsonData.chart,P=l.components,e=P.data,n=e.length,H,ba=B.paper,L=B.xAxis[0],N=l.yAxis,c,D,h=f.graphics,fa=h.datalabelsGroup,p=O.parseUnsafeString,g=O.getValidValue,K,ea,ka,J,s,u,t,A,Ua,ta,Wa=r.style,lb={fontFamily:Wa.fontFamily,
fontSize:Wa.fontSize,lineHeight:Wa.lineHeight,fontWeight:Wa.fontWeight,fontStyle:Wa.fontStyle,color:Wa.color},y=q.lineThickness,ha=l.graphics.container,G=l.graphics.trackerContainer,la=function(a){Ka.call(this,f,a)},ma=function(a,b){return function(c){b&&l._hoverPlotAnchor(a,"DataPlotRollOver",S);Ka.call(this,f,c,"DataPlotRollOver")}},pa=function(a,b){return function(c){b&&l._hoverPlotAnchor(a,"DataPlotRollOut",S);Ka.call(this,f,c,"DataPlotRollOut")}},na=r.viewPortConfig,Z=q.showTooltip,I,w=h.datasetGroup,
C,z=q.shadow,T,Y=l.graphics.dataLabelContainer,E={},Q,F,M=f.is3D,qa=q.use3dlineshift,V,$,da,ra=N.getAxisBase(),X=N.yBasePos=N.getAxisPosition(ra),ga=L.getAxisPosition(0),ca=L.getAxisPosition(1)-ga,W,ia=M?10:0,oa=M&&qa?10:0,aa=[ja(0,r.canvasLeft-ia),ja(0,r.canvasTop-oa),ja(1,r.canvasWidth+2*ia),ja(1,r.canvasHeight+oa)],ua=[ja(0,r.canvasLeft-ia),ja(0,r.canvasTop-oa),1,ja(1,r.canvasHeight+2*oa)],va=L.axisData&&L.axisData.scroll||{},ya=f.hasScroll||!1,za,wa=q.lineDashStyle,xa={color:q.lineColor,alpha:q.lineAlpha};
[Ea(xa),wa].join(":");var Ca,Da,Ga,Ha=l.graphics.lineElement,Va=l.visible,Ja,Ia,Fa=l.pool||(l.pool={element:[]}),Pa={},Oa={},Sa={},Ta=q.anchorRadius,fb,ab=[],gb,Ba,Xa,Ya,bb,cb,mb=r.showTerminalValidData,db=r.viewPortConfig,nb=r.showPeakData,hb=r.maxPeakDataLimit,ib=r.minPeakDataLimit,ob=x(r.useCrossline,0),Ma=db.step,eb=L.getPixel(db.step)-ga<db.amrd,jb=function(a,b){var d=a.graphics;F=a.config;s=F.setValue;J=F.setLink;Ja=F.x||b;$=g(p(R(F.setLevelTooltext,m.plottooltext,S.plottooltext)));V=F.showValue;
E=F.anchorProps;T=E.shadow;t=F.displayValue;Ia=F.dip||0;a||(a=e[b]={graphics:{}});Ga={color:F.color,alpha:F.alpha};da=F.dashStyle;c=F.xPos||L.getAxisPosition(Ja)-ia;D=l.visible?N.getAxisPosition(s)+oa:X;C=F.hoverEffects;E.isAnchorHoverRadius=C.anchorRadius;fb=L.getLabel(b);K=ob?"":F.toolText+($?"":F.toolTipValue);u={index:b,link:J,value:s,displayValue:t,categoryLabel:fb,toolText:K,id:q.userID,datasetIndex:v,datasetName:m.seriesname,visible:Va};null===F.setValue||eb||(E.imageUrl?(Q=new U,Q.onload=
l._onAnchorImageLoad(l,b,u,c,D),Q.onerror=l._onErrorSetter(c,D,b,l),Q.src=E.imageUrl):(ea=d.element,ea||(ea=Fa.element&&Fa.element.length?d.element=Fa.element.shift():d.element=ba.polypath(ha.anchorGroup)),ea.attr({polypath:[E.symbol[1]||2,c,D,E.radius,E.startAngle,Ia],fill:Ea({color:E.bgColor,alpha:E.bgAlpha}),stroke:Ea({color:E.borderColor,alpha:E.borderAlpha}),"stroke-width":E.borderThickness,visibility:E.radius?Va:"hidden"}).shadow(T,ha.anchorShadowGroup).data("anchorRadius",E.radius).data("anchorHoverRadius",
C.anchorRadius).data("setRolloverAttr",ta).data("setRolloutAttr",Ua),ea[F.setValue?"show":"hide"]()),C.enabled&&(ta={polypath:[C.anchorSides||2,c,D,C.anchorRadius,C.startAngle,C.dip],fill:Ea({color:C.anchorColor,alpha:C.anchorBgAlpha}),stroke:Ea({color:C.anchorBorderColor,alpha:C.anchorBorderAlpha}),"stroke-width":C.anchorBorderThickness},Ua={polypath:[E.sides,c,D,E.radius,E.startAngle,Ia],fill:Ea({color:E.bgColor,alpha:E.bgAlpha}),stroke:Ea({color:E.borderColor,alpha:E.borderAlpha}),"stroke-width":E.borderThickness},
ea&&ea.data("anchorRadius",E.radius).data("anchorHoverRadius",C.anchorRadius).data("setRolloverAttr",ta).data("setRolloutAttr",Ua)),Ta=ja(E.radius,C&&C.anchorRadius||0),I={cx:c,cy:D,r:Ta,cursor:J?"pointer":"",stroke:Aa,"stroke-width":E.borderThickness,fill:Aa,ishot:!0,visibility:Va},E.imageUrl||!J&&!Z||(ka=d.hotElement,ka||(ka=Fa.hotElement&&Fa.hotElement.length?d.hotElement=Fa.hotElement.shift():d.hotElement=ba.circle(G)),ka.show().attr(I),(ka||ea).data("eventArgs",u).data("groupId",void 0).click(la).tooltip(K),
C.enabled&&(ka||ea).hover(ma(a,C.enabled),pa(a,C.enabled))));a._xPos=c;a._yPos=D;[Ea(Ga||xa),da||wa].join(":");Sa=l.getLinePath([a],Sa);Ca=Ea(Ga||xa);Da=da||wa;R(F.setColor,F.setAlpha,F.setDashed);[Ca,Da].join(":");V&&!E.imageUrl&&l.drawLabel(b);ab.push(a)},pb=function(a,b){var c=a&&a.length,d=a.slice().sort(function(a,b){return a.config.setValue-b.config.setValue}),e=d&&d.pop().config.setValue,f=d.length&&d.shift().config.setValue||e,d=0;if(e>hb||f<ib)for(;d<c;){ea=a[d];e=ea.config.setValue;if(e>
hb||e<ib)e=b+d,jb(ea,e);d+=1}},Za=function(b,c){--b;c+=1;var d;for(H=b;H<c;H+=1)for(d in a=e[H]&&e[H].graphics||{},e[H]&&(e[H].config.isRemoving=!0),a)Fa[d]||(Fa[d]=[]),a[d]&&(Fa[d].push(a[d].hide()),a[d]=void 0)},$a=na.ddsi||0,Na=na.ddei||n,Qa=q._oldStartIndex,Ra=q._oldEndIndex,qb=q._oldStep,kb=P.removeDataArr,rb=kb&&kb.length;w.line=w.line||ba.group("line",w);w.lineConnector=w.lineConnector||ba.group("line-connector",w);ha||(ha=l.graphics.container={lineShadowGroup:ba.group("connector-shadow",w.line),
anchorShadowGroup:ba.group("anchor-shadow",w.lineConnector),lineGroup:ba.group("line",w.line),anchorGroup:ba.group("anchors",w.lineConnector)},Va||(ha.lineShadowGroup.hide(),ha.anchorShadowGroup.hide(),ha.lineGroup.hide(),ha.anchorGroup.hide()));G||(G=l.graphics.trackerContainer=ba.group("line-hot",G).toBack(),Va||G.hide());e||(e=l.components.data=[]);f._addCSSDefinition(".fusioncharts-datalabels .fusioncharts-label",lb);Y||(Y=l.graphics.dataLabelContainer=l.graphics.dataLabelContainer||ba.group("datalabel",
fa),Va||Y.hide());W=ca*n;eb&&!q._oldHideAnchors?Za(Qa,Ra):Ma!==qb?Za(Qa,Ra):($a>Qa&&Za(Qa,$a>Ra?Ra:$a),Na<Ra&&Za(Na<Qa?Qa:Na,Ra),($a<Qa||Na>Ra)&&Za(Qa,Ra));q._oldHideAnchors=eb;q._oldEndIndex=Na;q._oldStep=Ma;l.setVisibility(Va);for(H=q._oldStartIndex=$a;H<=Na;H+=Ma){A=e[H]||{};F=A.config||{};F.isRemoving=!1;s=F.setValue||null;Xa=H;if(mb)if(0===H&&null===s){gb=0;for(Ba=b=H;Ba<n;)if(null!==e[Ba].config.setValue||d?d=!0:Ba++,null===e[b].config.setValue&&!k&&b<=n?(b+=Ma,gb++):k=!0,d&&k){d=k=!1;break}0!==
Ba%Ma&&(F=e[Ba].config,Xa=Ba)}else if(H>=n&&null===s){for(Ba=b=H;0<Ba&&(void 0!==e[Ba]||d?d=!0:Ba--,void 0===e[b]&&!k&&0<=b?b-=Ma:k=!0,!d||!k););0!==Ba%Ma&&(F=e[Ba].config,Xa=Ba)}if(A=e[Xa])jb(A,Xa),nb&&1<Ma&&(Ya=sa(H+1,Na),cb=sa(Ya+Ma,Na),bb=cb===Na?e.slice(Ya):e.slice(Ya,cb),bb.length&&pb(bb,Ya))}Oa=l.getLinePath(ab,{});Pa=l.getLinePath(ab,Pa);q.lastPath=Oa;Ha||(Ha=Fa.lineElement&&Fa.lineElement.length?l.graphics.lineElement=Fa.lineElement.shift():l.graphics.lineElement=ba.path(ha.lineGroup));Ha.attr({path:Oa.getPathArr(),
"stroke-dasharray":wa,"stroke-width":y,stroke:Ea(xa),"stroke-linecap":"round","stroke-linejoin":2<=y?"round":"miter"}).shadow(z,ha.lineShadowGroup);ya&&(za=va.startPercent,aa[2]=W+ua[0],1===za&&(ua[0]=aa[2],aa[0]=0));aa[3]+=oa;l.drawn=!0;rb&&l.remove()},setVisibility:function(a,b){var d=this.graphics&&this.graphics.container,k=this.graphics&&this.graphics.trackerContainer,l=this.graphics&&this.graphics.dataLabelContainer,m=a?"show":"hide";d.lineGroup[m]();d.anchorGroup[m]();d.anchorShadowGroup[m]();
d.lineShadowGroup[m]();k[m]();l[m]();b&&this.transposeLimits(a)},transposeLimits:function(a){var b=this.chart,d=this.yAxis;b._chartAnimation();this.visible=a;this._conatinerHidden=!a;b._setAxisLimits();d.draw();b._drawDataset()},hide:function(){this.setVisibility(!1,!0)},show:function(){this.setVisibility(!0,!0)}},"Line"]);pa=function(){};pa.prototype.configure=function(a,b){var d,k,l,m=this,f=a.components,B=f.numberFormatter,q=f.paper,v=a.config;d=a.graphics;k=this.left=v._visx;l=this.top=v.canvasTop;
var r=this.height=v.canvasHeight,x=this._visout=v._visout,P=this.plots=a.components.dataset,e=d.datalabelsGroup,n,H,O=b.labelstyle,L=b.valuestyle,N=f.yAxis[0],c=N.getLimit(),D=f.yAxis[1],h=D&&D.getLimit();H=this.tracker;var f=this.labels,R=this.positionLabel;n=a.get("linkedItems");var p=n.container,g=n.eventListeners||(n.eventListeners=[]);m.width=v._visw;n=this.group;n||(n=this.group=q.group("crossline-labels",e),this.container=p);n.attr({transform:["T",k,v._ymin]});H||(H=m.tracker=p,g.push(Ga(p,
"touchstart mousemove",function(b){var c=m.onMouseMove,d=m.onMouseOut;a.isWithinCanvas(b,a).insideCanvas?c.call(m,b):d.call(m,b)},m)),g.push(Ga(p,"mousedown",function(){m.onMouseDown()},m)),g.push(Ga(p,"mouseup",function(){m.onMouseUp()},m)),g.push(Ga(p,"mouseout",function(){m.onMouseOut()},m)));H=this.line;H||(H=this.line=q.path(e).toBack());H.attr(na({path:["M",k,l,"l",0,r]},b.line));f||(f=this.labels=b.valueEnabled&&q.set());b.labelEnabled&&(R||(R=this.positionLabel=q.text("").insertAfter(d.datalabelsGroup)),
R.attr({x:x,y:l+r+(v.scrollHeight||0)+2.5,"vertical-align":"top",direction:v.textDirection,text:""}).css(O));this.hide();this.ppixelRatio=-N.getPVR();this.spixelRatio=D&&-D.getPVR();this.yminValue=v._yminValue;this.pyaxisminvalue=c.min;this.pyaxismaxvalue=c.max;this.syaxisminvalue=h&&h.min;this.syaxismaxvalue=h&&h.max;this.positionLabels=v.xlabels||{data:[],parsed:[]};this.chart=a;this.getZoomInfo=function(){return v.viewPortConfig};this.getDataIndexFromPixel=function(b){return Math.round(a.components.xAxis[0].getValue(b))};
this.getPositionLabel=function(b){return a.components.xAxis[0].getLabel(b).oriLabel};if(b.valueEnabled){d=0;for(k=P.length;d<k;d+=1)l=P[d],l=Ja(l.config.lineColor),f[d]||(f[d]=f.items[d]=q.text(n)),f[d].attr({x:0,y:x,text:"",fill:l,direction:v.textDirection}).css(L);for(;d<f.items.length;d+=1)f[d].remove(),delete f[d],f.items.splice(d,1);this.numberFormatter=B}else if(f.items.length){for(d=0;d<f.items.length;d+=1)f[d].remove(),delete f[d];f.length=0}};pa.prototype.disable=function(a){void 0!==a&&
(this.disabled=!!a)&&this.visible&&this.hide();return this.disabled};pa.prototype.onMouseOut=function(){this.hide();this.position=void 0};pa.prototype.onMouseDown=function(){!Ca&&this.hide();this._mouseIsDown=!0};pa.prototype.onMouseUp=function(){!Ca&&this.hide();delete this._mouseIsDown};pa.prototype.onMouseMove=function(a){if(!(this.disabled||this._mouseIsDown&&!Ca)){var b,d=this.getZoomInfo(),k=this.line,l=this.left,d=d.step,m=this.chart,f=m.components.xAxis[0],B=f.getCategoryLen(),q=m.get("config"),
m=q.canvasLeft,q=q.canvasRight;a=Ha(this.container,a).chartX-l;var l=f.getLimit().min,v=f.config.axisData.axisDimention.x-m,r;r=(r=this.getDataIndexFromPixel(Oa(a)))+((b=r%d)>d/2?d-b:-b);a=f.getPixel(r)-v-m;a<=q-m&&(k.transform(["T",Oa(a),0]),this.hidden&&this.show(),(r>=B||r<l)&&this.hide(),r!==this.position||this.hidden)&&(this.position=r,this.lineX=a,this.updateLabels())}};pa.prototype.updateLabels=function(){var a=this,b=a.labels,d=a.plots,k=a.width,l=a.position,m=a.lineX,f=ra(m),B=a.ppixelRatio,
q=a.spixelRatio,v=a.yminValue,r=a._visout,x=a.numberFormatter,P=a.pyaxisminvalue,e=a.pyaxismaxvalue,n=a.syaxisminvalue,H=a.syaxismaxvalue,O=function(){function b(){this.y=0;this.lRef=void 0;this.__index=this.__shift=0}function d(a){for(var b=0;b<a;)this.push(b++);return this}function c(a){var b,c,d,e,f=Number.POSITIVE_INFINITY;for(b=0;b<this.length;b++)c=this[b]-a,0>c?d=s.NEG:d=s.POS,c=q(c),c<=f&&(f=c,e={absValue:c,noScaleSide:d});return e}function e(a){this.holes=d.call([],a)}var f=-1*a.height,k=
v*B,l=0,g,m={},n,q=Math.abs,x=Math.floor,s={};"function"!=typeof Object.create&&(Object.create=function(){function a(){}var b=Object.prototype.hasOwnProperty;return function(c){var d,e,f;if("object"!=typeof c)throw new TypeError("Object prototype may only be an Object or null");a.prototype=c;f=new a;a.prototype=null;if(1<arguments.length)for(e in d=Object(arguments[1]),d)b.call(d,e)&&(f[e]=d[e]);return f}}());Array.prototype.indexOf||(Array.prototype.indexOf=function(a,b){var c,d,e;if(null==this)throw new TypeError('"this" is null or not defined');
d=Object(this);e=d.length>>>0;if(0===e)return-1;c=+b||0;Infinity===Math.abs(c)&&(c=0);if(c>=e)return-1;for(c=Math.max(0<=c?c:e-Math.abs(c),0);c<e;){if(c in d&&d[c]===a)return c;c++}return-1});Array.prototype.forEach||(Array.prototype.forEach=function(a,b){var c,d,e,f,g;if(null==this)throw new TypeError(" this is null or not defined");e=Object(this);f=e.length>>>0;if("function"!==typeof a)throw new TypeError(a+" is not a function");1<arguments.length&&(c=b);for(d=0;d<f;)d in e&&(g=e[d],a.call(c,g,
d,e)),d++});b.prototype.constructor=b;b.prototype.applyShift=function(a){this.__shift=a;this.lRef.calcY=this.y+=a*l};b.prototype.applyDirectIndex=function(a){this.__index=a;this.lRef.calcY=this.y=f-a*l*-1};try{Object.defineProperty(s,"POS",{enumerable:!1,configurable:!1,get:function(){return 1}}),Object.defineProperty(s,"NEG",{enumerable:!1,configurable:!1,get:function(){return-1}})}catch(u){s.POS=1,s.NEG=-1}e.prototype=Object.create(Array.prototype);e.prototype.constructor=e;e.prototype.repositionHoles=
function(){var a,b=0,c;for(a=this.holes.length=0;a<this.length;a++)c=this[a],!c&&(this.holes[b++]=a)};e.prototype.attachShift=function(a,d,e){var f,g=this.length;if(a===r)e.calcY=r;else if(g=d>g-1?g-1:d,f=this[g],d=new b,d.y=a,d.lRef=e,f){a=c.call(this.holes,g);e=g+a.absValue*a.noScaleSide;if(a.noScaleSide===s.POS)return d.applyDirectIndex(e),this.splice(e,1,d),this.holes.splice(this.holes.indexOf(e),1),e;if(a.noScaleSide===s.NEG){a=this.splice(e+1,this.length-1);this.pop();a.forEach(function(a){a&&
a.applyShift(-1)});for([].push.apply(this,a);this[e];)e++;this.push(0);this.repositionHoles();a=c.call(this.holes,e);e+=a.absValue*a.noScaleSide;d.applyDirectIndex(e);this.splice(e,1,d);this.repositionHoles();return this.length-1}}else d.applyDirectIndex(g),this.splice(g,1,d),this.holes.splice(this.holes.indexOf(g),1)};try{Object.defineProperty(m,"top",{enumerable:!1,configurable:!1,get:function(){return f}}),Object.defineProperty(m,"bottom",{enumerable:!1,configurable:!1,get:function(){return k}})}catch(t){m.top=
f,m.bottom=k}m.init=function(a,b){var c;l=a+2;f+=l/2;n=x(q(f)/l);g=new e(n);for(c=0;c<n;c++)g.push(0)};m.occupy=function(a,b){var c=x(q(f-a)/l);g.attachShift(a,c,b)};return m}();b&&(b[0].attr({text:x.yAxis("0")}),O.init(b[0].getBBox().height,b.length),b.forEach(function(a,b){var c=d[b],f=c.components.data[l]&&c.components.data[l].config.setValue,h=c.config.parentYAxis;O.occupy(void 0===f||!c.visible||(h?f>H||f<n:f>e||f<P)?r:h?(f-n)*q:(f-P)*B,a)}));b&&b.forEach(function(a,b){var c=d[b];(c=x[c.config.parentYAxis?
"sYAxis":"yAxis"](c.components.data[l]&&c.components.data[l].config.setValue))?(a.attr({text:c}),c=a.getBBox(),c=c.width,c=.5*c+10,a.attr({x:ja(0,sa(f,k)),y:a.calcY,"text-anchor":m<=c&&"start"||m+c>=k&&"end"||"middle","text-bound":["rgba(255,255,255,0.8)","rgba(0,0,0,0.2)",1,2.5]})):a.attr({x:-k})});a.positionLabel&&a.positionLabel.attr({x:m+a.left,text:a.getPositionLabel(l),"text-bound":["rgba(255,255,255,1)","rgba(0,0,0,1)",1,2.5]})};pa.prototype.show=function(){this.disabled||(this.hidden=!1,this.group.attr("visibility",
"visible"),this.line.attr("visibility","visible"),this.positionLabel&&this.positionLabel.attr("visibility","visible"))};pa.prototype.hide=function(){this.hidden=!0;this.group.attr("visibility","hidden");this.line.attr("visibility","hidden");this.positionLabel&&this.positionLabel.attr("visibility","hidden")};pa.prototype.dispose=function(){for(var a in this)this.hasOwnProperty(a)&&delete this[a]};oa.addSymbol({pinModeIcon:function(a,b,d){var k=.5*d,l=a-d,m=a+d,f=a-k,B=a+k,q=a+.5,v=q+1,r=q+1.5,x=b-
d,P=b+k,e=b-k,k=b+(d-k);return["M",l,x,"L",f,e,f,k,l,P,a-.5,P,a,b+d+.5,q,P,m,P,B,k,B,e,m,x,r,x,r,e,r,k,v,k,v,e,r,e,r,x,"Z"]},zoomOutIcon:function(a,b,d){a-=.2*d;b-=.2*d;var k=.8*d,l=oa.rad(43),m=oa.rad(48),f=a+k*ua(l),l=b+k*va(l),x=a+k*ua(m),m=b+k*va(m),q=oa.rad(45),v=f+d*ua(q),r=l+d*va(q),O=x+d*ua(q);d=m+d*va(q);return["M",f,l,"A",k,k,0,1,0,x,m,"Z","M",f+1,l+1,"L",v,r,O,d,x+1,m+1,"Z","M",a-2,b,"L",a+2,b,"Z"]},resetIcon:function(a,b,d){var k=a-d,l=(ma.PI/2+ma.PI)/2;a+=d*ua(l);var l=b+d*va(l),m=2*
d/3;return["M",k,b,"A",d,d,0,1,1,a,l,"L",a+m,l-1,a+2,l+m-.5,a,l]}})}]);
