﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>IdleReport</title>
    <style>


           @@media only screen and (min-width: 1280px) 
              {
            #IdDivModelViewIdleReport {
                height:500px;
            }
        } 
              
              @@media only screen and (max-width: 1275px) {
            #IdDivModelViewIdleReport {
                height:500px;
            }
        } 
         /*.ui-jqgrid tr.jqgrow td {
            word-wrap: break-word; 
            white-space: pre-wrap;
           
            white-space: normal !important;
            height: auto;
            vertical-align: text-top;
            padding-top: 2px;
            padding-bottom: 3px;
        }
        .ui-jqgrid .ui-jqgrid-htable th div {
            height: auto;
            overflow: hidden;
            padding-right: 4px;
            padding-top: 2px;
            position: relative;
            vertical-align: text-top;
            white-space: normal !important;
        }

        .ui-jqgrid tr.jqgrow td {
            word-wrap: break-word; 
            white-space: pre-wrap;
            white-space: normal !important;
            height: auto;
            vertical-align: text-top;
            padding-top: 2px;
            padding-bottom: 3px;
        }*/
    </style>
</head>
<body>
   <div style="width: inherit; overflow: auto;">
    <table id="IdTblIdleReportGrid"></table>
    <div id="IdTblIdleReportPager"></div>
</div>
    @*//=====================================================================================================//*@

     <div class="modal fade " id="IdDivModelViewIdleReport" style="position: absolute !important;overflow-y: scroll;">
                    <div class="modal-dialog modal-lg" style="width: 97%">
                        <!-- Modal content-->
                        <div class="modal-content">
                            <div class="modal-header">
                                @*<span style="margin: 0px; line-height: 33px;">Idle Report Details</span>*@
                                <button type="button" class="close closeIdleRepor">&times;</button>
                                <button type="button" class="Colsemodal" style="display:none" data-dismiss="modal"></button>
                                <h4 class="modal-title">IdleReport Details</h4>
                            </div>
                            <div id="IDDivCustModalBody" class="modal-body">
                                <div id="IdDivCustHeaderPart">
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectCode" class="ClsLabelIdleReportTxtBox">Project Code:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">                                           
                                                <input id="IdTxtProjectCode" type="text" class="form-control IdleReportValidation titlevalue" readonly="readonly" >                                        
                                        </div>
                                         <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectNameNew" class="ClsLabelIdleReportTxtBox">Project Name:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">                                           
                                                <input id="IdTxtProjectNameNew" type="text" class="form-control IdleReportValidation titlevalue" readonly="readonly" >                                        
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtProjectName" class="ClsLabelIdleReportTxtBox">Plant Type:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control IdleReportValidation titlevalue" id="IdTxtProjectName" readonly />
                                        </div>                              
                                    </div>
                                    <br />
                                    <div class="row">
                                             <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtClient" class="ClsLabelIdleReportTxtBox">Client:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control IdleReportValidation titlevalue" id="IdTxtClient" readonly />
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtState" class="ClsLabelIdleReportTxtBox">State:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control IdleReportValidation titlevalue" id="IdTxtState" readonly />
                                        </div>

                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtLocation" class="ClsLabelIdleReportTxtBox">Location:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control IdleReportValidation titlevalue" id="IdTxtLocation" readonly />
                                        </div>

                                    </div>
                                    <br />
                                    <div class="row">
                                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <label for="IdTxtStatus" class="ClsLabelIdleReportTxtBox titlevalue">Status:</label>
                                        </div>
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12">
                                            <input class="form-control IdleReportValidation titlevalue" id="IdTxtStatus"  readonly />
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="background-color: #8bcaca; color:#1f716c; width: 97%; margin-left: 13px; font-weight:bolder">
                                            Idle Report Details
                                        </div>
                                    </div>
                                    <br />
                                    <div class="row">
                                        <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12"></div>
                                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-12">
                                            <div style="width: inherit; overflow: auto;" id="IdDivIdleReportHistoryDetails">
                                                <table id='IdTblIdleReportHistoryGrid'></table>
                                                <div id='IdTblIdleReportHistoryPager'></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    @*<button type="button" class="btn ButtonStyle" id="IdbtnSaveIdleReport">Save</button>*@
                                    <button type="button" class="btn ButtonStyle closeIdleRepor" id="btn_cancelIdleReport"  >Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

</body>
</html>
<script>

    if ('@ViewBag.IdleReportProjectCode' == '0')
    {       
        var ProjectId = '@ViewBag.IdleReportProjectCode';
        $("#IdToAppend").html("");
        //function NewButton() {
        //    $("#IdToAppend").html("");
        //    $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        //    // $("#IdToAppend").append("<Button id='IdBtnNewIdleReport' class='AddNew'>New</Button>")
        //}
        //NewButton();
        $(document).on('click', '#IdBtnNewIdleReport', function () {
            $("#IdDivModelViewIdleReport").modal({ backdrop: false });
        })
        newbuttonValue = 1;
    }

    if ('@ViewBag.IdleReportProjectCode' != '0')
    {
        newbuttonValue = 0;
         var ProjectId = '@ViewBag.IdleReportProjectCode';
    }

    function LeftMenuInactive() {
        for (var i = 0; i < $("#IdLeftMenu li").length; i++) {
            $($("#IdLeftMenu li ")[i]).removeClass("menuActive");
        }
    }
    $(".titlevalue ").mouseenter(function () {
        var word =$(this).val();

        $(".titlevalue ").attr('title', word);
    });

    //============================================= IdleReport ==========================================//

    function IdleReportLeftMenu() {
        $("#IdToAppend").html("");
        $("#IdToAppend").css("padding", "0px").css("margin-top", "10px");
        $("#IdToAppend").append("<ul id='IdLeftMenu'></ul>")
        $("#IdLeftMenu").css("margin-left", "-30px");

        $("#IdLeftMenu").append("<li id='IdIdleReportIdleReport'>Idle Report</li>")
        $("#IdLeftMenu").append("<li id='IdIdleReportProject'>Project</li>")
        $("#IdLeftMenu").append("<li id='IdIdleReportCheckSheet'>Progress Sheet</li>")
        $("#IdLeftMenu").append("<li id='IdIdleReportInstallationProtocalls'>Installation Protocols</li>")
        $("#IdLeftMenu").append("<li id='IdIdleReportHydraAvailability'>Hydra Availability</li>")
        $("#IdLeftMenu").append("<li id='IdIdleReportCommissioning'>Commissioning</li>")
      //  $("#IdLeftMenu").append("<li id='IdIdleReportShortSupplies'>Short Supplies</li>")
    };

    $(document).on('click', '#IdIdleReportIdleReport', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/IdleReport?ProjectId=" + 0));
        newbuttonValue = 1;
    })

    $(document).on('click', '#IdIdleReportProject', function ()
    {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Projects?ProjectId=" + ProjectId));
    })

    $(document).on('click', '#IdIdleReportCheckSheet', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/CheckSheets?ProjectId=" + ProjectId));
    })

    $(document).on('click', '#IdIdleReportInstallationProtocalls', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/InstallationProtocalls?ProjectId=" + ProjectId));
    })

    $(document).on('click', '#IdIdleReportHydraAvailability', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/HydraAvailability?ProjectId=" + ProjectId));
    })
    $(document).on('click', '#IdIdleReportCommissioning', function () {
        $("#IdDivModelViewCust").modal("hide");
        LeftMenuInactive()
        $(this).addClass("menuActive")
        $(".MainContainerBody").html("");
        $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + ProjectId));
    })

    //$(document).on('click', '#IdIdleReportShortSupplies', function () {
    //    $("#IdDivModelViewCust").modal("hide");
    //    LeftMenuInactive()
    //    $(this).addClass("menuActive")
    //    $(".MainContainerBody").html("");
    //    $(".MainContainerBody").load(AbsolutePath("/Home/Commissioning?ProjectId=" + 1));
    //})


    //==============================================================================================================//
    function EditIdleReportDetails(Id)
    {
        if ('@ViewBag.IdleReportProjectCode' == '0')
        {
            IdleReportLeftMenu();
        }
        //var PDID = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(2).text()
        ////alert(PDID)
        //ProjectId = PDID
        //var jj=1
        //var ProjectCode = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(3).text()
        //var Client = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(4).text()
        //var State = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(5).text()
        //var Location = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(6).text()
        //var ProjectType = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(7).text()

        //var Status = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(8).text()
        //var Created = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(9).text()
        //var Completed = $("#IdTblIdleReportGrid tr").eq(Id).children().eq(10).text()
        //    $("#IdDivModelViewIdleReport").modal({ backdrop: false });
        //    $("#IdDivModelViewHydraAvailability").modal({ backdrop: false });
        //    $("#IdTxtProjectCode").val(ProjectCode)
        //    $("#IdTxtProjectName").val(ProjectType)
        //    $("#IdTxtClient").val(Client)
        //    $("#IdTxtState").val(State)
        //    $("#IdTxtLocation").val(Location)
        //    $("#IdTxtStatus").val(Status)
        //    GetIdleReportHistorydetails(PDID);
        //    idleReportnew(PDID)
    };
    $(document).on('click', ".editclassIr", function () {
        debugger
        // $(this).parent().next().next().css("border","solid red 2px")

        var PDID = $(this).parent().next().text()
        ProjectId = PDID
        $("#IdDivModelViewIdleReport").modal({ backdrop: false });
        $("#IdDivModelViewHydraAvailability").modal({ backdrop: false });

        var ProjectCode = $(this).parent().next().next().text()
        var ProjectName = $(this).parent().next().next().next().next().text()
        var Client = $(this).parent().next().next().next().text()
        var State = $(this).parent().next().next().next().next().text()
        var Location = $(this).parent().next().next().next().next().next().text()
        var ProjectType = $(this).parent().next().next().next().next().next().next().text()

        var Status = $(this).parent().next().next().next().next().next().next().next().text()
        var Created = $(this).parent().next().next().next().next().next().next().next().next().text()
        var Completed = $(this).parent().next().next().next().next().next().next().next().next().next().text()
        var Pname = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().text()
        

        //var Employename = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().text()
        //var Avaiable = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().text()
        //var notAvailiable = $(this).parent().next().next().next().next().next().next().next().next().next().next().next().next().next().text()
        $("#IdTxtProjectCode").val(ProjectCode)
        $("#IdTxtProjectName").val(ProjectType)
        $("#IdTxtClient").val(Client)
        $("#IdTxtState").val(State)
        $("#IdTxtLocation").val(Location)
        $("#IdTxtStatus").val(Status)
        $("#IdTxtProjectNameNew").val(Pname)
        //$("#IdTxtAvailable").val(Avaiable)
        //$("#IdTxtNotAvailable").val(notAvailiable)
        GetIdleReportHistorydetails(PDID);
        idleReportnew(PDID)
    })

    //======================================================================================================//

    function IdleReportGrid(ProjectId)
    {
        $("#IdTblIdleReportGrid").GridUnload();
 //       var LoadStaticDataMainGrid = [
 //{ "ProjectDetailsId": "1", "Project_code": "C.005867 & C.005869", "Site_Client_Name": "Mr. K K Singh", "State_Name": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "NW-Rapid", "ProjectStatusName": "Completed", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
 //{ "ProjectDetailsId": "2", "Project_code": "C.005868 & C.005900", "Site_Client_Name": "Mr. Praveen", "State_Name": "Karnataka", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "Modular", "ProjectStatusName": "InProgress", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
 //{ "ProjectDetailsId": "3", "Project_code": "C.005869 & C.005901", "Site_Client_Name": "Mr. Suresh", "State_Name": "Tamilnadu", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "Modular", "ProjectStatusName": "Completed", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" },
 //{ "ProjectDetailsId": "4", "Project_code": "C.005870 & C.005902", "Site_Client_Name": "Mr. Mahesh", "State_Name": "Maharashtra", "Location": "ChiKali Tarsod Highway Project, 181/3 +4, Row House No.-01, Opp-Navnath Mandir, Varangoan Road, Bhusawal-425201", "Plant_typeName": "NW-Rapid", "ProjectStatusName": "InProgress", "Created_Date": "11-09-2018", "CompletedDate": "18-09-2018" }];

        $("#IdTblIdleReportGrid").GridUnload();

        $("#IdTblIdleReportGrid").jqGrid({
            url: AbsolutePath('/IdleReports/ProjectInjfoForIdle?ProjectId=' + ProjectId),
           datatype: "json",
            mtype: "GET",
            //datatype: 'local',
            //data: LoadStaticDataMainGrid,
            caption: "Idle Report Details",
            width: "1002",
            height: 'auto',
            viewrecords: true,
            rowList: [5, 10, 15, 20],
            rownumbers: true,
            rownumWidth: 50,
            sortname: "ProjectDetailsId",
            sortorder: "asc",
            pager: "#IdTblIdleReportPager",
            rowNum: 20,
            //colNames: ["Select", "IC", "Name", "City", "RegionIC", "Region", "StateIC", "State", "Phone", "Email", "CountryIC", "ZipCode", "Address", "Code"],
            colModel: [
                          //{
                          //    name: 'Edit', width: 60, align: 'center', formatter: function view(a, b) {
                          //        return "<span class='glyphicon glyphicon-pencil ClsViewEnquiryDetails' id='editImg_" + b.rowId + "'></span>";
                          //    }, search: false,
                          //},

                          {
                              name: "View", align: "center", width: 80, resizable: true, sortable: false, search: false, searchable: false, formatter: function (a, b) {
                                  return "<i class='glyphicon glyphicon-file editclassIr'  title='Click Here For View' onClick='EditIdleReportDetails(" + b.rowId + ")' ></i>"
                              }
                          },
                          { name: "ProjectDetailsId", index: "ProjectDetailsId", label: "ProjectDetailsId", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                          { name: "Project_code", index: "Project_code", label: "Project Code", width: "125", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                          { name: "Site_Client_Name", index: "Site_Client_Name", label: "Client", width: "120", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                          { name: "State_Name", index: "State_Name", label: "State", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                          { name: "Location", index: "Location", label: "Location", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                          { name: "Plant_typeName", index: "Plant_typeName", label: "Project Type", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true },
                          { name: "ProjectStatusName", index: "ProjectStatusName", label: "Status", width: "150", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false },
                          { name: "Created_Date", index: "Created_Date", label: "Created Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
                          { name: "IdleReportSubmittedDate", index: "IdleReportSubmittedDate", label: "Completed Date", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: false, formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' } },
                           { name: "HydraAvailabilityStatusName", index: "HydraAvailabilityStatusName", label: "HydraAvailabilityStatusName", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
                  { name: "Company_Employee_Name", index: "Company_Employee_Name", label: "Company_Employee_Name", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true },
            { name: "ProjectName", index: "Project Name", label: "Company_Employee_Name", width: "100", align: "left", resizable: true, sortable: true, search: true, searchable: true, hidden: true }
            ],
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow", 'hidden');
                $(".ui-jqgrid-bdiv").css("overflow-y", 'auto');
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                console.log(data)
            },
        });
        var $grid = $('#IdTblIdleReportGrid');
       // $grid.jqGrid('setCaption', 'Idle Report Details');
        $("#IdTblIdleReportGrid").filterToolbar({
            resetIcon: "",
            defaultSearch: "cn",
            stringResult: true,
        });
        $("#IdTblIdleReportGrid").navGrid("#IdTblIdleReportPager", { search: false, edit: false, add: false, del: false, refresh: false });
        $("#IdTblIdleReportGrid").navButtonAdd("#IdTblIdleReportPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblIdleReportGrid").GridUnload();
                IdleReportGrid(ProjectId)
            }
        });
        $("#IdTblIdleReportGrid").navButtonAdd("#IdTblIdleReportPager", {
            title: 'Export',
            caption: "",
            buttonicon: 'ui-icon-bookmark',
            onClickButton: function () {

                window.location.href = AbsolutePath("/IdleReports/ExportToexcel");
            }
        });
    }
    IdleReportGrid('@ViewBag.IdleReportProjectCode');


    //======================================================================================================//

    function GetIdleReportHistorydetails(ProjectId) {

        $("#IdDivIdleReportHistoryDetails").html("");
        $("#IdDivIdleReportHistoryDetails").append("<table id='IdTblIdleReportHistoryGrid'></table>")
        $("#IdDivHydraAvailabilityHistoryDetails").append("<div id='IdTblIdleReportHistoryPager'></div >")
        $("#IdDivIdleReportHistoryDetails").GridUnload();


        var LoadIdleReportData = [
        {
            "IdelReport_date": "11-09-2018", "Available_MetsoEngineer": "3", "Available_Supervisor": "1", "Available_Fitter": "2", "Available_Welder": "3", "Available_Electrician": "2", "Available_Helper": "5", "Available_TotalCount": "16",
            "Idle_MetsoEngineer": "3", "Idle_Supervisor": "1", "Idle_Fitter": "2", "Idle_Welder": "3", "Idle_Electrician": "2", "Idle_Helper": "5", "Idle_TotalCount": "16", "IdleReport_IdleReasonCategoryName": "Weather", "Remars": ""
        },
        ]
        $("#IdTblIdleReportHistoryGrid").jqGrid({
            url: AbsolutePath('/IdleReports/IdleReasonDetails?Pid=' + ProjectId),
            datatype: "json",
             mtype: "GET",
            //datatype: 'local',
            //data: LoadIdleReportData,
            sortname: "IdleReportDetailsIC",
            sortorder: "asc",
            //caption:"Idle Report Details",
            width: "1500",
            height: '100',
            viewrecords: true,
            rowList: [5, 10, 15, 20],
            rownumbers: true,
            rownumWidth: 50,
            pager: "#IdTblIdleReportHistoryPager",
            rowNum: 10,
            //colNames: ["Select", "IC", "Name", "City", "RegionIC", "Region", "StateIC", "State", "Phone", "Email", "CountryIC", "ZipCode", "Address", "Code"],
            colModel: [
                //{
                //    label: 'Edit',
                //    name: "edit",
                //    width: 30,
                //    search: false,
                //    align: "center",
                //    formatter: function (a, b) {
                //        return ("<i class='glyphicon glyphicon-pencil' data-title='title' title='Click here to edit' onclick='EditFollwupDetails(" + b.rowId + ")' id='serch_" + b.rowId + "'></i>")
                //    }
                //},
                 {
                     label: 'IdleReportDetailsIC',
                     name: "IdleReportDetailsIC",
                     width: 50,
                     align: 'right',
                     editable: true,
                     hidden:true
                 },

                {
                    label: 'Date',
                    name: "IdelReport_date",
                    width: 50,
                    align: 'right',
                    editable: true,
                    formatter: "date", formatoptions: { srcformat: 'd/m/Y', newformat: 'd-M-Y' }
                },

                {
                    label: 'Metso Engineer',
                    name: "Available_MetsoEngineer",
                    width: 70,
                    align: 'right',
                    editable: true,
                },
                {
                    label: 'Cont/Supervisor',
                    name: "Available_Supervisor",
                    width: 80,
                    align: 'right',
                    editable: true,

                },
                {
                    label: 'Fitter',
                    name: "Available_Fitter",
                    width: 50,
                    align: 'right',
                    editable: true,

                },
                {
                    label: 'Welder',
                    name: "Available_Welder",
                    width: 50,
                    align: 'right',
                    editable: true,
                },
                {
                    label: 'Electrician',
                    name: "Available_Electrician",
                    width: 50,
                    align: 'right',
                    editable: true,
                },
                {
                    label: 'Helper',
                    name: "Available_Helper",
                    width: 50,
                    align: 'right',
                    editable: true
                },
                {
                    label: 'Total',
                    name: "Available_TotalCount",
                    width: 50,
                    align: 'right',
                    editable: true
                },

                {
                    label: 'Metso Engineer',
                    name: "Idle_MetsoEngineer",
                    width: 70,
                    align: 'right',
                    editable: true,
                },
                {
                    label: 'Cont/Supervisor',
                    name: "Idle_Supervisor",
                    width: 80,
                    align: 'right',
                    editable: true,

                },
                {
                    label: 'Fitter',
                    name: "Idle_Fitter",
                    width: 50,
                    align: 'right',
                    editable: true,

                },
                {
                    label: 'Welder',
                    name: "Idle_Welder",
                    width: 50,
                    align: 'right',
                    editable: true,
                },
                {
                    label: 'Electrician',
                    name: "Idle_Electrician",
                    width: 50,
                    align: 'right',
                    editable: true,
                },
                {
                    label: 'Helper',
                    name: "Idle_Helper",
                    width: 50,
                    align: 'right',
                    editable: true
                },
                {
                    label: 'Total',
                    name: "Idle_TotalCount",
                    width: 50,
                    align: 'right',
                    editable: true
                },
                {
                    label: 'Category',
                    name: "IdleReport_IdleReasonCategoryName",
                    width: 50,
                    editable: true
                },
                {
                    label: 'Details',
                    name: "Remars",
                    width: 50,
                    editable: true
                },
            ],
            //sortname: "",
            //sortorder: "asc",
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function () {
                $(".ui-jqgrid-bdiv").css("overflow", 'hidden');
                $(".ui-jqgrid-bdiv").css("overflow-y", 'auto');
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
            },
        });
        $("#IdTblIdleReportHistoryGrid").navGrid("#IdTblIdleReportHistoryPager", { add: false, del: false, edit: false, refresh: false, search: false });

        $("#IdTblIdleReportHistoryGrid").navButtonAdd("#IdTblIdleReportHistoryPager", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#IdTblIdleReportHistoryGrid").trigger('reloadGrid');
            }
        });

        //$("#IdTblIdleReportHistoryGrid").navButtonAdd("#IdTblIdleReportHistoryPager", {
        //    title: 'Add',
        //    buttonicon: "ui-icon-plus",
        //    caption: "",
        //    position: "first",
        //    onClickButton: function () {
        //        rowid = $("#IdTblIdleReportHistoryGrid").getDataIDs();
        //        //$(".ClsImgDelete").hide();

        //        if (rowid.length == 0) {
        //            var newRowId = 1
        //        }
        //        else {
        //            var newRowId = parseInt(rowid[(rowid.length - 1)]) + 1;
        //        }
        //        $("#IdTblIdleReportHistoryGrid").addRowData(newRowId, "last");
        //        $("#IdTblIdleReportHistoryGrid").editRow(newRowId);
        //    }
        //});
        //var $grid = $('#IdTblIdleReportHistoryGrid');
        //$grid.jqGrid('setCaption', 'Idle Report Details');
        $("#IdTblIdleReportHistoryGrid").filterToolbar({
            resetIcon: "",
            defaultSearch: "cn",
            stringResult: true,
        });

        $("#IdTblIdleReportHistoryGrid").jqGrid('setGroupHeaders', {
            useColSpanStyle: false,
            groupHeaders: [
              { startColumnName: 'Available_MetsoEngineer', numberOfColumns: 7, titleText: '<center>Available in Numbers</center>' },
              { startColumnName: 'Idle_MetsoEngineer', numberOfColumns: 7, titleText: '<center>Idle Time in Hours</center>' },
              { startColumnName: 'IdleReport_IdleReasonCategoryName', numberOfColumns: 2, titleText: '<center>Idle Reason</center>' }
            ]
        });

    }
    function idleReportnew(pid)
    {
        debugger
        $("#IdDivIdleReportHistoryDetails").html("");
        $("#IdDivIdleReportHistoryDetails").append("<table id='grid'></table>")
        $("#IdDivIdleReportHistoryDetails").append("<div id='pagerId'></div >")
        $("#IdDivIdleReportHistoryDetails").GridUnload();


        var data = [[48803, "DSK1", "", "02200220", "OPEN"], [48769, "APPR", "", "77733337", "ENTERED"]];

        var subgridData = [[1, "Item 1", 3], [2, "Item 2", 5]];

        var globalSubGridNames = [];
        var datetosend=""
        $("#grid").jqGrid({
           // datatype: "local",
            height: 250,
            url: AbsolutePath('/IdleReports/IdleReportDetailsforGrid?Pid=' + pid),
            datatype: "json",
          mtype: "GET",
            //datatype: 'local',
            //data: LoadIdleReportData,
             sortname: "IdleReportDetailsIC",
            sortorder: "asc",
            //caption: "Idle Report Details",
            width: "650",
            height: 'auto',
            viewrecords: true,
            rowList: [5, 10, 15, 20],
            rownumbers: true,
            rownumWidth: 50,
            colNames: ['IdleReportDetailsIC','Date','Total Available', 'Total Idle Time', 'Remarks'],
            colModel: [{
                name: 'IdleReportDetailsIC',
                index: 'IdleReportDetailsIC',
                width: 80,
                align: 'right',
                editable: true,
                hidden:true
            },
            {
                name: 'IdelReport_date',
                index: 'IdelReport_date',
                width: 50,
                align: 'center',
                editable: true,
                formatter: "date", formatoptions: { srcformat: 'm/d/Y', newformat: 'd-M-Y' }
            },
            {
                name: 'Available_TotalCount',
                index: 'Available_TotalCount',
                width: 50,
                align: 'left',
                editable: true,
            },
            {
                name: 'Idle_TotalCount',
                index: 'Idle_TotalCount',
                width: 50,
                align: 'left',
                editable: true,
            },
            {
                name: 'IdleReport_GeoLocation',
                index: 'IdleReport_GeoLocation',
                width: 80,
                align: 'right',
                editable: true,
            }
            ],
            pager: 'pagerId',
            jsonReader: {
                root: "root",
                page: "page",
                total: "total",
                records: "records",
                repeatitems: false,
            },
            loadComplete: function (data) {
                $(".ui-jqgrid-bdiv").css("overflow-x", 'hidden');
                console.log(data)
            },
            resizeStart: function (i, v) { $(".ui-jqgrid-bdiv").css("overflow-x", 'auto'); },
            //caption: "Stack Overflow Subgrid Example",
            subGrid: true,
            subGridOptions: {
                "plusicon": "ui-icon-triangle-1-e",
                "minusicon": "ui-icon-triangle-1-s",
                "openicon": "ui-icon-arrowreturn-1-e",
                "reloadOnExpand": false,
                "selectOnExpand": true
            },
            subGridRowExpanded: function (subgrid_id, row_id) {
                var IdleReportDetailsIC = $('#grid').jqGrid('getCell', row_id, 'IdleReportDetailsIC')
                var date = $('#grid').jqGrid('getCell', row_id, 'IdelReport_date')
                var month = ["", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                var datesplited = date.split('-')
                var day = datesplited[0]
                var sendday=""
                var newday=day.substring(0,1)
                //alert(day.substring(1,2))
                if (newday == "0") {
                    sendday = day.substring(1, 2)
                }
                else {
                    sendday = day
                }

                var mn = datesplited[1]
                var monthindex=0
                for (var i = 0; i < month.length; i++) {
                    if (month[i].toString().trim().toUpperCase() == mn.toString().trim().toUpperCase()) {
                        monthindex=i
                    }

                }
                var newdate = monthindex + "/" + sendday + "/" + datesplited[2]
                datetosend = newdate
                //alert(newdate)
                //alert(IdleReportDetailsIC)
              //  alert(date)
                var subgrid_table_id, pager_id; subgrid_table_id = subgrid_id + "_t";
                pager_id = "p_" + subgrid_table_id;
                $("#" + subgrid_id).html("<table id='" + subgrid_table_id + "' class='scroll'></table><div id='" + pager_id + "' class='scroll'></div>");
                $("#" + subgrid_table_id).jqGrid({
                    //datatype: "local",
                    colNames: ['IdleReportDesignationDetailerIC', 'Designation', 'Available (number)','Idle (hours)','Category'],
                    colModel: [{ name: "IdleReportDesignationDetailerIC", index: "num", width: 100,hidden:true },
                                { name: "IdleReportDesignation_Name", index: "IdleReportDesignation_Name", width: 50 },
                                { name: "Available_Count", index: "Available_Count", width: 40, align: "left" },
                    { name: "IdleTime_Count", index: "IdleTime_Count", width: 40, key: true },
                                { name: "IdleReport_IdleReasonCategoryName", index: "IdleReport_IdleReasonCategoryName", width: 50 },
                            ],
                    rowNum: 20,
                    pager: pager_id,
                    sortname: 'IdleReportDesignationDetailerIC',
                    sortorder: "asc", height: '100%',
                    width: "500",
                    url: AbsolutePath('/IdleReports/IdleReasondesignationDetailsforGrid?Pid=' + IdleReportDetailsIC + "&date=" + newdate),
                    datatype: "json",
                    mtype: "GET",
                    jsonReader: {
                        root: "root",
                        page: "page",
                        total: "total",
                        records: "records",
                        repeatitems: false,
                    },

                });
                $("#" + subgrid_table_id).jqGrid('navGrid', "#" + pager_id, { add: false, del: false, edit: false, refresh: false, search: false });
                //$("#" + subgrid_table_id).filterToolbar({
                //    resetIcon: "",
                //    defaultSearch: "cn",
                //    stringResult: true,
                //});
                $("#" + subgrid_table_id).navButtonAdd("#" + pager_id, {
                    title: 'Refresh',
                    caption: "",
                    buttonicon: 'ui-icon-refresh',
                    onClickButton: function () {
                        $("#grid").trigger('reloadGrid');

                    }
                });

                $("#" + subgrid_table_id).navButtonAdd("#" + pager_id, {
                    title: 'Refresh',
                    caption: "",
                    buttonicon: 'ui-icon-bookmark',
                    onClickButton: function () {
                        //$("#grid").trigger('reloadGrid');
                        //alert("Hi")
                        window.location.href = AbsolutePath("/IdleReports/ExportToexcelIdleDesignationDetails?ProjectId=" + ProjectId + "&IdleDetailsid=" + IdleReportDetailsIC + "&newdate=" + datetosend)
                    }
                });


                var subNames =['IdleReportDesignationDetailerIC', 'IdleReportDesignation_Name', 'Available_Count','IdleTime_Count','Category Name',];
                var mysubdata = [];
                for (var i = 0; i < subgridData.length; i++) {
                    mysubdata[i] = {};
                    for (var j = 0; j < subgridData[i].length; j++) {
                        mysubdata[i][subNames[j]] = subgridData[i][j];
                    }
                }
                for (var i = 0; i <= mysubdata.length; i++) {
                    $("#" + subgrid_table_id).jqGrid('addRowData', i + 1, mysubdata[i]);
                }
            }
        });
        $("#grid").navGrid("#pagerId", { add: false, del: false, edit: false, refresh: false, search: false });
        $("#grid").filterToolbar({
            resetIcon: "",
            defaultSearch: "cn",
            stringResult: true,
        });
        $("#grid").navButtonAdd("#pagerId", {
            title: 'Refresh',
            caption: "",
            buttonicon: 'ui-icon-refresh',
            onClickButton: function () {
                $("#grid").trigger('reloadGrid');
            }
        });
        $("#grid").navButtonAdd("#pagerId", {
            title: 'Export',
            caption: "",
            buttonicon: 'ui-icon-bookmark',
            onClickButton: function () {
                // $("#grid").trigger('reloadGrid');
               debugger
                window.location.href = AbsolutePath("/IdleReports/ExportToexcelIdleDetails?ProjectId=" + ProjectId)
            }
        });

        //var names = ["id", "thingy", "blank", "number", "status"];
        //var mydata = [];

        //for (var i = 0; i < data.length; i++) {
        //    mydata[i] = {};
        //    for (var j = 0; j < data[i].length; j++) {
        //        mydata[i][names[j]] = data[i][j];
        //    }
        //}

        //for (var i = 0; i <= mydata.length; i++) {
        //    $("#grid").jqGrid('addRowData', i + 1, mydata[i]);
        //}



        $("#grid").jqGrid('setGridParam', { ondblClickRow: function (rowid, iRow, iCol, e) {  } });



    }


 </script>