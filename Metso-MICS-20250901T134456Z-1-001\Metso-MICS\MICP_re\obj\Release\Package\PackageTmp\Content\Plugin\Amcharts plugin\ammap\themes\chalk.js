AmCharts.themes.chalk = {

	themeName: "chalk",

	Am<PERSON>hart: {
		color: "#e7e7e7",
		fontFamily: "Covered By Your Grace",
		fontSize: 18,
		handDrawn: true,
		backgroundColor: "#282828"
	},

	AmCoordinateChart: {
		colors: ["#FFFFFF", "#e384a6", "#f4d499", "#4d90d6", "#c7e38c", "#9986c8", "#edf28c", "#ffd1d4", "#5ee1dc", "#b0eead", "#fef85a", "#8badd2"]
	},

	AmSlicedChart: {
		outlineAlpha: 1,
		labelTickColor: "#FFFFFF",
		labelTickAlpha: 0.3,
		colors: ["#FFFFFF", "#e384a6", "#f4d499", "#4d90d6", "#c7e38c", "#9986c8", "#edf28c", "#ffd1d4", "#5ee1dc", "#b0eead", "#fef85a", "#8badd2"]
	},

	AmStockChart: {
		colors: ["#FFFFFF", "#e384a6", "#f4d499", "#4d90d6", "#c7e38c", "#9986c8", "#edf28c", "#ffd1d4", "#5ee1dc", "#b0eead", "#fef85a", "#8badd2"]
	},

	AmRectangularChart: {
		zoomOutButtonColor: '#FFFFFF',
		zoomOutButtonRollOverAlpha: 0.15,
		zoomOutButtonImage: "lensWhite"
	},

	AxisBase: {
		axisColor: "#FFFFFF",
		gridColor: "#FFFFFF"
	},

	ChartScrollbar: {
		backgroundColor: "#FFFFFF",
		backgroundAlpha: 0.2,
		graphFillAlpha: 0.5,
		graphLineAlpha: 0,
		selectedBackgroundColor: "#000000",
		selectedBackgroundAlpha: 0.25,
		fontSize: 15,
		gridAlpha: 0.15
	},

	ChartCursor: {
		cursorColor: "#FFFFFF",
		color: "#000000"
	},

	AmLegend: {
		color: "#e7e7e7",
		markerSize: 20
	},

	AmGraph: {
		lineAlpha: 0.8
	},


	GaugeArrow: {
		color: "#FFFFFF",
		alpha: 0.1,
		nailAlpha: 0,
		innerRadius: "40%",
		nailRadius: 15,
		startWidth: 15,
		borderAlpha: 0.8,
		nailBorderAlpha: 0
	},

	GaugeAxis: {
		tickColor: "#FFFFFF",
		tickAlpha: 0.8,
		tickLength: 15,
		minorTickLength: 8,
		axisThickness: 3,
		axisColor: '#FFFFFF',
		axisAlpha: 0.8,
		bandAlpha: 0.4
	},

	TrendLine: {
		lineColor: "#c03246",
		lineAlpha: 0.8
	},

	// ammap
	AmMap: {
		handDrawn: false
	},

	AreasSettings: {
		alpha: 0.8,
		color: "#FFFFFF",
		colorSolid: "#000000",
		unlistedAreasAlpha: 0.4,
		unlistedAreasColor: "#FFFFFF",
		outlineColor: "#000000",
		outlineAlpha: 0.5,
		outlineThickness: 0.5,
		rollOverColor: "#4d90d6",
		rollOverOutlineColor: "#000000",
		selectedOutlineColor: "#000000",
		selectedColor: "#e384a6",
		unlistedAreasOutlineColor: "#000000",
		unlistedAreasOutlineAlpha: 0.5
	},

	LinesSettings: {
		color: "#FFFFFF",
		alpha: 0.8
	},

	ImagesSettings: {
		alpha: 0.8,
		labelFontSize: 16,
		labelColor: "#FFFFFF",
		color: "#FFFFFF",
		labelRollOverColor: "#4d90d6"
	},

	ZoomControl: {
		buttonRollOverColor: "#4d90d6",
		buttonFillColor: "#e384a6",
		buttonFillAlpha: 0.8
	},

	SmallMap: {
		mapColor: "#FFFFFF",
		rectangleColor: "#FFFFFF",
		backgroundColor: "#000000",
		backgroundAlpha: 0.7,
		borderThickness: 1,
		borderAlpha: 0.8
	},


	// the defaults below are set using CSS syntax, you can use any existing css property
	// if you don't use Stock chart, you can delete lines below
	PeriodSelector: {
		fontFamily: "Covered By Your Grace",
		fontSize:"16px",
		color: "#e7e7e7"
	},

	PeriodButton: {
		fontFamily: "Covered By Your Grace",
		fontSize:"16px",
		color: "#e7e7e7",
		background: "transparent",
		opacity: 0.7,
		border: "1px solid rgba(255, 255, 255, .15)",
		MozBorderRadius: "5px",
		borderRadius: "5px",
		margin: "1px",
		outline: "none",
		boxSizing: "border-box"
	},

	PeriodButtonSelected: {
		fontFamily: "Covered By Your Grace",
		fontSize:"16px",
		color: "#e7e7e7",
		backgroundColor: "rgba(255, 255, 255, 0.1)",
		border: "1px solid rgba(255, 255, 255, .3)",
		MozBorderRadius: "5px",
		borderRadius: "5px",
		margin: "1px",
		outline: "none",
		opacity: 1,
		boxSizing: "border-box"
	},

	PeriodInputField: {
		fontFamily: "Covered By Your Grace",
		fontSize:"16px",
		color: "#e7e7e7",
		background: "transparent",
		border: "1px solid rgba(255, 255, 255, .15)",
		outline: "none"
	},

	DataSetSelector: {
		fontFamily: "Covered By Your Grace",
		fontSize:"16px",
		color: "#e7e7e7",
		selectedBackgroundColor: "rgba(255, 255, 255, .25)",
		rollOverBackgroundColor: "rgba(255, 255, 255, .15)"
	},

	DataSetCompareList: {
		fontFamily: "Covered By Your Grace",
		fontSize:"16px",
		color: "#e7e7e7",
		lineHeight: "100%",
		boxSizing: "initial",
		webkitBoxSizing: "initial",
		border: "1px solid rgba(255, 255, 255, .15)"
	},

	DataSetSelect: {
		fontFamily: "Covered By Your Grace",
		fontSize:"16px",
		border: "1px solid rgba(255, 255, 255, .15)",
		outline: "none"
	}

};