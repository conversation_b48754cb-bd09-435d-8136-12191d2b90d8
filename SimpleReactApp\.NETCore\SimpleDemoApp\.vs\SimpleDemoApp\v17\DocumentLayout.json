{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.client\\src\\app.jsx||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|solutionrelative:simpledemoapp.client\\src\\app.jsx||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.client\\src\\main.jsx||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|solutionrelative:simpledemoapp.client\\src\\main.jsx||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.server\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|solutionrelative:simpledemoapp.server\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.server\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|solutionrelative:simpledemoapp.server\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.server\\controllers\\productmastercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|solutionrelative:simpledemoapp.server\\controllers\\productmastercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.server\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|solutionrelative:simpledemoapp.server\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.client\\package.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|solutionrelative:simpledemoapp.client\\package.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.client\\src\\components\\productmaster\\productmasterscript.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|solutionrelative:simpledemoapp.client\\src\\components\\productmaster\\productmasterscript.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.client\\src\\components\\productmaster\\productmaster.jsx||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|solutionrelative:simpledemoapp.client\\src\\components\\productmaster\\productmaster.jsx||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.server\\weatherforecast.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|solutionrelative:simpledemoapp.server\\weatherforecast.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.server\\controllers\\weatherforecastcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA9F4BC5-6B2F-4A23-BF10-99FC1E12B1D3}|SimpleDemoApp.Server\\SimpleDemoApp.Server.csproj|solutionrelative:simpledemoapp.server\\controllers\\weatherforecastcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\node_modules\\react-dom\\cjs\\react-dom-client.development.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:simpledemoapp.client\\node_modules\\react-dom\\cjs\\react-dom-client.development.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}|"}, {"AbsoluteMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.client\\src\\components\\productmaster\\productmaster.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|solutionrelative:simpledemoapp.client\\src\\components\\productmaster\\productmaster.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|c:\\users\\<USER>\\downloads\\simplereactapp\\.netcore\\simpledemoapp\\simpledemoapp.client\\index.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{7F3A6DE9-C03B-B626-42F5-BD52D0B7326A}|simpledemoapp.client\\simpledemoapp.client.esproj|solutionrelative:simpledemoapp.client\\index.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Document", "DocumentIndex": 5, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Program.cs", "RelativeDocumentMoniker": "SimpleDemoApp.Server\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Program.cs", "RelativeToolTip": "SimpleDemoApp.Server\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T13:34:10.885Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\appsettings.json", "RelativeDocumentMoniker": "SimpleDemoApp.Server\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\appsettings.json", "RelativeToolTip": "SimpleDemoApp.Server\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-04T13:02:36.14Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProductMasterController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Controllers\\ProductMasterController.cs", "RelativeDocumentMoniker": "SimpleDemoApp.Server\\Controllers\\ProductMasterController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Controllers\\ProductMasterController.cs", "RelativeToolTip": "SimpleDemoApp.Server\\Controllers\\ProductMasterController.cs", "ViewState": "AgIAAD8AAAAAAAAAAAA0wBQAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T12:02:36.475Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "App.jsx", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\App.jsx", "RelativeDocumentMoniker": "simpledemoapp.client\\src\\App.jsx", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\App.jsx", "RelativeToolTip": "simpledemoapp.client\\src\\App.jsx", "ViewState": "AgIAACgAAAAAAAAAAAAAwDgAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003663|", "WhenOpened": "2025-08-04T10:44:21.477Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "main.jsx", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\main.jsx", "RelativeDocumentMoniker": "simpledemoapp.client\\src\\main.jsx", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\main.jsx", "RelativeToolTip": "simpledemoapp.client\\src\\main.jsx", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003663|", "WhenOpened": "2025-08-04T10:44:40.306Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "ProductMasterScript.js", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMasterScript.js", "RelativeDocumentMoniker": "simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMasterScript.js", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMasterScript.js", "RelativeToolTip": "simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMasterScript.js", "ViewState": "AgIAABAAAAAAAAAAAAAAACkAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-04T10:47:04.769Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "WeatherForecastController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Controllers\\WeatherForecastController.cs", "RelativeDocumentMoniker": "SimpleDemoApp.Server\\Controllers\\WeatherForecastController.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Controllers\\WeatherForecastController.cs", "RelativeToolTip": "SimpleDemoApp.Server\\Controllers\\WeatherForecastController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T14:06:46.38Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "SimpleDemoApp.Server\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\Properties\\launchSettings.json", "RelativeToolTip": "SimpleDemoApp.Server\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB0AAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-04T13:37:28.139Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "package.json", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\package.json", "RelativeDocumentMoniker": "simpledemoapp.client\\package.json", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\package.json", "RelativeToolTip": "simpledemoapp.client\\package.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-04T14:21:54.007Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "react-dom-client.development.js", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\node_modules\\react-dom\\cjs\\react-dom-client.development.js", "RelativeDocumentMoniker": "simpledemoapp.client\\node_modules\\react-dom\\cjs\\react-dom-client.development.js", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\node_modules\\react-dom\\cjs\\react-dom-client.development.js", "RelativeToolTip": "simpledemoapp.client\\node_modules\\react-dom\\cjs\\react-dom-client.development.js", "ViewState": "AgIAAOQFAAAAAAAAAAAIwPYFAABZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-08-04T11:26:45.348Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ProductMaster.css", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.css", "RelativeDocumentMoniker": "simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.css", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.css", "RelativeToolTip": "simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.css", "ViewState": "AgIAAEgAAAAAAAAAAAAAAFYAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-08-04T11:44:25.711Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "ProductMaster.jsx", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.jsx", "RelativeDocumentMoniker": "simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.jsx", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.jsx", "RelativeToolTip": "simpledemoapp.client\\src\\Components\\ProductMaster\\ProductMaster.jsx", "ViewState": "AgIAAEsAAAAAAAAAAAAAAGAAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003663|", "WhenOpened": "2025-08-04T10:45:08.322Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "WeatherForecast.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\WeatherForecast.cs", "RelativeDocumentMoniker": "SimpleDemoApp.Server\\WeatherForecast.cs", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\SimpleDemoApp.Server\\WeatherForecast.cs", "RelativeToolTip": "SimpleDemoApp.Server\\WeatherForecast.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-04T14:07:52.121Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "index.html", "DocumentMoniker": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\index.html", "RelativeDocumentMoniker": "simpledemoapp.client\\index.html", "ToolTip": "C:\\Users\\<USER>\\Downloads\\SimpleReactApp\\.NETCore\\SimpleDemoApp\\simpledemoapp.client\\index.html", "RelativeToolTip": "simpledemoapp.client\\index.html", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-08-04T11:22:12.289Z"}]}]}]}